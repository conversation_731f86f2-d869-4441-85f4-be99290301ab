<ui:UXML xmlns:ui="UnityEngine.UIElements" eui="UnityEditor.UIElements" aui="UnityEditor.U2D.Animation.SpriteLibraryEditor">
    <Style src="SpriteLibraryEditorWindow.uss"/>
    <ui:Template name="EditorTopToolbar" src="EditorTopToolbar.uxml" />
    <ui:Template name="EditorMainWindow" src="EditorMainWindow.uxml" />
    <ui:Template name="EditorBottomToolbar" src="EditorBottomToolbar.uxml" />
    <ui:VisualElement name="EditorWindowRoot" >
        <ui:Instance template="EditorTopToolbar" name="EditorTopToolbar" pickingMode="Ignore" />
        <ui:Instance template="EditorMainWindow" name="EditorMainWindow" />
        <ui:Instance template="EditorBottomToolbar" name="EditorBottomToolbar" />
    </ui:VisualElement>
</ui:UXML>
