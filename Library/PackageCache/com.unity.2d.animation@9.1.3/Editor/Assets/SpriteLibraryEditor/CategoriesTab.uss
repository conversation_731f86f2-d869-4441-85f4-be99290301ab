/* *** General *** */

#CategoriesTab {
    background-color: var(--unity-colors-default-background);
    flex-grow: 1;
}

.unity-foldout__content {
    margin-left: 0;
    flex-grow: 1;
}

.unity-list-view__empty-label {
    display: none;
}

.unity-foldout__toggle {
    background-color: var(--unity-colors-window-background);
    padding-left: 3px;
    padding-top: 3px;
    padding-bottom: 2px;
    margin: 0;
}

.unity-toggle__text {
    height: 20px;
    font-size: 12px;
    -unity-font-style: bold;
}

.sprite-library-editor-window__category-list-text {
    margin-left: 10px;
}

.sprite-library-editor-window__category-list-item {
    justify-content: center;
}

.sprite-library-editor-window__category-container {
    color: lightgray;
    -unity-font-style: bold;
}

.sprite-library-editor-window__category-label-container {
    flex-direction: row;
}

.sprite-library-editor-window__category-list-container {
    flex-grow: 1;
    flex-shrink: 1;
}

#LocalLabel {
    padding: 5px;
    background-color: var(--unity-colors-default-background);
}

#InheritedLabel {
    padding: 5px;
    background-color: var(--unity-colors-default-background);
}

#CategoryListsScrollView {
    flex-grow: 1;
}

#CategoryListsContainer {
    flex-grow: 1;
    justify-content: flex-start;
}