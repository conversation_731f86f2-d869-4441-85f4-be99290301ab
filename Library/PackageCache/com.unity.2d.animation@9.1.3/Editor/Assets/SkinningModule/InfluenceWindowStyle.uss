
InfluenceWindow
{
    height: 200px;
}


#HeaderImage
{
    background-image: resource("Builtin Skins/LightSkin/images/rol-header.png");
    -unity-slice-left: 4;
    -unity-slice-right: 4;
    -unity-slice-top: 3;
    -unity-slice-bottom: 2;

    /* GUIStyle.clipping */
    overflow : hidden;

    /* GUIStyle.contentOffset */
    /*-unity-content-offset: 0 -1;*/

    /* GUIStyle.fixedHeight */
    height: 18px;
}

.Dark . #HeaderImage
{
    background-image: resource("Builtin Skins/DarkSkin/images/d_rol-header.png");
}


#HeaderText
{
    -unity-text-align : middle-center;
}

#ListView
{
    background-image: resource("Builtin Skins/LightSkin/images/rol-body.png");
    flex: 1 auto;
    -unity-slice-left: 6;
    -unity-slice-right: 3;
    -unity-slice-bottom: 6;
    height : 130px;
    border-bottom-width : 5px;
    border-left-width : 1px;
    border-right-width : 1px;
}


.Dark . #ListView
{
    background-image: resource("Builtin Skins/DarkSkin/images/d_rol-body.png");
}

#ListRow
{
    -unity-text-align : middle-center;
}

.ListRowOddColor
{
    background-color: #DFDFDF;
}

.ListRowEvenColor
{
    background-color: #D8D8D8;
}

.Dark . .ListRowOddColor
{
    background-color: #373737;
}

.Dark .ListRowEvenColor
{
    background-color: #3C3C3C;
}

ListView . .unity-collection-view__item--selected
{
    background-image: resource("Builtin Skins/LightSkin/images/ro_selected_l.png");
    -unity-slice-left: 2;
    -unity-slice-right: 2;
    -unity-slice-bottom: 3;
}

.Dark . ListView . .unity-collection-view__item--selected
{
    background-image: resource("Builtin Skins/DarkSkin/images/ro_selected_d.png");
}

ScrollView .unity-list-view__empty-label
{
    /* we don't want any text displayed when the list is is empty */
    display: none;
}

.Dark . #ContentBackgroundImage
{
    background-image: resource("Builtin Skins/DarkSkin/images/d_rol-body.png");
}

#ButtonsContainer
{
    flex-direction : row;
    align-self : flex-end;
    background-image: resource("Builtin Skins/LightSkin/images/rol-tab.png");
    -unity-slice-left: 4;
    -unity-slice-right: 4;
    -unity-slice-top: 3;
    -unity-slice-bottom: 4;
    height: 18px;
    width : 48px;
    top : -3px;
}

.Dark . #ButtonsContainer
{
    background-image: resource("Builtin Skins/DarkSkin/images/d_rol-tab.png");
}

#AddButton
{
    background-image: resource("Icons/Toolbar Plus.png");
}

.unity-button
{
    flex : 0 0 auto;
    -unity-slice-left: 0;
    -unity-slice-right: 0;
    -unity-slice-top: 0;
    -unity-slice-bottom: 0;
    align-self: center;
    -unity-background-scale-mode : scale-to-fit;
    background-color: rgb(221, 221, 221);
    margin-top : 2px;
    margin-bottom : 2px;
    margin-left : 2px;
    margin-right : 2px;
    padding-top : 0;
    padding-bottom : 0;
    padding-left : 0;
    padding-right : 0;
    min-height : 0;
    height : 16px;
    width : 16px;
}

#AddButton:hover {
    background-color: rgb(255, 255, 255);
}

.Dark #AddButton:hover {
    /* GUIState.textColor */
    background-color: rgb(128, 128, 128);
}
.Dark . #AddButton
{
    background-image: resource("Icons/d_Toolbar Plus.png");
    background-color: rgba(255, 255, 255,0);
}

#RemoveButton
{
    background-image: resource("Icons/Toolbar Minus.png");
}

.Dark . #RemoveButton
{
    background-image: resource("Icons/d_Toolbar Minus.png");
    background-color: rgba(255, 255, 255, 0);
}

#RemoveButton:hover {
    /* GUIState.textColor */
    background-color: rgb(255, 255, 255);
}

.Dark #RemoveButton:hover {
    /* GUIState.textColor */
    background-color: rgb(128, 128, 128);
}


Button:disabled {
    opacity: 0.5;
}

LayoutOverlay > #HorizontalHolder > #RightOverlay PopupWindow Slider {
    flex: 1 0;
    margin-left : 0;
    margin-top :16px;
    margin-right : 0;
    margin-bottom :16px;
}