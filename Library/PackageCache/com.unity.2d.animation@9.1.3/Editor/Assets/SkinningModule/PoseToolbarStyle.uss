/**********************************************************************************************************************/
/* PoseToolbar                                                                                                         */
/**********************************************************************************************************************/

#EditPoseImage {
    background-image: url("../EditorIcons/Light/Edit_Pose.png");
}

#ResetPoseImage {
    background-image: url("../EditorIcons/Light/character_Mode.png");
}

#SetPivotImage {
    background-image: url("../EditorIcons/Light/SetPivot16.png");
}

.Dark #EditPoseImage {
    background-image: url("../EditorIcons/Dark/d_Edit_Pose.png");
}

.Dark #ResetPoseImage {
    background-image: url("../EditorIcons/Dark/d_character_Mode.png");
}

.Dark #SetPivotImage {
    background-image: url("../EditorIcons/Dark/d_SetPivot16.png");
}


.Checked #EditPoseImage{
    background-image: url("../EditorIcons/Selected/Edit_Pose.png");
}

.Checked #ResetPoseImage {
    background-image: url("../EditorIcons/Selected/character_Mode.png");
}

.Checked #SetPivotImage {
    background-image: url("../EditorIcons/Selected/SetPivot16.png");
}