<?xml version="1.0" encoding="utf-8"?>
<UXML xmlns:ui="UnityEngine.UIElements" xmlns:eui="UnityEditor.UIElements" xmlns:aui="UnityEditor.U2D.Animation">
  <aui:PastePanel name="PastePanel" picking-mode="Ignore">
    <ui:PopupWindow text="Paste">
      <ui:VisualElement class="form-row">
        <ui:Label name="BonesLabel" tooltip="Paste copied Bone data" text="Bones" />
        <ui:Toggle name="BonesField" class="form-editor" value="true" />
      </ui:VisualElement>
      <ui:VisualElement class="form-row">
        <ui:Label name="MeshLabel" tooltip="Paste copied Mesh data" text="Mesh" />
        <ui:Toggle name="MeshField" class="form-editor" value="true" />
      </ui:VisualElement>
      <ui:VisualElement class="form-row">
        <ui:Label name="FlipXLabel" tooltip="Flips all pasted data horizontally" text="Flip X" />
        <ui:Toggle name="FlipXField" class="form-editor" value="false" />
      </ui:VisualElement>
      <ui:VisualElement class="form-row">
        <ui:Label name="FlipYLabel" tooltip="Flips all pasted data vertically" text="Flip Y" />
        <ui:Toggle name="FlipYField" class="form-editor" value="false" />
      </ui:VisualElement>
      <ui:VisualElement name="Content">
        <ui:VisualElement class="form-row-space" />
        <ui:VisualElement class="form-row">
          <ui:Button name="PasteButton" text="Paste" tooltip="Paste Selected Animation Data"/>
        </ui:VisualElement>
      </ui:VisualElement>
    </ui:PopupWindow>
  </aui:PastePanel>
</UXML>
