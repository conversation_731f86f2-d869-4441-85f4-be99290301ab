<?xml version="1.0" encoding="utf-8"?>
<UXML xmlns:ui="UnityEngine.UIElements" xmlns:eui="UnityEditor.UIElements" xmlns:aui="UnityEditor.U2D.Animation">
  <aui:GenerateGeometryPanel name="GenerateGeometryPanel" picking-mode="Ignore">
    <ui:PopupWindow text="Geometry">
      <ui:VisualElement name="Content">
        <ui:VisualElement class="form-row">
          <ui:VisualElement class="form-editor">
            <ui:Slider name="OutlineDetailSlider" class="named-slider" direction="Horizontal" low-value="0" high-value="100" label="Outline Detail" tooltip="Accuracy of the generated outline. Small values will produce simpler outlines. Large values will produce denser outlines that fit to the Sprite better" />
            <eui:IntegerField name="OutlineDetailField" class="slider-field" value="0" />
          </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement class="form-row">
          <ui:VisualElement class="form-editor">
            <ui:Slider name="AlphaToleranceSlider" class="named-slider" direction="Horizontal" low-value="0" high-value="254" label="Alpha Tolerance" tooltip="Pixels with alpha value smaller than tolerance will be considered transparent during outline detection" />
            <eui:IntegerField name="AlphaToleranceField" class="slider-field" value="0" />
          </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement class="form-row">
          <ui:VisualElement class="form-editor">
            <ui:Slider name="SubdivideSlider" class="named-slider" direction="Horizontal" low-value="0" high-value="100" label="Subdivide" tooltip="Tessellate the Sprite by adding vertices inside the generated outline" />
            <eui:IntegerField name="SubdivideField" class="slider-field" value="0" />
          </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement class="form-row">
          <ui:Label name="GenerateWeightsLabel" tooltip="Initialize weights automatically for the generated geometry" text="Weights" />
          <ui:Toggle name="GenerateWeightsField" class="form-editor" value="true" />
        </ui:VisualElement>
        <ui:VisualElement class="form-row-space" />
        <ui:VisualElement name="GenerateSingleSprite" class="form-row">
          <ui:Button name="GenerateGeometryButton" text="Generate For Selected" tooltip="Generate Geometry for the selected Sprite"/>
        </ui:VisualElement>
        <ui:VisualElement name="GenerateMultipleSprite" class="form-row">
          <ui:Button name="GenerateGeometryAllButton" text="Generate For All Visible" tooltip="Generate Geometry for all the Sprites"/>
        </ui:VisualElement>
      </ui:VisualElement>
    </ui:PopupWindow>
  </aui:GenerateGeometryPanel>
</UXML>
