{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 66026, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 66026, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 66026, "tid": 159, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 66026, "tid": 159, "ts": 1748399539746638, "dur": 1106, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 66026, "tid": 159, "ts": 1748399539779876, "dur": 2034, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 66026, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 66026, "tid": 1, "ts": 1748399538056857, "dur": 17822, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 66026, "tid": 1, "ts": 1748399538074683, "dur": 182646, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 66026, "tid": 1, "ts": 1748399538257358, "dur": 282540, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 66026, "tid": 159, "ts": 1748399539781917, "dur": 35, "ph": "X", "name": "", "args": {}}, {"pid": 66026, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538045398, "dur": 11509, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538056911, "dur": 1666488, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538058161, "dur": 25601, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538083770, "dur": 2236, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538086010, "dur": 83, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538086099, "dur": 554, "ph": "X", "name": "ProcessMessages 8118", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538086656, "dur": 49, "ph": "X", "name": "ReadAsync 8118", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538087186, "dur": 6, "ph": "X", "name": "ProcessMessages 7378", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538087194, "dur": 608, "ph": "X", "name": "ReadAsync 7378", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538087805, "dur": 8, "ph": "X", "name": "ProcessMessages 8175", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538087815, "dur": 48, "ph": "X", "name": "ReadAsync 8175", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538087882, "dur": 3, "ph": "X", "name": "ProcessMessages 2779", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538087887, "dur": 88, "ph": "X", "name": "ReadAsync 2779", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538087978, "dur": 2, "ph": "X", "name": "ProcessMessages 1847", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538087982, "dur": 62, "ph": "X", "name": "ReadAsync 1847", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538088047, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538088050, "dur": 4588, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538092643, "dur": 21, "ph": "X", "name": "ProcessMessages 8172", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538092687, "dur": 49, "ph": "X", "name": "ReadAsync 8172", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538093208, "dur": 2, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538093211, "dur": 56, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538093271, "dur": 3, "ph": "X", "name": "ProcessMessages 3715", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538093276, "dur": 41, "ph": "X", "name": "ReadAsync 3715", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538093321, "dur": 1264, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538094588, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538094589, "dur": 67, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538094660, "dur": 6, "ph": "X", "name": "ProcessMessages 6736", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538094667, "dur": 511, "ph": "X", "name": "ReadAsync 6736", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095182, "dur": 2, "ph": "X", "name": "ProcessMessages 1210", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095185, "dur": 55, "ph": "X", "name": "ReadAsync 1210", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095245, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095247, "dur": 39, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095289, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095291, "dur": 48, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095342, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095344, "dur": 522, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095882, "dur": 7, "ph": "X", "name": "ProcessMessages 5598", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095892, "dur": 53, "ph": "X", "name": "ReadAsync 5598", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095948, "dur": 2, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538095952, "dur": 50, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096004, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096005, "dur": 58, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096075, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096078, "dur": 58, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096150, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096152, "dur": 45, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096199, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096201, "dur": 591, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096813, "dur": 9, "ph": "X", "name": "ProcessMessages 2210", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096825, "dur": 53, "ph": "X", "name": "ReadAsync 2210", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096881, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096884, "dur": 36, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096923, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096926, "dur": 34, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096962, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538096964, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097004, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097006, "dur": 54, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097064, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097066, "dur": 37, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097107, "dur": 19, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097127, "dur": 39, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097170, "dur": 29, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097202, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097204, "dur": 46, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097253, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097256, "dur": 49, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097309, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097311, "dur": 299, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097615, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097618, "dur": 121, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097742, "dur": 2, "ph": "X", "name": "ProcessMessages 1536", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097746, "dur": 50, "ph": "X", "name": "ReadAsync 1536", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097800, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097802, "dur": 138, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097983, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538097986, "dur": 100, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098094, "dur": 1, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098096, "dur": 553, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098655, "dur": 5, "ph": "X", "name": "ProcessMessages 5471", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098662, "dur": 69, "ph": "X", "name": "ReadAsync 5471", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098735, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098760, "dur": 55, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098818, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098821, "dur": 57, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098882, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098885, "dur": 66, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538098956, "dur": 66, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099026, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099029, "dur": 98, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099132, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099134, "dur": 67, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099205, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099208, "dur": 140, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099353, "dur": 2, "ph": "X", "name": "ProcessMessages 1221", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099356, "dur": 80, "ph": "X", "name": "ReadAsync 1221", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099446, "dur": 2, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099452, "dur": 56, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099512, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538099514, "dur": 536, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538100054, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538100057, "dur": 42, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538100102, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538100104, "dur": 37, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538100145, "dur": 43, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538100192, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538100195, "dur": 118, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538100316, "dur": 1, "ph": "X", "name": "ProcessMessages 1247", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538100318, "dur": 43, "ph": "X", "name": "ReadAsync 1247", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101005, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101009, "dur": 117, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101129, "dur": 7, "ph": "X", "name": "ProcessMessages 5918", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101138, "dur": 49, "ph": "X", "name": "ReadAsync 5918", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101190, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101237, "dur": 68, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101309, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101311, "dur": 53, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101402, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101404, "dur": 39, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101446, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101449, "dur": 93, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101545, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101547, "dur": 89, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101639, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101640, "dur": 29, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101725, "dur": 1, "ph": "X", "name": "ProcessMessages 1071", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101727, "dur": 38, "ph": "X", "name": "ReadAsync 1071", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101769, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101771, "dur": 34, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101808, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101810, "dur": 32, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101846, "dur": 42, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101891, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101893, "dur": 33, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101930, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101948, "dur": 40, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538101992, "dur": 56, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102060, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102062, "dur": 53, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102118, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102120, "dur": 65, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102189, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102191, "dur": 58, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102252, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102255, "dur": 118, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102378, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102380, "dur": 54, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102438, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102440, "dur": 47, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102491, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102494, "dur": 97, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102595, "dur": 35, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102632, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102635, "dur": 40, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102696, "dur": 16, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102713, "dur": 62, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102779, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102782, "dur": 164, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102950, "dur": 1, "ph": "X", "name": "ProcessMessages 1004", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102952, "dur": 38, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538102994, "dur": 35, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538103033, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538103036, "dur": 67, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538103108, "dur": 2519, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538105635, "dur": 7, "ph": "X", "name": "ProcessMessages 8178", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538105644, "dur": 67, "ph": "X", "name": "ReadAsync 8178", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538106880, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538106883, "dur": 50, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538106936, "dur": 7, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538106943, "dur": 45, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538106991, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538106992, "dur": 36, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107032, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107034, "dur": 64, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107101, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107104, "dur": 48, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107154, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107157, "dur": 40, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107200, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107202, "dur": 51, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107257, "dur": 31, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538107293, "dur": 30, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538109817, "dur": 68, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538109889, "dur": 8, "ph": "X", "name": "ProcessMessages 8157", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538109899, "dur": 36, "ph": "X", "name": "ReadAsync 8157", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538109938, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538109941, "dur": 64, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110009, "dur": 1, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110012, "dur": 659, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110674, "dur": 5, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110680, "dur": 38, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110721, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110723, "dur": 38, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110765, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110767, "dur": 52, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110861, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110864, "dur": 73, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110939, "dur": 1, "ph": "X", "name": "ProcessMessages 1349", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110942, "dur": 32, "ph": "X", "name": "ReadAsync 1349", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538110978, "dur": 34, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111016, "dur": 83, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111101, "dur": 2, "ph": "X", "name": "ProcessMessages 1112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111104, "dur": 37, "ph": "X", "name": "ReadAsync 1112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111144, "dur": 35, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111182, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111184, "dur": 30, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111218, "dur": 31, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111265, "dur": 44, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111311, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111313, "dur": 171, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111487, "dur": 2, "ph": "X", "name": "ProcessMessages 1615", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111491, "dur": 35, "ph": "X", "name": "ReadAsync 1615", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111529, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111533, "dur": 48, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111584, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111586, "dur": 58, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111647, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111649, "dur": 51, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538111703, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538112213, "dur": 746, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538112965, "dur": 7, "ph": "X", "name": "ProcessMessages 4941", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538112974, "dur": 1964, "ph": "X", "name": "ReadAsync 4941", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538114946, "dur": 47, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538114999, "dur": 1428, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538116434, "dur": 3, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538116439, "dur": 86, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538116529, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538116532, "dur": 71, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538116608, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538116610, "dur": 961, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538117582, "dur": 2, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538117601, "dur": 813, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538118419, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538118422, "dur": 69918, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538188370, "dur": 17, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538188395, "dur": 136, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538188535, "dur": 37, "ph": "X", "name": "ProcessMessages 8189", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538188575, "dur": 128, "ph": "X", "name": "ReadAsync 8189", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538188720, "dur": 2, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538188723, "dur": 832, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538189572, "dur": 2, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538257944, "dur": 144, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538258093, "dur": 12, "ph": "X", "name": "ProcessMessages 8137", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538258108, "dur": 121, "ph": "X", "name": "ReadAsync 8137", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538258236, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538258239, "dur": 65, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538258308, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538258324, "dur": 2458, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538260789, "dur": 3, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538260794, "dur": 553, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538261351, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538261353, "dur": 407, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538261764, "dur": 12, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538261778, "dur": 335, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538262117, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538262120, "dur": 45, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538262169, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538262171, "dur": 2089, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538264264, "dur": 1, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538264266, "dur": 74, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538264345, "dur": 2, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538264348, "dur": 18303, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538282657, "dur": 23, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538282681, "dur": 148, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538282860, "dur": 326, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538283191, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538283193, "dur": 637, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538283833, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538283835, "dur": 520, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538284359, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538284362, "dur": 630, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538284995, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538284997, "dur": 297, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538285297, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538285299, "dur": 384, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538285687, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538285688, "dur": 1174, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538286865, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538286868, "dur": 454, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538287342, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538287388, "dur": 1306, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538288699, "dur": 2, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538288702, "dur": 235, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538288941, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538288942, "dur": 326, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538289273, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538289275, "dur": 512, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538289791, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538289794, "dur": 49, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538289847, "dur": 1837, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538291689, "dur": 4, "ph": "X", "name": "ProcessMessages 3256", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538291694, "dur": 1590, "ph": "X", "name": "ReadAsync 3256", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538293289, "dur": 2, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538293292, "dur": 1408, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538295326, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538295329, "dur": 62, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538295395, "dur": 602, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538296002, "dur": 2, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538296005, "dur": 4790, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538300803, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538300830, "dur": 105, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538300940, "dur": 5, "ph": "X", "name": "ProcessMessages 3163", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538300952, "dur": 487, "ph": "X", "name": "ReadAsync 3163", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538301465, "dur": 2, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538301468, "dur": 1444, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538302917, "dur": 614, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538303536, "dur": 148, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538303689, "dur": 3, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538303693, "dur": 132, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538303830, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538303832, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538303901, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538303903, "dur": 88, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538303996, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538303998, "dur": 897, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538304901, "dur": 4, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538304907, "dur": 343, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538305253, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538305256, "dur": 207, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538305467, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538305469, "dur": 1226, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538306711, "dur": 5, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538306717, "dur": 10591, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317345, "dur": 16, "ph": "X", "name": "ProcessMessages 1776", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317364, "dur": 54, "ph": "X", "name": "ReadAsync 1776", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317420, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317423, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317523, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317525, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317632, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317634, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317763, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317765, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317855, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538317857, "dur": 141, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318002, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318004, "dur": 113, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318122, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318124, "dur": 303, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318430, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318433, "dur": 219, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318656, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318659, "dur": 81, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318850, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318853, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318914, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538318916, "dur": 742, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538319663, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538319668, "dur": 172, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538319844, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538319846, "dur": 397, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538320249, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538320252, "dur": 838, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538321094, "dur": 4, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538321100, "dur": 581, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538321685, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538321689, "dur": 84, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538321778, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538321781, "dur": 676, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538322513, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538322516, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538322581, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538322586, "dur": 244, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538322835, "dur": 172, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538323012, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538323014, "dur": 343, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538323361, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538323365, "dur": 262, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538323646, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538323649, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538323734, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538323737, "dur": 1366, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538325118, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538325121, "dur": 1656, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538326782, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538326786, "dur": 32071, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538358865, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538358869, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538359002, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538359015, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538359153, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538359401, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538359403, "dur": 3662, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538363072, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538363074, "dur": 10542, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538373625, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538373629, "dur": 354, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538373987, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538373990, "dur": 1253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538375249, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538375300, "dur": 152, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538375457, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538375459, "dur": 1916, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538377381, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538377384, "dur": 715, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538378105, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538378107, "dur": 404, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538378516, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538378518, "dur": 215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538378738, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538378811, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538378899, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538378901, "dur": 177, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379083, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379085, "dur": 174, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379262, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379265, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379403, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379405, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379491, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379493, "dur": 199, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379701, "dur": 235, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379940, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538379942, "dur": 476, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538380423, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538380426, "dur": 707, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538381137, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538381140, "dur": 919, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538382064, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538382067, "dur": 943, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538383016, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538383020, "dur": 291, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538383316, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538383319, "dur": 64, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538383388, "dur": 220, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538383612, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538383632, "dur": 645, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538384282, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538384284, "dur": 252, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538384541, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538384543, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538384675, "dur": 409, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538385091, "dur": 341, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538385436, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538385438, "dur": 216, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538385658, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538385661, "dur": 411, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538386075, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538386078, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538386315, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538386318, "dur": 594, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538386916, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538386919, "dur": 228, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538387151, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538387154, "dur": 642, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538387800, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538387802, "dur": 1038, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538388846, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538388849, "dur": 799, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538389654, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538389657, "dur": 1536, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538391200, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538391249, "dur": 3748, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538395020, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538395025, "dur": 367, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538395397, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538395401, "dur": 459, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538395864, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538395867, "dur": 2649, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538398523, "dur": 6, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538398531, "dur": 461, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538398996, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538398999, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538399127, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538399129, "dur": 1594, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538400728, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538400731, "dur": 358, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538401226, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538401229, "dur": 482, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538401717, "dur": 47, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538401766, "dur": 367, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538402177, "dur": 1263, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538403448, "dur": 148, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538403603, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538403606, "dur": 365, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538403975, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538403977, "dur": 985, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538404970, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538404973, "dur": 284, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538405284, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538405288, "dur": 345, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538405637, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538405640, "dur": 323, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538405968, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538405970, "dur": 335, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538406309, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538406311, "dur": 601, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538406917, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538406919, "dur": 390, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538407315, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538407317, "dur": 1079, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538408402, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538408405, "dur": 890, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538409301, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538409305, "dur": 502, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538409863, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538409866, "dur": 320, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410191, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410194, "dur": 317, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410515, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410518, "dur": 78, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410615, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410621, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410690, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410692, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410771, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410773, "dur": 88, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410865, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410867, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410984, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538410986, "dur": 169, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538411159, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538411162, "dur": 127, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538411294, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538411296, "dur": 265, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538411565, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538411590, "dur": 4038, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538415636, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538415640, "dur": 238, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538415882, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538415884, "dur": 549, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538416438, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538416440, "dur": 543, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538416988, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538416990, "dur": 739, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538417734, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538417737, "dur": 229399, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538647147, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538647152, "dur": 80, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538647238, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538647241, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538647311, "dur": 99, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538647416, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538647418, "dur": 60, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538649118, "dur": 70, "ph": "X", "name": "ReadAsync 4138", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538649213, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538649215, "dur": 73, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538649293, "dur": 2117, "ph": "X", "name": "ProcessMessages 4842", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538651415, "dur": 3237, "ph": "X", "name": "ReadAsync 4842", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538654660, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538654663, "dur": 1093, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538655763, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538655767, "dur": 2251, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538658041, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538658045, "dur": 876, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538658926, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538658928, "dur": 243, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538659175, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538659178, "dur": 210, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538659392, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538659394, "dur": 2751, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538662151, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538662154, "dur": 305, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538662464, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538662466, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538662631, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538662633, "dur": 150, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538662789, "dur": 2221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538665015, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538665018, "dur": 590, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538665613, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538665615, "dur": 184, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538665803, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538665805, "dur": 225, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538666034, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538666037, "dur": 3041, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538669084, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538669087, "dur": 110, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538669201, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538669204, "dur": 3117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538672329, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538672332, "dur": 406, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538672744, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538672747, "dur": 1567, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538674320, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538674324, "dur": 938, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538675267, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538675269, "dur": 833, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538676107, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538676110, "dur": 735, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538676849, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538676852, "dur": 2315, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679173, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679176, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679288, "dur": 32, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679348, "dur": 184, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679536, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679539, "dur": 144, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679688, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679691, "dur": 128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679823, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679825, "dur": 123, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679952, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538679954, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680112, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680114, "dur": 98, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680216, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680218, "dur": 210, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680433, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680436, "dur": 133, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680573, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680576, "dur": 79, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680660, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680663, "dur": 116, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680783, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680786, "dur": 94, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680893, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680897, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680986, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538680988, "dur": 59, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681077, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681081, "dur": 71, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681157, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681159, "dur": 101, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681264, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681267, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681359, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681362, "dur": 73, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681439, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681442, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681530, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681547, "dur": 78, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681629, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681631, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681699, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681701, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681793, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681796, "dur": 167, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681967, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538681970, "dur": 122, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682096, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682098, "dur": 66, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682168, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682170, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682248, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682250, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682379, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682381, "dur": 313, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682699, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399538682702, "dur": 547010, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539229723, "dur": 26, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539229751, "dur": 4757, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539234513, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539234516, "dur": 469460, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539703986, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539703990, "dur": 91, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539704086, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539704089, "dur": 114, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539704213, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539704216, "dur": 92, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539704313, "dur": 53, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539704371, "dur": 23, "ph": "X", "name": "ProcessMessages 2629", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539704396, "dur": 8028, "ph": "X", "name": "ReadAsync 2629", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539712432, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539712437, "dur": 902, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539713348, "dur": 106, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539713475, "dur": 278, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539713758, "dur": 374, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 66026, "tid": 12884901888, "ts": 1748399539714135, "dur": 9173, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 66026, "tid": 159, "ts": 1748399539781967, "dur": 3058, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 66026, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 66026, "tid": 8589934592, "ts": 1748399538036785, "dur": 503323, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 66026, "tid": 8589934592, "ts": 1748399538540112, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 66026, "tid": 8589934592, "ts": 1748399538540120, "dur": 5028, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 66026, "tid": 159, "ts": 1748399539785028, "dur": 59, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 66026, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 66026, "tid": 4294967296, "ts": 1748399537946073, "dur": 1781394, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 66026, "tid": 4294967296, "ts": 1748399537966421, "dur": 55178, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 66026, "tid": 4294967296, "ts": 1748399539727839, "dur": 15164, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 66026, "tid": 4294967296, "ts": 1748399539735027, "dur": 5050, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 66026, "tid": 4294967296, "ts": 1748399539743129, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 66026, "tid": 159, "ts": 1748399539785090, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748399538042790, "dur": 6404, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748399538049233, "dur": 33279, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748399538082621, "dur": 96, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748399538082718, "dur": 208, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748399538083646, "dur": 3290, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748399538088166, "dur": 341, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748399538089110, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748399538090439, "dur": 3022, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_58CF8A58239EF61C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748399538098307, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748399538101553, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748399538102842, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748399538103907, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_697AC546F729FDEE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748399538104633, "dur": 1841, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748399538107382, "dur": 388, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.Editor.ref.dll_B6E543B7064A5CCA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748399538108929, "dur": 1782, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748399538134870, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748399538134959, "dur": 54404, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748399538200515, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748399538200570, "dur": 58354, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748399538259018, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748399538265612, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748399538275789, "dur": 7715, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748399538286346, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748399538293364, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748399538082935, "dur": 219215, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748399538302166, "dur": 1411830, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748399539713998, "dur": 56, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748399539714265, "dur": 162, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748399539714486, "dur": 1393, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748399538082845, "dur": 219334, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538302229, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748399538302642, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538303329, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538303514, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538303787, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538304016, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538304127, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538304236, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538304514, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538304694, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538304788, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538305031, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538305203, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538305314, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538305403, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538305626, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538305747, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538305813, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538306042, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538306222, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538306990, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538307076, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538307427, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538307595, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538307870, "dur": 1451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538309338, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748399538309862, "dur": 74, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748399538310011, "dur": 3397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748399538313409, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538313472, "dur": 1644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748399538315252, "dur": 1190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748399538316443, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538316693, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538316754, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538316812, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538316905, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748399538317032, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748399538317099, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538317355, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748399538317990, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538318219, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748399538318861, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538319191, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538319411, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538319484, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538319600, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538319883, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748399538320377, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538320934, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538321360, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748399538321465, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538321590, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538321726, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538322017, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748399538322475, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538322678, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538322771, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748399538322976, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748399538323854, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748399538323971, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538324125, "dur": 4544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538328670, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538330010, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538331217, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538332360, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538333619, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538335299, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538336410, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538337508, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538338677, "dur": 1480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538340229, "dur": 778, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/Renderer2DDataEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748399538342044, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/Converter/ParametricToFreeformLightUpgrader.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748399538340157, "dur": 4007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538344165, "dur": 2208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538346373, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538348336, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538350016, "dur": 1856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538351872, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538353395, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538354824, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538356079, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538357213, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538358412, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538359652, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538359722, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748399538359776, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538359853, "dur": 1569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538361422, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538362698, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538364122, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538365439, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538367137, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538368425, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538369534, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538370610, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538371773, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538373117, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538374608, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538375658, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538375975, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538376036, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538377259, "dur": 745, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538378009, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538378072, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538378148, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538379243, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538379452, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538380148, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538380212, "dur": 1968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538382181, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538382764, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538382843, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538383220, "dur": 1669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538384889, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538385269, "dur": 3505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538388775, "dur": 696, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538389484, "dur": 7214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538396699, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538396864, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538396929, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538397268, "dur": 3183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538400452, "dur": 758, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538401221, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Editor.ref.dll_7BE565C70AE586E5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538401323, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538401688, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538401983, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538402253, "dur": 3850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538406104, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538406670, "dur": 5131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538411802, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538412092, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748399538412155, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538412280, "dur": 2416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538414697, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538414948, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538415265, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538415429, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538415536, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538416009, "dur": 136068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538552080, "dur": 99983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538652065, "dur": 2873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538654939, "dur": 1484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538656454, "dur": 2964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538659419, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538659863, "dur": 3292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538663156, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538663293, "dur": 3117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538666411, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538666518, "dur": 2867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538669386, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538669523, "dur": 3134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538672668, "dur": 1698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538674402, "dur": 2986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538677388, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538677497, "dur": 5863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748399538683361, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748399538683440, "dur": 1030530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538082845, "dur": 219373, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538302236, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748399538302626, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538303208, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538303506, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538303574, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538303784, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538304027, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538304466, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_EBB973DC47E5C128.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538304633, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538304730, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AB6C0CE8644C7690.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538304842, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538305007, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_57AB718CCA0A791C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538305117, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538305262, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538305381, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538305696, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538305872, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538306044, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_51CCB6F491E5150D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538306239, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538306424, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538306576, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538306960, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538307245, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538307356, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538307902, "dur": 2854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538310783, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748399538311715, "dur": 1767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748399538313552, "dur": 1601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538315178, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538315236, "dur": 1404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748399538316679, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538316750, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538316895, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538317049, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538317369, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748399538317608, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538318072, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538318203, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538318479, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538318599, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538319445, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538319537, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538319612, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748399538319681, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538319831, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538320015, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538320180, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538320236, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538320744, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538320994, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538321332, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748399538321413, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538321616, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538321695, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538321811, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538322128, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538322459, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538322600, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538322716, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538322795, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538323006, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538323087, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538323203, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538324112, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748399538324321, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538324460, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538324609, "dur": 1597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538326206, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538327661, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538329279, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538330425, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538331715, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538332888, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538334520, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538335817, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538336981, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538338134, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538340221, "dur": 773, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShapeEditor/EditablePath/BezierUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748399538342018, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteCustomLitShaderGraph.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748399538339542, "dur": 3044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538342586, "dur": 2905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538345491, "dur": 2336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538347827, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538349258, "dur": 2174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538351432, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538352860, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538354396, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538355698, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538356811, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538357916, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538359114, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538360626, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538362137, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538363412, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538364868, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538366335, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538367820, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538368997, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538370088, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538371252, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538372453, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538373965, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538375279, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538375779, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538375871, "dur": 3323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538379195, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538379619, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C5B5631EE4DDD44F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538379695, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538379770, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538380286, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538380392, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538381030, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538381585, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538381819, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538383336, "dur": 679, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538384025, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538384084, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538384305, "dur": 1620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538385926, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538386411, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538387477, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538387821, "dur": 1561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538389383, "dur": 659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538390058, "dur": 1738, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538391814, "dur": 2382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538394198, "dur": 1004, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538395210, "dur": 922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538396133, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538396472, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538396627, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538396703, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538397021, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538397422, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538397526, "dur": 18573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538416100, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538416322, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748399538416573, "dur": 235450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538652030, "dur": 3054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538655085, "dur": 1376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538656495, "dur": 3180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538659676, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538659941, "dur": 3473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538663415, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538663508, "dur": 3167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538666676, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538666773, "dur": 3042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538669816, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538669924, "dur": 4135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538674066, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538674357, "dur": 3285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538677643, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538677759, "dur": 5252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748399538683011, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748399538683112, "dur": 1030918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538082858, "dur": 219370, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538302233, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538303080, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538303677, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538304074, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538304182, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538304462, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538304647, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_E69ADDE2B61683F5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538304779, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538305028, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538305180, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538305276, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538305397, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538305655, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538305758, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538305825, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538306037, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538306173, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538306375, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538306526, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538306625, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538307057, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538307475, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538307650, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538307957, "dur": 1214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538309185, "dur": 1533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538310746, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538312199, "dur": 1247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_FB7A597403E5FDBB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538313519, "dur": 1565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538315117, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538315184, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748399538316521, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748399538316635, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538316700, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538316879, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538316997, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538317290, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538317403, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538318099, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748399538318459, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538318528, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538318840, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538319182, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538319363, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748399538319434, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538319505, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538319589, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538320300, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538320743, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538320956, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538321351, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538321486, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538321612, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538321681, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538321778, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538322056, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538322469, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538322596, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538322727, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538322816, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538323043, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538323216, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538323447, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538323641, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538323895, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538324016, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538324119, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748399538324350, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538324438, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538326103, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538327517, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538329190, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538330353, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538331618, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538332719, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538334215, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538335628, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538336778, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538337926, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538340210, "dur": 765, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShapeEditor/Selection/ISelector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748399538339209, "dur": 2532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538342034, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_4_3.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748399538341742, "dur": 3396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538345138, "dur": 2398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538347536, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538348995, "dur": 2196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538351191, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538352625, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538354148, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538355490, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538356675, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538357800, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538359005, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538360393, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538361952, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538363267, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538364759, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538366097, "dur": 1619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538367716, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538368887, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538369999, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538371170, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538372372, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538373867, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538375222, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538375771, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538375831, "dur": 3369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538379200, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538379595, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_A298F0F0964CDA5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538379663, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538379747, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538380565, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538380646, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538381135, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538381709, "dur": 2997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538384707, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538385361, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538385722, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538386443, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538386628, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538386761, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538386980, "dur": 5381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538392362, "dur": 3027, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538395400, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538395668, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538395769, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538396847, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538397046, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538397109, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538397291, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538397558, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538398038, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538398429, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538398481, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538398605, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538399696, "dur": 1315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538401012, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538401131, "dur": 4502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538405634, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538405991, "dur": 2601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538408593, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538408877, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538411163, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538411471, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538411570, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538411679, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748399538411748, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538411826, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538411972, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538412035, "dur": 2359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538414395, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538415855, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538416012, "dur": 236079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538652094, "dur": 3172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538655267, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538655348, "dur": 3025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538658375, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538658697, "dur": 3516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538662214, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538662313, "dur": 3295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538665609, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538665679, "dur": 3240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538668920, "dur": 840, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538669766, "dur": 3007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538672774, "dur": 1574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538674373, "dur": 3009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538677382, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538677477, "dur": 2143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748399538679621, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538679799, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748399538679981, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538680167, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538680352, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748399538680407, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538680520, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748399538680634, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538680772, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538680876, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538681195, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748399538681402, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538681589, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538681691, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538681810, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538681918, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538682174, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538682346, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538682548, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538682894, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748399538683122, "dur": 1030845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538082868, "dur": 219401, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538302276, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538303047, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538303532, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538303783, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538304010, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538304109, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_37E22A5ED73E2B2F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538304218, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538304498, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538304640, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538304767, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD8CC2F62B9395CF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538304827, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538305040, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538305334, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538305447, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538305650, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538305794, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538306021, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538306188, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538306362, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538306537, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538306649, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748399538307058, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538307505, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538307856, "dur": 1338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538309231, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538309370, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748399538309595, "dur": 1037, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538310727, "dur": 926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538311654, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538311742, "dur": 1764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748399538313589, "dur": 1572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538315162, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538315222, "dur": 1634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538316910, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538317137, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538317502, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538317885, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538318007, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748399538318515, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748399538318570, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538319220, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538319470, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538319541, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748399538319623, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538319899, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538319990, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538320535, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538320891, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538320983, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538321134, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538321374, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538321562, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538321676, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538321832, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538322137, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538322292, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538322508, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538322857, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538323181, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538323860, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538323981, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748399538324107, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538324478, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538324547, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538326133, "dur": 1584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538327718, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538329331, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538330473, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538331752, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538332860, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538334539, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538335809, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538336969, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538338107, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538340262, "dur": 771, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShapeEditor/EditorTool/ScriptablePath.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748399538339402, "dur": 2565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538342068, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_0_0.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748399538341968, "dur": 3237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538345206, "dur": 2439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538347646, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538349008, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538351154, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538352592, "dur": 1584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538354176, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538355557, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538356724, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538357892, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538359089, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538360427, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538361938, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538363248, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538364716, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538366083, "dur": 1590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538367674, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538368841, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538369917, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538371079, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538372247, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538373709, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538375108, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538375985, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538376062, "dur": 1892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538377960, "dur": 813, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538378830, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538379183, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538379797, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538379989, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538382362, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538382770, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538382854, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538383210, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538383399, "dur": 1535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538384935, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538385227, "dur": 2120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538387348, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538387538, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538388308, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538388420, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538389515, "dur": 1380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538390906, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538391939, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538392030, "dur": 2482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538394513, "dur": 644, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538395165, "dur": 1372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538396538, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538396833, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538397037, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538397520, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538397634, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538397900, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538397991, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538398348, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538398406, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538398468, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538398691, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538398790, "dur": 5349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538404140, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538404486, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538405638, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538405976, "dur": 3443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538409420, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538409625, "dur": 1491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538411117, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538411436, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538411516, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_AD2FFB7646FD92B4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748399538411631, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538411732, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748399538411785, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538411895, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538411972, "dur": 2481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538414454, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538414874, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538415044, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538415130, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538415194, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538415640, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538416030, "dur": 236082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538652115, "dur": 3070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538655186, "dur": 1239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538656469, "dur": 3015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538659486, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538659608, "dur": 3164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538662773, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538662899, "dur": 3397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538666297, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538666399, "dur": 3415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538669815, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538669973, "dur": 4059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538674033, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538674341, "dur": 3193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538677535, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538677625, "dur": 5159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748399538682785, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748399538682973, "dur": 1031004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538082880, "dur": 219406, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538302291, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538303089, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538303567, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1996541149DA13E0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538303657, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538303798, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538304000, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538304084, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538304197, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538304461, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_2EC518F8CEEB6CA9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538304634, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538304736, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538305015, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538305147, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538305230, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538305357, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538305668, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538305750, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538305835, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538306038, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538306169, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538306342, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538306445, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538306601, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538306994, "dur": 1683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748399538308678, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538309214, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748399538309268, "dur": 14464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538323733, "dur": 542, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538324275, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538324341, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538324396, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538324467, "dur": 2878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538327346, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538327478, "dur": 31697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538359179, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538359519, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538359577, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538360119, "dur": 3548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538363668, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538363794, "dur": 10160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538373955, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538374261, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538374329, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538374723, "dur": 1171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538375945, "dur": 3373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538379319, "dur": 754, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538380136, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538380628, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538381059, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538381673, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538381731, "dur": 2003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538383735, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538384078, "dur": 5962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538390041, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538390500, "dur": 4019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538394567, "dur": 853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538395421, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538395623, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538395942, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538396944, "dur": 1282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538398240, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538398331, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538398823, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538398905, "dur": 7267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538406173, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538406764, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538406852, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748399538407437, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538407499, "dur": 3577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538411077, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538411634, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538411715, "dur": 4782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538416498, "dur": 1222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538417728, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399538418356, "dur": 52, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399538418445, "dur": 811960, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748399539231959, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748399539231927, "dur": 2175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748399539234912, "dur": 274, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399539704626, "dur": 515, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748399539235217, "dur": 469947, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748399539712931, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748399539712908, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748399539713131, "dur": 728, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748399539713863, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538082909, "dur": 219419, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538302334, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538303082, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538303567, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_710F5443A18E10C1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538303678, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538304052, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_CAA42EBCF9A9A6C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538304189, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538304486, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B66FF45CB39FB62A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538304659, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538304791, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B92AE864B27146B1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538304862, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538305038, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538305185, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538305292, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538305400, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538305633, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538305802, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538305950, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538306144, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538306346, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538306512, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538306612, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538307061, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538307462, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538307747, "dur": 1856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748399538309642, "dur": 13812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538323455, "dur": 784, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538324291, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538324502, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538326144, "dur": 1422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538327566, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538329200, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538330345, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538331630, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538332711, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538334196, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538335621, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538336752, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538337887, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538340253, "dur": 770, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/Camera/UniversalRenderPipelineCameraUI.Rendering.Skin.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748399538339094, "dur": 2488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538342022, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.State/FlowStateTransition.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748399538341583, "dur": 3311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538344894, "dur": 2240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538347148, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538348735, "dur": 2133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538350868, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538352356, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538353817, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538355261, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538356439, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538357593, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538358760, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538360136, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538361635, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538362925, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538364368, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538365682, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538367342, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538368566, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538369678, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538370754, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538371934, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538373332, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538374801, "dur": 971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538375772, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538375849, "dur": 9612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538385462, "dur": 641, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538386169, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538386379, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538386686, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538386959, "dur": 3091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538390051, "dur": 4933, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538395013, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538395421, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748399538395547, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538395826, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538396557, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538396988, "dur": 4170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1748399538401158, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538401279, "dur": 906, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538647743, "dur": 2308, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538402733, "dur": 247409, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1748399538652020, "dur": 4171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538656192, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538656462, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538656526, "dur": 3452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538659979, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538660054, "dur": 3159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538663214, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538663325, "dur": 3107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538666433, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538666539, "dur": 3584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538670124, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538670212, "dur": 3191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538673403, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538673506, "dur": 3182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538676689, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538676853, "dur": 3368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748399538680222, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538680685, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748399538681033, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538681124, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538681258, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538681417, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538681645, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538681735, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538681799, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538681905, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538682019, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538682163, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538682314, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748399538682371, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538682594, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538682671, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538682873, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748399538683001, "dur": 1031016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538082920, "dur": 219433, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538302387, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538303042, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538303517, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538303625, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538303716, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37EA32C02CB3EC76.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538303779, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538304062, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538304171, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538304290, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538304505, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_72608D0D80FBEA70.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538304853, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538305050, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538305146, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538305236, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538305617, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538305708, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538305849, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538306022, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538306176, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538306378, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538306475, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538306610, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538306958, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538307305, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538307401, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538307509, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538307769, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_F0DEE4EE0970DD2B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538308632, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538309012, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538309338, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748399538309941, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538310510, "dur": 1737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538312248, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538312499, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538313677, "dur": 1423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538315101, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748399538315195, "dur": 1472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538316668, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538316727, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538317134, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538317417, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538317785, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538318328, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538318580, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538318641, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538319458, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538319573, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538319711, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538319784, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538320009, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538320607, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748399538320828, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538321069, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538321339, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538321617, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538321688, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538321821, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538322344, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538322512, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538322665, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538322742, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538323843, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538323971, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538324240, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748399538324303, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538325731, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538327287, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538328913, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538330167, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538331317, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538332453, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538333763, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538335406, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538336553, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538337644, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538340244, "dur": 773, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/Converter/RenderPipelineConvertersEditor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748399538338850, "dur": 2167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538341018, "dur": 3559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538344578, "dur": 2062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538346641, "dur": 1831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538348473, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538350421, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538352199, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538353703, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538355110, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538356340, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538357472, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538358654, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538359919, "dur": 1568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538361488, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538362785, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538364180, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538365527, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538367232, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538368469, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538369583, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538370660, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538371814, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538373224, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538374678, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538375698, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538376090, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538376161, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538379001, "dur": 659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538379691, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538379757, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538380284, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538380886, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538380971, "dur": 1285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538382257, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538382716, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D33AAA747FCEED0A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538382855, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538382975, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538384019, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538384279, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538384645, "dur": 7678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538392324, "dur": 2256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538394666, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538394843, "dur": 1420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538396264, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538396477, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538397091, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538397223, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538397387, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538397557, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538397620, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538398591, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538398744, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538401014, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538401394, "dur": 5198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538406593, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538406841, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538407052, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538407739, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538408004, "dur": 1796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538409801, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538410260, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538410459, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538410542, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Editor.ref.dll_FD249531A54E376F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538410777, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538411156, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538411218, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_94629FB7B2A83F68.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538411286, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538411391, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_CBD4E358CB8CDF43.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538411485, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538411616, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748399538411695, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538411837, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538411958, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538412063, "dur": 2385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538414448, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538415264, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538415384, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538415769, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538415993, "dur": 129316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538545313, "dur": 6759, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538552072, "dur": 99958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538652036, "dur": 3686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538655723, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538656411, "dur": 3388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538659800, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538659948, "dur": 3075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538663024, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538663084, "dur": 3124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538666209, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538666334, "dur": 3199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538669534, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538669700, "dur": 3370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538673071, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538673300, "dur": 3372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538676673, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538676829, "dur": 4492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748399538681322, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538681542, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748399538681600, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538681753, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538682125, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538682258, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538682470, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538682852, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399538682931, "dur": 1029979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748399539712931, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748399539712913, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748399539713126, "dur": 821, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748399538082931, "dur": 219433, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538302365, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538303075, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538303543, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_25D10CC8709418E4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538303725, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538304072, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_C9DCA0B70AE22DB0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538304202, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538304468, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538304657, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538305006, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538305090, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538305279, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538305677, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538305797, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538305992, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538306203, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538306351, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538306534, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538306623, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538307050, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538307509, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538307719, "dur": 1364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538309083, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538309306, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538309399, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538309560, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748399538309889, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538310019, "dur": 1320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538311372, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538311455, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748399538312255, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538312463, "dur": 1044, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538313534, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748399538313630, "dur": 1467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538315139, "dur": 1262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538316484, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538316672, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538316764, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538316822, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538316939, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538317066, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538317477, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538317873, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538318353, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538318466, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538318636, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538319358, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538319423, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538319491, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538319637, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538319818, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538319984, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538320183, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538320743, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538320970, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538321349, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538321582, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538321706, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538321980, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538322365, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538322574, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538322712, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538322789, "dur": 1284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748399538324073, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538324133, "dur": 3515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538327649, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538329203, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538330335, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538331606, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538332731, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538334257, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538335650, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538336769, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538337891, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538340200, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/AssetPostProcessors/FBXArnoldSurfaceMaterialDescriptionPreprocessor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748399538339112, "dur": 2514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538341645, "dur": 3365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538345011, "dur": 2322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538347334, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538348851, "dur": 2169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538351020, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538352487, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538353940, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538355308, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538356509, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538357678, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538358887, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538360223, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538361816, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538363054, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538364476, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538365816, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538367448, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538368653, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538369716, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538370835, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538372023, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538373469, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538374949, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538375757, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538375821, "dur": 7561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538383383, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538383944, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_4EC1FE8B3807FE28.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538384010, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538384116, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538384611, "dur": 7573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538392185, "dur": 2413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538394613, "dur": 556, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538395179, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538395555, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538395905, "dur": 2524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538398430, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538398632, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_7BBE081BF0E8AF4A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538398840, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538398933, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538399753, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538399826, "dur": 3905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538403732, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538404255, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538404321, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538404973, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538405067, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538405835, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538406099, "dur": 1423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538407523, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538407800, "dur": 2486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538410286, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538410855, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_258B0455F8F51CDE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538410940, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538411161, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538411257, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538411344, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.Editor.ref.dll_B6E543B7064A5CCA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538411458, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538411562, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538411635, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538411764, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538411896, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538411996, "dur": 2212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538414210, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538415990, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538416343, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748399538416552, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538417081, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538417162, "dur": 234877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538652042, "dur": 3388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538655431, "dur": 954, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538656399, "dur": 3141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538659541, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538659709, "dur": 2999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538662709, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538662841, "dur": 3334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538666176, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538666292, "dur": 3229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538669521, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538669613, "dur": 3196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538672816, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538672910, "dur": 2957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538675868, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538675947, "dur": 4911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748399538680859, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538681423, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538681515, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538681655, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538681954, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748399538682015, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538682188, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538682362, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538682685, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538682775, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538682848, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538682912, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748399538683460, "dur": 1030528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748399539721128, "dur": 1787, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 66026, "tid": 159, "ts": 1748399539788896, "dur": 7602, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 66026, "tid": 159, "ts": 1748399539797123, "dur": 7169, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 66026, "tid": 159, "ts": 1748399539773034, "dur": 34562, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}