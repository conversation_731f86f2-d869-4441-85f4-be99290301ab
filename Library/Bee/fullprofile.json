{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 70593, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 70593, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 70593, "tid": 123, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 70593, "tid": 123, "ts": 1748421449631183, "dur": 770, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 70593, "tid": 123, "ts": 1748421449636682, "dur": 1022, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 70593, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 70593, "tid": 1, "ts": 1748421447644684, "dur": 19454, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748421447664144, "dur": 122911, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748421447787065, "dur": 240945, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 70593, "tid": 123, "ts": 1748421449637710, "dur": 44, "ph": "X", "name": "", "args": {}}, {"pid": 70593, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447641706, "dur": 150864, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447792573, "dur": 1819275, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447795746, "dur": 28214, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447823968, "dur": 4559, "ph": "X", "name": "ProcessMessages 8174", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447828532, "dur": 108, "ph": "X", "name": "ReadAsync 8174", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447828645, "dur": 19, "ph": "X", "name": "ProcessMessages 8127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447828666, "dur": 684, "ph": "X", "name": "ReadAsync 8127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829354, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829357, "dur": 72, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829433, "dur": 6, "ph": "X", "name": "ProcessMessages 8123", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829441, "dur": 57, "ph": "X", "name": "ReadAsync 8123", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829501, "dur": 2, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829505, "dur": 78, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829587, "dur": 2, "ph": "X", "name": "ProcessMessages 1362", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829590, "dur": 87, "ph": "X", "name": "ReadAsync 1362", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829681, "dur": 2, "ph": "X", "name": "ProcessMessages 1118", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829684, "dur": 93, "ph": "X", "name": "ReadAsync 1118", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829782, "dur": 2, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829786, "dur": 80, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829887, "dur": 2, "ph": "X", "name": "ProcessMessages 1244", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829891, "dur": 73, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829967, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447829977, "dur": 84, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447830065, "dur": 2, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447830069, "dur": 39, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447830112, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447830115, "dur": 38, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447830156, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447830158, "dur": 46, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447830207, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447830209, "dur": 6352, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447836567, "dur": 8, "ph": "X", "name": "ProcessMessages 8156", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447836576, "dur": 145, "ph": "X", "name": "ReadAsync 8156", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447836725, "dur": 2, "ph": "X", "name": "ProcessMessages 985", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447836728, "dur": 66, "ph": "X", "name": "ReadAsync 985", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447836798, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447836800, "dur": 1684, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447838500, "dur": 6, "ph": "X", "name": "ProcessMessages 5979", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447838508, "dur": 244, "ph": "X", "name": "ReadAsync 5979", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447838757, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447838760, "dur": 41, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447838804, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447838820, "dur": 1270, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840095, "dur": 22, "ph": "X", "name": "ProcessMessages 7349", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840119, "dur": 71, "ph": "X", "name": "ReadAsync 7349", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840193, "dur": 6, "ph": "X", "name": "ProcessMessages 4619", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840201, "dur": 60, "ph": "X", "name": "ReadAsync 4619", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840265, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840268, "dur": 36, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840307, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840309, "dur": 683, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840995, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447840997, "dur": 514, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447841535, "dur": 5, "ph": "X", "name": "ProcessMessages 5445", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447841541, "dur": 36, "ph": "X", "name": "ReadAsync 5445", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447841580, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447841582, "dur": 46, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447841631, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447841633, "dur": 105, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447841741, "dur": 2, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447841744, "dur": 35196, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447876954, "dur": 8, "ph": "X", "name": "ProcessMessages 8179", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447876965, "dur": 95, "ph": "X", "name": "ReadAsync 8179", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877063, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877066, "dur": 53, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877124, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877127, "dur": 364, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877495, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877498, "dur": 80, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877582, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877584, "dur": 75, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877663, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877667, "dur": 146, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877817, "dur": 1, "ph": "X", "name": "ProcessMessages 1291", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877819, "dur": 42, "ph": "X", "name": "ReadAsync 1291", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877881, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877884, "dur": 49, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877937, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447877939, "dur": 719, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447878661, "dur": 38, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447878709, "dur": 151, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447878864, "dur": 1, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447878867, "dur": 526, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447879396, "dur": 11, "ph": "X", "name": "ProcessMessages 2537", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447879410, "dur": 71, "ph": "X", "name": "ReadAsync 2537", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447879595, "dur": 1, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447879599, "dur": 53, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447879654, "dur": 2, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447879657, "dur": 622, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447880283, "dur": 2, "ph": "X", "name": "ProcessMessages 2231", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447880286, "dur": 751, "ph": "X", "name": "ReadAsync 2231", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447881041, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447881043, "dur": 50, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447881098, "dur": 11, "ph": "X", "name": "ProcessMessages 3825", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447881148, "dur": 46, "ph": "X", "name": "ReadAsync 3825", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447881197, "dur": 1, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447881199, "dur": 34348, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447915567, "dur": 3, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447915572, "dur": 145, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447915720, "dur": 21, "ph": "X", "name": "ProcessMessages 8137", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447915742, "dur": 81, "ph": "X", "name": "ReadAsync 8137", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447915827, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447915829, "dur": 987, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447916834, "dur": 2, "ph": "X", "name": "ProcessMessages 1514", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447916838, "dur": 86, "ph": "X", "name": "ReadAsync 1514", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447916927, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447916929, "dur": 81, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917015, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917017, "dur": 60, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917081, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917083, "dur": 119, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917205, "dur": 1, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917208, "dur": 99, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917322, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917324, "dur": 37, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917365, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917366, "dur": 98, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917467, "dur": 1, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917470, "dur": 49, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917522, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917525, "dur": 444, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917972, "dur": 2, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447917992, "dur": 550, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918545, "dur": 4, "ph": "X", "name": "ProcessMessages 2697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918551, "dur": 76, "ph": "X", "name": "ReadAsync 2697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918631, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918633, "dur": 56, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918693, "dur": 3, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918699, "dur": 56, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918758, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918761, "dur": 82, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918847, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447918850, "dur": 827, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447919681, "dur": 5, "ph": "X", "name": "ProcessMessages 4294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447919688, "dur": 246, "ph": "X", "name": "ReadAsync 4294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447919938, "dur": 21, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447919961, "dur": 116, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920080, "dur": 1, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920083, "dur": 144, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920247, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920250, "dur": 133, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920387, "dur": 2, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920390, "dur": 61, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920455, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920458, "dur": 68, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920530, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920535, "dur": 154, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920693, "dur": 2, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920696, "dur": 39, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920739, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920741, "dur": 44, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920789, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920791, "dur": 46, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447920840, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921523, "dur": 77, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921603, "dur": 3, "ph": "X", "name": "ProcessMessages 1554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921607, "dur": 92, "ph": "X", "name": "ReadAsync 1554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921703, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921706, "dur": 51, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921760, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921763, "dur": 67, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921833, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921836, "dur": 43, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921882, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447921884, "dur": 281, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447922169, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447922171, "dur": 89, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447922264, "dur": 2, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447922267, "dur": 710, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447922981, "dur": 6, "ph": "X", "name": "ProcessMessages 4889", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447922988, "dur": 98, "ph": "X", "name": "ReadAsync 4889", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447923090, "dur": 2, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447923093, "dur": 564, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447923661, "dur": 5, "ph": "X", "name": "ProcessMessages 4456", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447923668, "dur": 155, "ph": "X", "name": "ReadAsync 4456", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447923860, "dur": 4, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447923881, "dur": 61, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447923946, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447923948, "dur": 423, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447924376, "dur": 2, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447924379, "dur": 57, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447924440, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447924443, "dur": 50, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447924494, "dur": 7, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447924505, "dur": 541, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925050, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925053, "dur": 709, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925766, "dur": 2, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925769, "dur": 50, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925823, "dur": 4, "ph": "X", "name": "ProcessMessages 3081", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925828, "dur": 44, "ph": "X", "name": "ReadAsync 3081", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925876, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925878, "dur": 52, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925934, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925937, "dur": 52, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925992, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447925995, "dur": 740, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447926740, "dur": 3, "ph": "X", "name": "ProcessMessages 3116", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447926744, "dur": 66, "ph": "X", "name": "ReadAsync 3116", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447926817, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447926820, "dur": 61, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447926885, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447926887, "dur": 62, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447926954, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447926957, "dur": 156, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447927117, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447927119, "dur": 607, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447927730, "dur": 7, "ph": "X", "name": "ProcessMessages 3302", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447927739, "dur": 148, "ph": "X", "name": "ReadAsync 3302", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447927890, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447927893, "dur": 531, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447928444, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447928446, "dur": 1318, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447929769, "dur": 42, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447929813, "dur": 55, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447929873, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447929876, "dur": 256, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447930136, "dur": 3, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447930140, "dur": 2458, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447932634, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447932638, "dur": 419, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447933061, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447933064, "dur": 1399, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447934468, "dur": 4, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447934474, "dur": 327, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447934804, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447934806, "dur": 523, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447935333, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447935336, "dur": 301, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447935640, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447935642, "dur": 2809, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447938454, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447938457, "dur": 1252, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447939713, "dur": 1485, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447941202, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447941204, "dur": 41, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447941251, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447941254, "dur": 1062, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447942320, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447942323, "dur": 68, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447942395, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447942398, "dur": 1705, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447944107, "dur": 32, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447944141, "dur": 239, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447944384, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447944387, "dur": 1753, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447946144, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447946147, "dur": 269, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447946420, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447946422, "dur": 1691, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447948120, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447948124, "dur": 211, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447948340, "dur": 3, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447948344, "dur": 2027, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447950376, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447950417, "dur": 452, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447950879, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447950882, "dur": 2580, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447953468, "dur": 3, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447953492, "dur": 91, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447953588, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447953590, "dur": 2470, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447956066, "dur": 2, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447956070, "dur": 1967, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447958049, "dur": 4, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447958055, "dur": 1422, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447959482, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447959486, "dur": 597, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447960086, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447960089, "dur": 3184, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447963278, "dur": 55, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447963366, "dur": 1386, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447964756, "dur": 2, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447964760, "dur": 1739, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447966504, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447966508, "dur": 1336, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447967849, "dur": 2, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447967852, "dur": 1323, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447969180, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447969183, "dur": 348, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447969536, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447969539, "dur": 2729, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447972273, "dur": 42, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447972318, "dur": 2672, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447974997, "dur": 2, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447975001, "dur": 93, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447975099, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447975102, "dur": 2992, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447978099, "dur": 2, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447978103, "dur": 1937, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447980045, "dur": 3, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447980050, "dur": 1550, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447981605, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447981608, "dur": 162, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447981775, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447981778, "dur": 1250, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447983032, "dur": 2, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447983035, "dur": 51, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447983090, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447983092, "dur": 59, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447983154, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447983156, "dur": 641, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447983801, "dur": 13, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447983831, "dur": 607, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447984441, "dur": 2, "ph": "X", "name": "ProcessMessages 2000", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447984444, "dur": 746, "ph": "X", "name": "ReadAsync 2000", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447985194, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447985196, "dur": 209, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447985409, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447985426, "dur": 1225, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447986668, "dur": 1, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447986671, "dur": 249, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447986924, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447986926, "dur": 1464, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447988394, "dur": 1, "ph": "X", "name": "ProcessMessages 1210", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447988396, "dur": 2874, "ph": "X", "name": "ReadAsync 1210", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447991274, "dur": 2, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447991277, "dur": 1125, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447992406, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447992409, "dur": 274, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447992686, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447992688, "dur": 879, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447993569, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447993571, "dur": 53, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447993627, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447993629, "dur": 286, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447993920, "dur": 2371, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447996297, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447996300, "dur": 275, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447996579, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447996582, "dur": 658, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447997243, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447997246, "dur": 301, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447997551, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421447997553, "dur": 3204, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448000760, "dur": 3, "ph": "X", "name": "ProcessMessages 2412", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448000764, "dur": 9483, "ph": "X", "name": "ReadAsync 2412", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448010274, "dur": 4, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448010280, "dur": 131, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448010416, "dur": 7, "ph": "X", "name": "ProcessMessages 4986", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448010492, "dur": 476, "ph": "X", "name": "ReadAsync 4986", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448010973, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448010977, "dur": 1301, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448012297, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448012299, "dur": 42, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448012344, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448012346, "dur": 1296, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448013655, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448013658, "dur": 53, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448013717, "dur": 494, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448014214, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448014216, "dur": 624, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448014844, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448014847, "dur": 493, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448015343, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448015345, "dur": 323, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448015672, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448015674, "dur": 931, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448016611, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448016615, "dur": 315, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448016934, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448016938, "dur": 727, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448017668, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448017670, "dur": 33, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448017707, "dur": 30, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448017741, "dur": 31, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448017776, "dur": 48, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448017841, "dur": 39, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448017883, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448017884, "dur": 1548, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448019438, "dur": 3, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448019443, "dur": 802, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448020249, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448020251, "dur": 266, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448020521, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448020524, "dur": 801, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448021329, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448021332, "dur": 302, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448021637, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448021653, "dur": 856, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448022512, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448022514, "dur": 251, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448022769, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448022772, "dur": 966, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448023742, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448023745, "dur": 287, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448024036, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448024039, "dur": 44, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448024099, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448024101, "dur": 46, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448024151, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448024153, "dur": 83, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448024240, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448024243, "dur": 1561, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448025810, "dur": 2, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448025814, "dur": 487, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448026311, "dur": 558, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448026874, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448026976, "dur": 19, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027007, "dur": 82, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027092, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027096, "dur": 62, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027161, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027163, "dur": 87, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027255, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027258, "dur": 64, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027325, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027328, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027390, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027392, "dur": 50, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027446, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027449, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027512, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027515, "dur": 82, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027600, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027603, "dur": 153, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027759, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027762, "dur": 101, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027867, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448027869, "dur": 160, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448028033, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448028036, "dur": 764, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448028804, "dur": 5, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448028811, "dur": 410, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029226, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029229, "dur": 143, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029377, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029380, "dur": 78, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029463, "dur": 14, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029478, "dur": 57, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029539, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029541, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029735, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029753, "dur": 58, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029815, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448029818, "dur": 727, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448030581, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448030589, "dur": 113, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448030706, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448030709, "dur": 78, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448030791, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448030794, "dur": 64, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448030862, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448030864, "dur": 128, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448030996, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031000, "dur": 69, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031073, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031076, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031208, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031210, "dur": 108, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031322, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031324, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031570, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031572, "dur": 172, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031748, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031751, "dur": 128, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031883, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031886, "dur": 84, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031973, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448031977, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448032067, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448032069, "dur": 459, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448032533, "dur": 31, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448032568, "dur": 106, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448032815, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448032819, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448032896, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448032899, "dur": 114, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033018, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033022, "dur": 185, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033211, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033215, "dur": 198, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033417, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033419, "dur": 294, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033731, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033737, "dur": 93, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033833, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033835, "dur": 76, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033913, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448033916, "dur": 214, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034135, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034138, "dur": 102, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034255, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034258, "dur": 49, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034312, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034314, "dur": 91, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034410, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034412, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034484, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034488, "dur": 63, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034557, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034560, "dur": 64, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034626, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034629, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034709, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034712, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034765, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034791, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034884, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034886, "dur": 94, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034984, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448034986, "dur": 50, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035040, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035042, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035105, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035108, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035215, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035217, "dur": 144, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035373, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035391, "dur": 61, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035469, "dur": 9, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448035483, "dur": 3195, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448038684, "dur": 9, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448038722, "dur": 1029, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448039778, "dur": 30, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448039842, "dur": 316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448040162, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448040165, "dur": 416, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448040586, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448040589, "dur": 264, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448040858, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448040860, "dur": 3369, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448044236, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448044239, "dur": 23486, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448067733, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448067737, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448067842, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448067844, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448067952, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448067954, "dur": 7471, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448075447, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448075450, "dur": 6247, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448081719, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448081723, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448081916, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448081918, "dur": 2021, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448083947, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448083951, "dur": 1830, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448085788, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448085793, "dur": 944, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448086797, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448086801, "dur": 140, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448086945, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448086947, "dur": 2346, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448089298, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448089302, "dur": 167, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448089473, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448089475, "dur": 772, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448090272, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448090275, "dur": 637, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448090974, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448091026, "dur": 179, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448091209, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448091211, "dur": 661, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448091877, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448091879, "dur": 617, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448092500, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448092503, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448092587, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448092603, "dur": 743, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448093351, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448093368, "dur": 1210, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448094581, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448094605, "dur": 72, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448094682, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448094685, "dur": 256, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448094944, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448094947, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095010, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095012, "dur": 517, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095533, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095538, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095647, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095649, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095783, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095786, "dur": 191, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095980, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448095982, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448096161, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448096163, "dur": 2062, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448098231, "dur": 3625, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448101879, "dur": 123, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448102006, "dur": 5, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448102013, "dur": 233, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448102250, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448102253, "dur": 130, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448102387, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448102389, "dur": 677, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448103072, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448103075, "dur": 848, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448103930, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448103935, "dur": 368, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448104308, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448104311, "dur": 518, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448104836, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448104839, "dur": 282, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448105127, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448105130, "dur": 724, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448105859, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448105862, "dur": 626, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448106495, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448106498, "dur": 376, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448106879, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448106882, "dur": 214, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448107117, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448107119, "dur": 746, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448107872, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448107876, "dur": 639, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448108521, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448108524, "dur": 323, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448108852, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448108855, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448108922, "dur": 14, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448108938, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109071, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109073, "dur": 77, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109154, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109157, "dur": 98, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109259, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109261, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109332, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109334, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109460, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109462, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109536, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109538, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109640, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109641, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109765, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109767, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109852, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109854, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109990, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448109992, "dur": 514, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448110512, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448110515, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448110610, "dur": 22, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448110637, "dur": 188, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448110830, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448110832, "dur": 1762, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448112602, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448112606, "dur": 152, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448112762, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448112764, "dur": 595, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448113381, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448113385, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448113549, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448113554, "dur": 255489, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448369085, "dur": 4, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448369297, "dur": 103, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448369406, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448369409, "dur": 136, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448369551, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448369553, "dur": 492, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448370051, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448370054, "dur": 196, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448370254, "dur": 1, "ph": "X", "name": "ProcessMessages 4138", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448370561, "dur": 76, "ph": "X", "name": "ReadAsync 4138", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448370642, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448370645, "dur": 81, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448370767, "dur": 5632, "ph": "X", "name": "ProcessMessages 4842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448376406, "dur": 457, "ph": "X", "name": "ReadAsync 4842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448376895, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448376899, "dur": 351, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448377255, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448377258, "dur": 2444, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448379710, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448379714, "dur": 2648, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448382370, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448382375, "dur": 347, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448382726, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448382729, "dur": 1597, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448384331, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448384334, "dur": 1063, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448385404, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448385407, "dur": 1621, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448387050, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448387053, "dur": 978, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448388038, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448388041, "dur": 1458, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448389506, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448389509, "dur": 1965, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448391482, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448391502, "dur": 494, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448392001, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448392005, "dur": 531, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448392540, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448392543, "dur": 899, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448393448, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448393451, "dur": 1638, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448395097, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448395100, "dur": 220, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448395325, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448395328, "dur": 505, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448395837, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448395840, "dur": 1389, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448397234, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448397237, "dur": 722, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448397965, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448397967, "dur": 1007, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448398979, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448398982, "dur": 1260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448400248, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448400251, "dur": 293, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448400549, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448400552, "dur": 1503, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448402063, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448402066, "dur": 1175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448403248, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448403251, "dur": 1455, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448404715, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448404719, "dur": 1404, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406129, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406133, "dur": 99, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406236, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406239, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406366, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406368, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406483, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406485, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406675, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406677, "dur": 152, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406833, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406835, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406937, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448406939, "dur": 344, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448407288, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448407291, "dur": 671, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448407967, "dur": 3, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448407972, "dur": 228, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448408204, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448408208, "dur": 825, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448409039, "dur": 4, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448409044, "dur": 118, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448409166, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448409168, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448409291, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448409293, "dur": 198, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448409496, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448409498, "dur": 1085, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448410590, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448410594, "dur": 108, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448410707, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448410711, "dur": 371, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411088, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411092, "dur": 95, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411191, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411193, "dur": 87, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411286, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411300, "dur": 106, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411410, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411413, "dur": 257, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411674, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411677, "dur": 95, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411776, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448411778, "dur": 353, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448412136, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448412139, "dur": 1177, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448413323, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448413327, "dur": 648, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448413979, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421448413982, "dur": 750507, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449164500, "dur": 35, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449164537, "dur": 5205, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449169749, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449169755, "dur": 425199, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449594964, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449594969, "dur": 76, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449595050, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449595052, "dur": 72, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449595130, "dur": 69, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449595204, "dur": 389, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449595596, "dur": 50, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449595649, "dur": 31, "ph": "X", "name": "ProcessMessages 2629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449595681, "dur": 4737, "ph": "X", "name": "ReadAsync 2629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449600425, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449600430, "dur": 4282, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449604719, "dur": 23, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449604744, "dur": 444, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449605192, "dur": 355, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748421449605550, "dur": 6248, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 70593, "tid": 123, "ts": 1748421449637756, "dur": 4084, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 70593, "tid": 8589934592, "ts": 1748421447633269, "dur": 394766, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748421448028039, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748421448028048, "dur": 5797, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 70593, "tid": 123, "ts": 1748421449641844, "dur": 26, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 70593, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 70593, "tid": 4294967296, "ts": 1748421447566426, "dur": 2048704, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748421447581631, "dur": 42965, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748421449615414, "dur": 11091, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748421449620579, "dur": 3369, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748421449626593, "dur": 17, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 70593, "tid": 123, "ts": 1748421449641873, "dur": 40, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748421447684474, "dur": 60, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421447684886, "dur": 7252, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421447692200, "dur": 41622, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421447734044, "dur": 158, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748421447734204, "dur": 450, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421447734785, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1748421447735410, "dur": 86627, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748421447822671, "dur": 6214, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748421447830232, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748421447832783, "dur": 4019, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748421447842987, "dur": 34199, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748421447877592, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748421447882771, "dur": 33201, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748421447916830, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_7BBE081BF0E8AF4A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748421447916991, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748421447917079, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748421447918762, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_8E9298DC6B12ED57.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748421447920068, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Editor.ref.dll_FD249531A54E376F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748421447926077, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748421447929896, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748421447942505, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748421447953661, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748421447972044, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748421448025875, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748421447734664, "dur": 291267, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421448025946, "dur": 1578990, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421449604941, "dur": 69, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421449605046, "dur": 52, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421449605200, "dur": 90, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421449605355, "dur": 909, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748421447734507, "dur": 291451, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448026033, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748421448026489, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_9909B9D5D2D6D40E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448026642, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448026917, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448027065, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448027413, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448027601, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448027696, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448027810, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448027933, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448028042, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448028166, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448028256, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448028400, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448028455, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448028721, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748421448028879, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748421448029040, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448029100, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748421448029289, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448029381, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748421448029656, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748421448029960, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448030017, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748421448030384, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748421448030882, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448030985, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448031066, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448031240, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448031302, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748421448031505, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448031636, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448031718, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448031860, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748421448032119, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448032252, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448032344, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748421448033048, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448033279, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748421448033684, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448033849, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748421448033931, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448033988, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748421448034301, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448034364, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448034442, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448034502, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448034624, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448034773, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448034838, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448034945, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748421448035501, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448035662, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448037159, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448038966, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448040798, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448042870, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448044325, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448045364, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448046533, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448047695, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448048886, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448050235, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448051506, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448053796, "dur": 705, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/Decal/CreateDecalProjector.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748421448052682, "dur": 2007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448054690, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448055930, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448057092, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448058371, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448059768, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448061654, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448062963, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448064162, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448065684, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448067299, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448068549, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448069831, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448071164, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448072657, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448074045, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448075640, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448076805, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448077884, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448078990, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448080243, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448081941, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448083345, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448083707, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448084714, "dur": 1094, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448085823, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448085901, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448085999, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448086768, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448086838, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448088008, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448088072, "dur": 1275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448089399, "dur": 4146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448093546, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448093618, "dur": 2827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448096446, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448097032, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448097437, "dur": 2318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448099756, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448099974, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448100484, "dur": 2452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448102937, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448103126, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_BC6ADFF987BC82A3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448103200, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448103277, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748421448103886, "dur": 2051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448105938, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448106417, "dur": 1264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448107682, "dur": 2348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448110153, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448111431, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448112584, "dur": 260524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448373111, "dur": 3998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448377110, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448377195, "dur": 5500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448382696, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448382781, "dur": 3880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448386663, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448386750, "dur": 3595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448390346, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448390444, "dur": 3363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448393808, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448393884, "dur": 4047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448397932, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448398043, "dur": 3909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448401992, "dur": 5459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748421448407452, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448407621, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748421448407673, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448407813, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421448407924, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448408093, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421448408156, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448408298, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448408426, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448408536, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448408738, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448409059, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421448409143, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448409235, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748421448409330, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448409482, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748421448409568, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448409662, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421448409730, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448409847, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421448409931, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448410090, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421448410168, "dur": 582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448410764, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448410883, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448411030, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421448411093, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448411303, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448411393, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421448411443, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448411534, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448411629, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448411722, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448411868, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448411990, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448412077, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421448412329, "dur": 1188083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748421449600436, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421449600416, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421449600575, "dur": 4285, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748421449604864, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421447734525, "dur": 291479, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448026023, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748421448026482, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448026804, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_888C5CF0B798DF8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448026934, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448027077, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448027214, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448027344, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448027403, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_57AB718CCA0A791C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448027518, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448027609, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448027686, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448027795, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448027859, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448028058, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448028142, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_51CCB6F491E5150D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448028237, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448028358, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448028486, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448028715, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5041E5295CD553D4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448029047, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448029214, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748421448029420, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448029527, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748421448029779, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448029883, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748421448030448, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748421448030909, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448030999, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748421448031431, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448031573, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748421448031772, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448031826, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748421448032004, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748421448032259, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448032337, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748421448032672, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448032822, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448032961, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448033121, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448033184, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748421448033454, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748421448033573, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448033832, "dur": 7542, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448041374, "dur": 2325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448043699, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448044833, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448045907, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448047167, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448048333, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448049470, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448050970, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448052116, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448053821, "dur": 706, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/GameObjectCreation.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748421448053229, "dur": 2076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448055305, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448056521, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448057733, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448059260, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448060333, "dur": 1785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448062119, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448063343, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448064551, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448066309, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448067702, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448068982, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448070459, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448071746, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448073197, "dur": 1641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448074839, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448076168, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448077283, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448078366, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448079533, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448080930, "dur": 2037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448082969, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448083417, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448083497, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448084231, "dur": 1628, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448085869, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C5B5631EE4DDD44F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448085957, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448087053, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448088223, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448088284, "dur": 5109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448093393, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448093782, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448094532, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448094995, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448095192, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448096098, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448096185, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448096879, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448097076, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448097826, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448098136, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448098691, "dur": 2065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448100757, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448100892, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448101631, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448101947, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448102441, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448102501, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448104126, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448104267, "dur": 1830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448106098, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448106309, "dur": 2095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448108405, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448108550, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448109301, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448109404, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748421448109520, "dur": 3124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448112688, "dur": 260362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448373068, "dur": 3889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448376958, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448377112, "dur": 3477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448380590, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448380674, "dur": 3013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448383688, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448383769, "dur": 4195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448387965, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448388054, "dur": 3555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448391610, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448391725, "dur": 3530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448395256, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448395371, "dur": 3617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448399030, "dur": 5693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448404724, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748421448405195, "dur": 8789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748421448414079, "dur": 1190865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421447734549, "dur": 291468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448026025, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448026747, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37EA32C02CB3EC76.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448026930, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_CAA42EBCF9A9A6C9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448027060, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_37E22A5ED73E2B2F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448027184, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448027262, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448027363, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448027427, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448027582, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448027682, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448027779, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448027873, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448027983, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448028121, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448028202, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448028358, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448028414, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448028645, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448029043, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448029136, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748421448029461, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448029891, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448029992, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748421448030453, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748421448030689, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448030888, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448030963, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448031089, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448031221, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448031739, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448031912, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448032107, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448032248, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448032390, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448032714, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448032886, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448033026, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448033135, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448033267, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448033667, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448033876, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448033939, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448034000, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448034243, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448034327, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448034408, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448034469, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448034524, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448034650, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448035154, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448035295, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748421448035674, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448037143, "dur": 1941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448039084, "dur": 1753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448040838, "dur": 2307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448043145, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448044438, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448045563, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448046781, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448047973, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448049095, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448050493, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448051709, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448053812, "dur": 726, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShapeEditor/Selection/SerializableSelection.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748421448052892, "dur": 2243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448055136, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448056378, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448057556, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448059018, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448060127, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448061825, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448063098, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448064340, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448065936, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448067482, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448068736, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448070134, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448071360, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448072884, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448074312, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448075849, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448076964, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448078057, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448079181, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448080464, "dur": 1989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448082455, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448083452, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448083561, "dur": 9215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748421448092777, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448093236, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448093693, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748421448094478, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448094637, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448094825, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448094896, "dur": 1977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748421448096874, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448097147, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748421448097976, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448098207, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448098383, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448098565, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448098893, "dur": 13757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748421448112752, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748421448112927, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748421448113553, "dur": 71, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421448113659, "dur": 1050969, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748421449166140, "dur": 639, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748421449165883, "dur": 2854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748421449169546, "dur": 287, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421449595027, "dur": 448, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748421449169861, "dur": 425631, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748421449600422, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748421449600408, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748421449600600, "dur": 4288, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748421447734560, "dur": 291498, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448026069, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448026621, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448026908, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448027073, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448027220, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_72608D0D80FBEA70.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448027373, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448027442, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448027574, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448027685, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448027755, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448027850, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448027953, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448028102, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448028178, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448028267, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448028386, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448028508, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748421448028868, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448029449, "dur": 9524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448038974, "dur": 815, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448039840, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448040078, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448040195, "dur": 4085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448044281, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448044347, "dur": 23155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448067503, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448067718, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448067804, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448067913, "dur": 3189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448071103, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448071170, "dur": 10212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448081383, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448081756, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448081849, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448081972, "dur": 1435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448083460, "dur": 3239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448086700, "dur": 889, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448087694, "dur": 1329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448089077, "dur": 1962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448091040, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448091326, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448092804, "dur": 1280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448094084, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448094508, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448094803, "dur": 1018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448095822, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448095986, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448096690, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448097010, "dur": 4043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448101054, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448101220, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448101681, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448102302, "dur": 2415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448104718, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448104861, "dur": 1819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448106681, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448106872, "dur": 2324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448109197, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448109395, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748421448109606, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448109796, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448109935, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448110050, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448110151, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448110677, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448112125, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448112189, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448112583, "dur": 260473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448373065, "dur": 5010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448378076, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448378196, "dur": 6530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448384727, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448384844, "dur": 3896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448388741, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448388871, "dur": 3365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448392237, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448392320, "dur": 4193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448396514, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448396600, "dur": 3783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448400384, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448400476, "dur": 5429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748421448405906, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448406071, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448406466, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448406718, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448406859, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448407037, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448407167, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748421448407221, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448407586, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448407751, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448407819, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748421448407874, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448408086, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748421448408157, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448408314, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448408404, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748421448408482, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448408723, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448408909, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448409013, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748421448409086, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448409208, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448409415, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748421448409471, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448409601, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448409742, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748421448409811, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448410006, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748421448410063, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448410526, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748421448410800, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448410920, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448411035, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448411254, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748421448411327, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448411511, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448411587, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748421448411650, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448411858, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448411998, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448412099, "dur": 1383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748421448413484, "dur": 1191457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421447734571, "dur": 291496, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448026072, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448026654, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_25D10CC8709418E4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448026738, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A49EDD7A339F9A85.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448026823, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448026894, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448026956, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448027164, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B66FF45CB39FB62A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448027228, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448027334, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AB6C0CE8644C7690.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448027464, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448027548, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448027628, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448027731, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448027815, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448027966, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448028075, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448028183, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448028248, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448028448, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448028555, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448029253, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448029356, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448029630, "dur": 8527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448038159, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448038602, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448038689, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448038825, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448040456, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448040625, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748421448040770, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448040863, "dur": 2121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448042984, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448044373, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448045471, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448046678, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448047833, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448049020, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448050358, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448051573, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448053768, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/Camera/UniversalRenderPipelineCameraUI.Rendering.Drawers.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748421448052756, "dur": 1981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448054738, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448055942, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448057127, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448058509, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448059870, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448061668, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448062993, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448064195, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448065803, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448067353, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448068564, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448069861, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448071189, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448072734, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448074181, "dur": 1573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448075755, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448076890, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448077964, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448079055, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448080312, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448082094, "dur": 1313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448083473, "dur": 6805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448090279, "dur": 628, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448090954, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448091027, "dur": 921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448091989, "dur": 2219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448094209, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448094694, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748421448094907, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448095673, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448095798, "dur": 1672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748421448097519, "dur": 195, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448368934, "dur": 1959, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448098135, "dur": 272838, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748421448373050, "dur": 3091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448376143, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448376254, "dur": 744, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448377002, "dur": 5446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448382449, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448382557, "dur": 4266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448386824, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448386936, "dur": 4755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448391692, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448391824, "dur": 3872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448395697, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448395762, "dur": 3934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448399696, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448399813, "dur": 3411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448403226, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448403405, "dur": 10570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748421448413976, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748421448414084, "dur": 1190837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421447734618, "dur": 291460, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448026084, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448026691, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448027048, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448027151, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_EBB973DC47E5C128.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448027240, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448027344, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448027431, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448027525, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448027636, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448027740, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448027864, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448027972, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448028116, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448028210, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448028405, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448028512, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748421448028997, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448029268, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748421448029461, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448029563, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748421448029956, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448030013, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748421448030601, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748421448030795, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448030927, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448031263, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748421448031938, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448032045, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448032170, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448032266, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748421448032652, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448032757, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448032922, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448033064, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448033335, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748421448033664, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448033927, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034056, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034177, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034271, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034349, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034420, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034489, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034579, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034744, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034810, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448034887, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448035093, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748421448035420, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448035489, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448036179, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/InputManager.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748421448037098, "dur": 694, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/DataManager.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748421448037793, "dur": 774, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/AudioManager.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748421448035644, "dur": 5199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448040843, "dur": 2251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448043095, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448044465, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448045595, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448046833, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448048018, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448049145, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448050569, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448051792, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448053785, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShapeEditor/GUIFramework/Control.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748421448052989, "dur": 2285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448055275, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448056468, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448057675, "dur": 1490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448059165, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448060245, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448061959, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448063202, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448064407, "dur": 1641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448066048, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448067544, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448068783, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448070199, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448071433, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448072928, "dur": 1490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448074419, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448075940, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448077020, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448078135, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448079275, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448080526, "dur": 1969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448082497, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448083589, "dur": 5374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448088964, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448089445, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_A298F0F0964CDA5C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448089549, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448090361, "dur": 2063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448092425, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448092742, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448093631, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448093897, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448095150, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448095397, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448095573, "dur": 4163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448099737, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448099949, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448100507, "dur": 4030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448104538, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448104958, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Editor.ref.dll_7BE565C70AE586E5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448105079, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748421448105266, "dur": 1651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448106918, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448107185, "dur": 2326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448109512, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448109723, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448109813, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448109918, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448110003, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448110158, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448111516, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448111667, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448111874, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448112574, "dur": 260502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448373084, "dur": 3043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448376129, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448376236, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448376350, "dur": 3495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448379846, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448380130, "dur": 4311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448384442, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448384536, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448388138, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448388210, "dur": 4149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448392391, "dur": 3508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448395900, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448395973, "dur": 3363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448399397, "dur": 3901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448403299, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448403442, "dur": 9472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748421448412915, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748421448413477, "dur": 1191438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421447734648, "dur": 291439, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448026093, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448026713, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448026921, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448027037, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448027102, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_2EC518F8CEEB6CA9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448027177, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448027243, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448027389, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD8CC2F62B9395CF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448027473, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448027532, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448027616, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448027704, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448027810, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448027872, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448028050, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448028130, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448028217, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448028335, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448028479, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748421448028796, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448029206, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448029362, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448029516, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448029883, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448030075, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448030462, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748421448030695, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448030900, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448030995, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448031412, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448031551, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448031651, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448031751, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448031839, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748421448032099, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448032161, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448032240, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448032553, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748421448032632, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448032710, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448032870, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448032990, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448033106, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448033440, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448033718, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448033867, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448034032, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448034253, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748421448034316, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448034384, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448034456, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448034536, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448034632, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448034763, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448034901, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448034970, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748421448036392, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Utils/ObjectPool.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748421448038202, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/UI/EnemyHealthBar.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748421448040149, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Enemies/EnemyController.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748421448035622, "dur": 5534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448041157, "dur": 2335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448043493, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448044607, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448045725, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448046967, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448048165, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448049283, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448050737, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448051898, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448053816, "dur": 716, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShapeEditor/EditablePath/EditablePath.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748421448053095, "dur": 2198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448055294, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448056507, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448057756, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448059450, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448060522, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448062346, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448063538, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448064810, "dur": 1749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448066560, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448067914, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448068002, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448068092, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448069324, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448070740, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448072203, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448073539, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448075126, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448076402, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448077526, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448078633, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448079790, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448081288, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448083097, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448083677, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448084735, "dur": 980, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448085730, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448085834, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448086745, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448086807, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448087846, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448087911, "dur": 1356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448089313, "dur": 2875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448092189, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448092580, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448093519, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448093815, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448094323, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448094668, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448094851, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448095101, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448095888, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448096206, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448096391, "dur": 2497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448098889, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448099322, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448099651, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448100347, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448100407, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448100957, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448101502, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448101794, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448102331, "dur": 3311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448105642, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448105827, "dur": 1924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448107752, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448107886, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448110393, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448110633, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_AD2FFB7646FD92B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448110751, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448112188, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448112315, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448112567, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448112757, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748421448112885, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448113470, "dur": 259662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448373135, "dur": 6717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448379853, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448380082, "dur": 3902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448383986, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448384074, "dur": 3888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448387963, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448388040, "dur": 5236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448393277, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448393450, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448393515, "dur": 3865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448397417, "dur": 3179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448400597, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448400702, "dur": 6255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748421448406958, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448407197, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448407495, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748421448407569, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448407714, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748421448407768, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448407924, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748421448408005, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448408171, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448408383, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448408508, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748421448408584, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748421448408650, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448408770, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448409028, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748421448409103, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448409274, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448409359, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748421448409427, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448409620, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448409787, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448409948, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448410034, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448410144, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448410236, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448410318, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748421448410790, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448410883, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748421448410955, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448411061, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448411181, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748421448411262, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448411416, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748421448411479, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448411617, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748421448411713, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448411781, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748421448411939, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448412077, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448412209, "dur": 1917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748421448414127, "dur": 1190834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421447734659, "dur": 291439, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448026100, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448026664, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1996541149DA13E0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448026816, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D0749362471C98A9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448026940, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_C9DCA0B70AE22DB0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448027037, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448027094, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3C55960BA9CB8745.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448027229, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_E69ADDE2B61683F5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448027394, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B92AE864B27146B1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448027490, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448027593, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448027691, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448027801, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448027942, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448028058, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448028237, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448028663, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448028979, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448029086, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448029326, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448029440, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448029555, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748421448029887, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448030368, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748421448030729, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748421448030914, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448031032, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448031153, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448031236, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748421448031448, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448031633, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448031731, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448031896, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448032155, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448032331, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448032740, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448032870, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448033031, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448033146, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448033632, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448033882, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448034219, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448034375, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448034435, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448034499, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448034613, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448034788, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448034910, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448035186, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748421448035666, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448037232, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448039068, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448040910, "dur": 2164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448043075, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448044408, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448045545, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448046775, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448047948, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448049072, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448050470, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448051701, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448053804, "dur": 744, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/AssetPostProcessors/FBXArnoldSurfaceMaterialDescriptionPreprocessor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748421448052888, "dur": 2021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448054909, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448056137, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448057288, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448058678, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448059948, "dur": 1732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448061681, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448063009, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448064207, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448065814, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448067394, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448068651, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448070021, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448071320, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448072841, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448074269, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448075841, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448076972, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448078044, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448079189, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448080440, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448082325, "dur": 1214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448083540, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448083660, "dur": 7933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448091594, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448092190, "dur": 1203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448093394, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448093458, "dur": 2376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448095835, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448095969, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448096254, "dur": 1633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448097888, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448098079, "dur": 2347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448100427, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448100822, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448101512, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448101761, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448102392, "dur": 1720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448104113, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448104278, "dur": 1332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448105611, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448105794, "dur": 4784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448110579, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448110799, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748421448110926, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448112574, "dur": 260491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448373072, "dur": 4349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448377422, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448377550, "dur": 3265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448380816, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448380896, "dur": 3837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448384734, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448384811, "dur": 3891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448388703, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448388788, "dur": 3794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448392635, "dur": 4644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448397280, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448397364, "dur": 4777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448402142, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448402444, "dur": 9697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748421448412142, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448412268, "dur": 1864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748421448414133, "dur": 1190817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748421449609996, "dur": 1412, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 70593, "tid": 123, "ts": 1748421449645140, "dur": 2558, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 70593, "tid": 123, "ts": 1748421449647800, "dur": 2645, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 70593, "tid": 123, "ts": 1748421449634887, "dur": 16704, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}