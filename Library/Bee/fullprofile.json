{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 70122, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 70122, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 70122, "tid": 42, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 70122, "tid": 42, "ts": 1748418664687104, "dur": 678, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 70122, "tid": 42, "ts": 1748418664719351, "dur": 5713, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 70122, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 70122, "tid": 1, "ts": 1748418663085976, "dur": 12407, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70122, "tid": 1, "ts": 1748418663098390, "dur": 149258, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70122, "tid": 1, "ts": 1748418663247662, "dur": 449504, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 70122, "tid": 42, "ts": 1748418664725074, "dur": 68, "ph": "X", "name": "", "args": {}}, {"pid": 70122, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663082933, "dur": 25658, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663108595, "dur": 1559266, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663110502, "dur": 7887, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663118398, "dur": 2308, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663120953, "dur": 91302, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663212317, "dur": 814, "ph": "X", "name": "ProcessMessages 8118", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663213146, "dur": 96, "ph": "X", "name": "ReadAsync 8118", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663213287, "dur": 7, "ph": "X", "name": "ProcessMessages 6274", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663213296, "dur": 112, "ph": "X", "name": "ReadAsync 6274", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663213412, "dur": 2, "ph": "X", "name": "ProcessMessages 1455", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663213416, "dur": 55, "ph": "X", "name": "ReadAsync 1455", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663213492, "dur": 36, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663213531, "dur": 1343, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663214879, "dur": 19, "ph": "X", "name": "ProcessMessages 8156", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663214903, "dur": 85, "ph": "X", "name": "ReadAsync 8156", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663214993, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663214996, "dur": 5479, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663220542, "dur": 11, "ph": "X", "name": "ProcessMessages 8122", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663220556, "dur": 1146, "ph": "X", "name": "ReadAsync 8122", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663221709, "dur": 13, "ph": "X", "name": "ProcessMessages 8171", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663221723, "dur": 368, "ph": "X", "name": "ReadAsync 8171", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663222113, "dur": 6, "ph": "X", "name": "ProcessMessages 3904", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663222120, "dur": 555, "ph": "X", "name": "ReadAsync 3904", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663222734, "dur": 310, "ph": "X", "name": "ProcessMessages 2685", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663224059, "dur": 154, "ph": "X", "name": "ReadAsync 2685", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663224241, "dur": 6, "ph": "X", "name": "ProcessMessages 4279", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663224250, "dur": 108, "ph": "X", "name": "ReadAsync 4279", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663224363, "dur": 2, "ph": "X", "name": "ProcessMessages 1183", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663224366, "dur": 4003, "ph": "X", "name": "ReadAsync 1183", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663228376, "dur": 11, "ph": "X", "name": "ProcessMessages 8183", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663228450, "dur": 145, "ph": "X", "name": "ReadAsync 8183", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663228599, "dur": 3, "ph": "X", "name": "ProcessMessages 1498", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663228603, "dur": 107, "ph": "X", "name": "ReadAsync 1498", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663228778, "dur": 2, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663228782, "dur": 89, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663229146, "dur": 36, "ph": "X", "name": "ProcessMessages 1177", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663229187, "dur": 184, "ph": "X", "name": "ReadAsync 1177", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663229376, "dur": 4, "ph": "X", "name": "ProcessMessages 2766", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663229381, "dur": 423, "ph": "X", "name": "ReadAsync 2766", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663229809, "dur": 3, "ph": "X", "name": "ProcessMessages 1805", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663229813, "dur": 120, "ph": "X", "name": "ReadAsync 1805", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663229953, "dur": 3, "ph": "X", "name": "ProcessMessages 2492", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663229958, "dur": 71, "ph": "X", "name": "ReadAsync 2492", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663230032, "dur": 1, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663230035, "dur": 1874, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663231915, "dur": 3, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663231935, "dur": 191, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663232130, "dur": 10, "ph": "X", "name": "ProcessMessages 8133", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663232859, "dur": 479, "ph": "X", "name": "ReadAsync 8133", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663233343, "dur": 6, "ph": "X", "name": "ProcessMessages 3267", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663233466, "dur": 322, "ph": "X", "name": "ReadAsync 3267", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663233792, "dur": 4, "ph": "X", "name": "ProcessMessages 2639", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663233798, "dur": 164, "ph": "X", "name": "ReadAsync 2639", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663234007, "dur": 3, "ph": "X", "name": "ProcessMessages 1233", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663234011, "dur": 165, "ph": "X", "name": "ReadAsync 1233", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663234273, "dur": 2, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663234331, "dur": 70427, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663305176, "dur": 13, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663305195, "dur": 259, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663305940, "dur": 11, "ph": "X", "name": "ProcessMessages 8103", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663306183, "dur": 554, "ph": "X", "name": "ReadAsync 8103", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663306743, "dur": 36, "ph": "X", "name": "ProcessMessages 4777", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663306780, "dur": 267, "ph": "X", "name": "ReadAsync 4777", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663307053, "dur": 7, "ph": "X", "name": "ProcessMessages 4747", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663307061, "dur": 64, "ph": "X", "name": "ReadAsync 4747", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663307129, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663307132, "dur": 153, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663307290, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663307293, "dur": 99, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663359453, "dur": 6, "ph": "X", "name": "ProcessMessages 1335", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663360090, "dur": 125, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663360218, "dur": 9, "ph": "X", "name": "ProcessMessages 8188", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663360229, "dur": 209, "ph": "X", "name": "ReadAsync 8188", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663360642, "dur": 2, "ph": "X", "name": "ProcessMessages 1438", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663360645, "dur": 188, "ph": "X", "name": "ReadAsync 1438", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663360837, "dur": 3, "ph": "X", "name": "ProcessMessages 1897", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663360856, "dur": 208, "ph": "X", "name": "ReadAsync 1897", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663361067, "dur": 4, "ph": "X", "name": "ProcessMessages 2589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663361073, "dur": 54, "ph": "X", "name": "ReadAsync 2589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663361130, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663361133, "dur": 54, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663361190, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663361193, "dur": 1220, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663362418, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663362421, "dur": 20581, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663383174, "dur": 6, "ph": "X", "name": "ProcessMessages 4104", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385036, "dur": 121, "ph": "X", "name": "ReadAsync 4104", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385160, "dur": 8, "ph": "X", "name": "ProcessMessages 8157", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385169, "dur": 115, "ph": "X", "name": "ReadAsync 8157", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385288, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385290, "dur": 54, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385348, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385350, "dur": 70, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385424, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385427, "dur": 152, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385583, "dur": 1, "ph": "X", "name": "ProcessMessages 1065", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385585, "dur": 84, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385673, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385676, "dur": 109, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385788, "dur": 2, "ph": "X", "name": "ProcessMessages 1425", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385791, "dur": 167, "ph": "X", "name": "ReadAsync 1425", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385975, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663385978, "dur": 100, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663386081, "dur": 5, "ph": "X", "name": "ProcessMessages 1977", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663386087, "dur": 71, "ph": "X", "name": "ReadAsync 1977", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663387440, "dur": 2, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663387443, "dur": 74, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663388182, "dur": 11, "ph": "X", "name": "ProcessMessages 8159", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663388212, "dur": 58, "ph": "X", "name": "ReadAsync 8159", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663388275, "dur": 7, "ph": "X", "name": "ProcessMessages 5948", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663388284, "dur": 674, "ph": "X", "name": "ReadAsync 5948", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663389022, "dur": 2, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663389026, "dur": 60, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663389090, "dur": 4, "ph": "X", "name": "ProcessMessages 2672", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663389096, "dur": 1181, "ph": "X", "name": "ReadAsync 2672", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663390281, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663390283, "dur": 299, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663390587, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663390588, "dur": 504, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663391095, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663391098, "dur": 845, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663391947, "dur": 2, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663391950, "dur": 1012, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663392966, "dur": 2, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663392970, "dur": 837, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663393813, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663393816, "dur": 764, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663394585, "dur": 2, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663394589, "dur": 915, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663395509, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663395513, "dur": 328, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663395846, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663395848, "dur": 797, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663396651, "dur": 2, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663396654, "dur": 1369, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663398029, "dur": 3, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663398033, "dur": 852, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663398890, "dur": 2, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663398894, "dur": 365, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663399262, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663399317, "dur": 2780, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663402103, "dur": 4, "ph": "X", "name": "ProcessMessages 2315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663402109, "dur": 599, "ph": "X", "name": "ReadAsync 2315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663402712, "dur": 2, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663402716, "dur": 816, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663403557, "dur": 2, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663403560, "dur": 767, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663404338, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663404341, "dur": 63, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663404409, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663404411, "dur": 1364, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663405787, "dur": 663, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663406456, "dur": 260595, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663667062, "dur": 7, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663667072, "dur": 197, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663667275, "dur": 13, "ph": "X", "name": "ProcessMessages 8170", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663667290, "dur": 106, "ph": "X", "name": "ReadAsync 8170", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663667402, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663667406, "dur": 1099, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663668512, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663668515, "dur": 432, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663668956, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663668960, "dur": 583, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663669548, "dur": 2, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663669553, "dur": 1590, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663671169, "dur": 2, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663671173, "dur": 612, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663671807, "dur": 2, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663671811, "dur": 726, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663672544, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663672548, "dur": 253, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663672805, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663672809, "dur": 318, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663673132, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663673135, "dur": 818, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663673974, "dur": 2, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663673984, "dur": 1233, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663675222, "dur": 2, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663675225, "dur": 996, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663676227, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663676232, "dur": 209, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663676446, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663676449, "dur": 1886, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663678340, "dur": 3, "ph": "X", "name": "ProcessMessages 1631", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663678344, "dur": 1112, "ph": "X", "name": "ReadAsync 1631", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663679461, "dur": 2, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663679464, "dur": 778, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663680245, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663680265, "dur": 366, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663680635, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663680638, "dur": 401, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663681090, "dur": 2, "ph": "X", "name": "ProcessMessages 1289", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663681093, "dur": 899, "ph": "X", "name": "ReadAsync 1289", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663681997, "dur": 2, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663682000, "dur": 790, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663682793, "dur": 2, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663682796, "dur": 1457, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663684262, "dur": 2, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663684266, "dur": 560, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663684830, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663684833, "dur": 342, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663685179, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663685182, "dur": 977, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663686164, "dur": 3, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663686169, "dur": 748, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663686921, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663686924, "dur": 1043, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663687971, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663687973, "dur": 286, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663688262, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663688263, "dur": 757, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663689024, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663689027, "dur": 368, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663689397, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663689399, "dur": 540, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663689942, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663689944, "dur": 33, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663689981, "dur": 705, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663690691, "dur": 2, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663690693, "dur": 637, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663691333, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663691335, "dur": 39, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663691376, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663691378, "dur": 37, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663691418, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663691420, "dur": 69, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663691494, "dur": 3, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663691499, "dur": 740, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663692243, "dur": 1, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663692245, "dur": 678, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663692928, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663692931, "dur": 337, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663693271, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663693273, "dur": 525, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663693803, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663693806, "dur": 50, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663693859, "dur": 799, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663694662, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663694664, "dur": 33, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663694701, "dur": 754, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695459, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695462, "dur": 43, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695508, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695510, "dur": 51, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695564, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695566, "dur": 35, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695604, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695609, "dur": 41, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695653, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663695654, "dur": 707, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663696370, "dur": 436, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663696809, "dur": 491, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697303, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697375, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697381, "dur": 79, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697465, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697471, "dur": 110, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697585, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697588, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697650, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697652, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697720, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697722, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697801, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697803, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697892, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697895, "dur": 65, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697970, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663697972, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698041, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698044, "dur": 199, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698248, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698250, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698308, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698310, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698390, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698392, "dur": 51, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698448, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698450, "dur": 207, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698661, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698663, "dur": 148, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698814, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698817, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698889, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698892, "dur": 50, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698946, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663698953, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699024, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699027, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699086, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699089, "dur": 117, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699210, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699212, "dur": 51, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699267, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699270, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699324, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699327, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699388, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699390, "dur": 62, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699471, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699474, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699538, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699540, "dur": 66, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699610, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699613, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699679, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699681, "dur": 145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699830, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699834, "dur": 69, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699906, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699908, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663699999, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700002, "dur": 156, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700162, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700165, "dur": 181, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700359, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700362, "dur": 71, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700437, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700439, "dur": 196, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700640, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700643, "dur": 152, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700798, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700801, "dur": 145, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700951, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663700954, "dur": 100, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701058, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701061, "dur": 91, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701156, "dur": 9, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701187, "dur": 227, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701418, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701420, "dur": 70, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701495, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701498, "dur": 231, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701734, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701737, "dur": 58, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701798, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701800, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701882, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701884, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701982, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663701985, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702051, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702054, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702126, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702128, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702190, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702192, "dur": 189, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702385, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702387, "dur": 163, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702558, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702561, "dur": 89, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702653, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702655, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702716, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702722, "dur": 67, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702792, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702795, "dur": 72, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702871, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702873, "dur": 75, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702953, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663702956, "dur": 61, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703021, "dur": 13, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703037, "dur": 79, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703120, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703122, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703186, "dur": 68, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703295, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703379, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703382, "dur": 105, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703510, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703512, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703612, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703614, "dur": 108, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703754, "dur": 8, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703765, "dur": 67, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703838, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663703840, "dur": 184, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704028, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704031, "dur": 62, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704097, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704099, "dur": 103, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704214, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704217, "dur": 74, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704296, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704299, "dur": 51, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704354, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704357, "dur": 48, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704416, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704418, "dur": 95, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704516, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704519, "dur": 109, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704632, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704635, "dur": 79, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704729, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704751, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704807, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704809, "dur": 89, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704902, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704904, "dur": 49, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704956, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663704959, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663705051, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663705053, "dur": 70, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663705128, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663705130, "dur": 107, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663705242, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663705244, "dur": 3857, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663709112, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663709115, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663709282, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663709285, "dur": 2509, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663711801, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663711805, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663711974, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663711976, "dur": 684, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663712665, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663712668, "dur": 4592, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663717267, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663717272, "dur": 29425, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663746707, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663746711, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663746821, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663746824, "dur": 996, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663747825, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663747828, "dur": 3758, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663751595, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663751599, "dur": 13204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663764815, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663764821, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663764982, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663764985, "dur": 704, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663765696, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663765699, "dur": 117, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663765822, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663765824, "dur": 355, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663766186, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663766188, "dur": 635, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663766830, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663766834, "dur": 624, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663767464, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663767467, "dur": 551, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663768047, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663768050, "dur": 639, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663768694, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663768697, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663768875, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663768878, "dur": 476, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663769359, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663769361, "dur": 1121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663770490, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663770591, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663770721, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663770724, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663770815, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663770818, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663771055, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663771057, "dur": 321, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663771383, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663771399, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663771520, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663771522, "dur": 521, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663772049, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663772051, "dur": 777, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663772843, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663772847, "dur": 483, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663773335, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663773339, "dur": 239, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663773581, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663773583, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663773728, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663773731, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663773881, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663773883, "dur": 446, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663774333, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663774336, "dur": 818, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663775160, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663775164, "dur": 198, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663775368, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663775371, "dur": 260, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663775635, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663775638, "dur": 162, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663775805, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663775807, "dur": 788, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663776601, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663776604, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663776699, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663776701, "dur": 76, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663776780, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663776783, "dur": 311, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663777098, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663777100, "dur": 664, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663777770, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663777803, "dur": 502, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663778326, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663778329, "dur": 402, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663778736, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663778739, "dur": 284, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663779028, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663779030, "dur": 516, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663779551, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663779553, "dur": 345, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663779902, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663779904, "dur": 163, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780071, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780073, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780282, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780284, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780364, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780366, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780537, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780539, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780670, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780672, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780831, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780834, "dur": 122, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780959, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663780962, "dur": 679, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663781646, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663781648, "dur": 291, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663781945, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663781947, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663782063, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663782065, "dur": 845, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663782916, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663782919, "dur": 449, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663783373, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663783377, "dur": 475, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663783875, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663783878, "dur": 132, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784015, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784017, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784131, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784133, "dur": 382, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784520, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784522, "dur": 146, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784672, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784675, "dur": 193, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784871, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784874, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784952, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663784954, "dur": 1275, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663786235, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663786238, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663786332, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663786334, "dur": 352, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663786690, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663786692, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663786765, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663786959, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663786962, "dur": 376, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663787342, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663787345, "dur": 1061, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663788411, "dur": 1069, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663789487, "dur": 121, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663789612, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663789615, "dur": 300, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663789920, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663789922, "dur": 686, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663790624, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663790628, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663790709, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663790711, "dur": 428, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791145, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791148, "dur": 250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791403, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791407, "dur": 334, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791746, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791749, "dur": 127, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791881, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791883, "dur": 60, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791948, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663791950, "dur": 160, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663792115, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663792117, "dur": 2798, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663794932, "dur": 7, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663794941, "dur": 2900, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663797848, "dur": 10, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663797860, "dur": 152, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663798017, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663798020, "dur": 839, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663798868, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663798872, "dur": 440, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663799318, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418663799322, "dur": 209935, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664009268, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664009274, "dur": 100, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664009380, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664009383, "dur": 72, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664009461, "dur": 74, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664009540, "dur": 68, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664009864, "dur": 61, "ph": "X", "name": "ReadAsync 4138", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664009938, "dur": 11, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664009953, "dur": 65, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664010069, "dur": 3409, "ph": "X", "name": "ProcessMessages 4842", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664013486, "dur": 3040, "ph": "X", "name": "ReadAsync 4842", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664016534, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664016538, "dur": 413, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664016956, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664016959, "dur": 1906, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664018872, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664018876, "dur": 1274, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664020157, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664020161, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664020236, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664020238, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664020310, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664020312, "dur": 884, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664021202, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664021205, "dur": 2080, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664023333, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664023337, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664023527, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664023530, "dur": 1220, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664024755, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664024758, "dur": 307, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664025069, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664025071, "dur": 2256, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664027334, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664027337, "dur": 1479, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664028824, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664028828, "dur": 1335, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664030171, "dur": 11, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664030184, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664030312, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664030314, "dur": 2156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664032476, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664032480, "dur": 171, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664032656, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664032659, "dur": 2752, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664035419, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664035423, "dur": 662, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664036090, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664036093, "dur": 490, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664036587, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664036589, "dur": 1258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664037855, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664037858, "dur": 1150, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664039015, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664039018, "dur": 882, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664039905, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664039907, "dur": 2486, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664042399, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664042403, "dur": 251, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664042658, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664042662, "dur": 98, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664042765, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664042767, "dur": 194, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664042966, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664042969, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043076, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043079, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043168, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043170, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043320, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043323, "dur": 100, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043427, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043429, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043499, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043502, "dur": 115, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043621, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043623, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043703, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043705, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043789, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043811, "dur": 118, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043933, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664043935, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044032, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044035, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044136, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044138, "dur": 145, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044288, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044291, "dur": 58, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044353, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044356, "dur": 258, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044618, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044621, "dur": 155, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044799, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044805, "dur": 117, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044926, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664044928, "dur": 246, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045180, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045183, "dur": 172, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045359, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045361, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045544, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045547, "dur": 225, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045777, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045780, "dur": 103, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045887, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664045890, "dur": 147, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046041, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046043, "dur": 99, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046146, "dur": 21, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046170, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046283, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046286, "dur": 66, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046356, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046358, "dur": 79, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046441, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046443, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664046496, "dur": 1143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664047646, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664047649, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664047794, "dur": 12, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664047810, "dur": 208, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664048025, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664048027, "dur": 285556, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664333592, "dur": 38, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664333644, "dur": 4577, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664338227, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664338231, "dur": 315857, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664654100, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664654105, "dur": 97, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664654207, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664654210, "dur": 83, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664654298, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664654300, "dur": 61, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664654384, "dur": 83, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664654496, "dur": 23, "ph": "X", "name": "ProcessMessages 2629", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664654522, "dur": 5074, "ph": "X", "name": "ReadAsync 2629", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664659613, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664659618, "dur": 607, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664660232, "dur": 53, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664660287, "dur": 526, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664660818, "dur": 454, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418664661277, "dur": 6536, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 70122, "tid": 42, "ts": 1748418664725144, "dur": 4087, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 70122, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 70122, "tid": 8589934592, "ts": 1748418663077974, "dur": 619262, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 70122, "tid": 8589934592, "ts": 1748418663697246, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 70122, "tid": 8589934592, "ts": 1748418663697257, "dur": 4235, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 70122, "tid": 42, "ts": 1748418664729233, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 70122, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418663007246, "dur": 1663418, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418663028063, "dur": 41364, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418664671214, "dur": 13299, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418664675359, "dur": 3916, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418664684604, "dur": 23, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 70122, "tid": 42, "ts": 1748418664729242, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748418663097486, "dur": 6629, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418663104155, "dur": 106711, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418663211007, "dur": 100, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748418663211108, "dur": 276, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418663212372, "dur": 708, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663213086, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_37E22A5ED73E2B2F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663213153, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663213275, "dur": 137, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663213416, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663214933, "dur": 805, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_51CCB6F491E5150D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663215785, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663216204, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37EA32C02CB3EC76.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663216547, "dur": 167, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663216718, "dur": 137, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663216893, "dur": 142, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663217044, "dur": 130, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748418663217182, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748418663217366, "dur": 174, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663219565, "dur": 160, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_E711E874CC38399E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663219730, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_0118987859331F44.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663219869, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_58CF8A58239EF61C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663220009, "dur": 478, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_F0DEE4EE0970DD2B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663220523, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663221211, "dur": 418, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663222018, "dur": 489, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663224011, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663224612, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748418663225676, "dur": 225, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748418663226089, "dur": 152, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663226740, "dur": 235, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748418663227829, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748418663228007, "dur": 224, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748418663228319, "dur": 892, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663232287, "dur": 592, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663233240, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663234571, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663234653, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748418663235018, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748418663235196, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D33AAA747FCEED0A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663235264, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748418663235631, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748418663236408, "dur": 69901, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663306767, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663307164, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748418663308561, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663309376, "dur": 51686, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663363141, "dur": 273, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748418663363710, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748418663363774, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663363938, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663364055, "dur": 21956, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663387884, "dur": 518, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418663388409, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663389317, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418663400835, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748418663401376, "dur": 138, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748418663402085, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748418663405128, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748418663413622, "dur": 254494, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748418663670205, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748418663211393, "dur": 485778, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418663697184, "dur": 963878, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418664661245, "dur": 66, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418664661318, "dur": 169, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418664661559, "dur": 1285, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748418663211293, "dur": 485903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663697230, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748418663697621, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_9909B9D5D2D6D40E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663697732, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663697984, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663698060, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663698363, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663698437, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B92AE864B27146B1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663698579, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663698659, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663698758, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663698845, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663698958, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663699034, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663699137, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663699194, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663699359, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663699452, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663699553, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663699754, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418663700141, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663700303, "dur": 9178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418663709482, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663709790, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663709879, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663709989, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663711707, "dur": 1880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663713588, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663715521, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663717041, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663718843, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663720611, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663722258, "dur": 2040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663724299, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663726233, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663727907, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.State/TriggerStateTransition.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748418663727907, "dur": 2559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663730466, "dur": 2006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663732472, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663733915, "dur": 1605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663735521, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663736942, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663738814, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663740459, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663741944, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663743482, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663745176, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663746766, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663748339, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748418663748390, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663748538, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663750010, "dur": 1691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663751702, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663753682, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663755076, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663756632, "dur": 1805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663758438, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663760286, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663761891, "dur": 1821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663763712, "dur": 2199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663765912, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663766358, "dur": 9022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418663775381, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663775832, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663776042, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418663778309, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663778706, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_CB2B1FA2A63FF8D9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663778759, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663779131, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418663779549, "dur": 2875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418663782425, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663782658, "dur": 5287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748418663788024, "dur": 750, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418664009876, "dur": 912, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418663789258, "dur": 221604, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748418664013456, "dur": 4226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418664017691, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418664017796, "dur": 3112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418664020909, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418664021004, "dur": 3019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418664024024, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418664024149, "dur": 3649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418664027799, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418664027867, "dur": 4358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418664032259, "dur": 3891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418664036210, "dur": 3069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418664039314, "dur": 3403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418664042761, "dur": 5944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418664048790, "dur": 612236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663211302, "dur": 485920, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663697231, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418663697615, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663697861, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D0749362471C98A9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663697929, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663698024, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_CAA42EBCF9A9A6C9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663698179, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3C55960BA9CB8745.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663698355, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663698483, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663698600, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663698720, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663698782, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663698916, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663698985, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663699099, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663699166, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663699260, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663699393, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663699496, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663699595, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663699914, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748418663700205, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418663700467, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748418663700820, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418663701089, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418663701247, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663701430, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418663701491, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663701601, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663701713, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418663701865, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663701973, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663702093, "dur": 5356, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663707449, "dur": 2131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663709581, "dur": 1825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663711407, "dur": 1822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663713276, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663713451, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663713552, "dur": 2134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663715687, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663717151, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663719015, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663720775, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663722508, "dur": 2008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663724517, "dur": 1894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663727889, "dur": 759, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/AnimationClipUpgrader.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748418663726412, "dur": 2352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663728764, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663730613, "dur": 1986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663732600, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663734116, "dur": 1597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663735714, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663737084, "dur": 1846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663738930, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663740590, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663742054, "dur": 1599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663743654, "dur": 1695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663745349, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663746996, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663748527, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663748623, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663750119, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663751791, "dur": 2065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663753856, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663755225, "dur": 1648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663756873, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663758711, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663760512, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663762174, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663764118, "dur": 2060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663766179, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663766302, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663766370, "dur": 1613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418663767985, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663768196, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663768259, "dur": 1244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663769567, "dur": 1805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663771430, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663772134, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663772226, "dur": 1425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418663773652, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663773947, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418663775991, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663776302, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663776502, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663776563, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418663777188, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663777499, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663777564, "dur": 5754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418663783319, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663783524, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663783674, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663783744, "dur": 14707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418663798452, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663798585, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418663798775, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418663799429, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418663799560, "dur": 213893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418664013460, "dur": 6534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418664019995, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418664020208, "dur": 5522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418664025782, "dur": 5156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418664030940, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418664031047, "dur": 4086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418664035134, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418664035220, "dur": 3139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418664038360, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418664038445, "dur": 3647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418664042094, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418664042169, "dur": 6326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418664048545, "dur": 612460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663211327, "dur": 485906, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663697240, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663697870, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663698012, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663698291, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663698362, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663698469, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663698580, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663698687, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663698769, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663698839, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663699063, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663699190, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663699370, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663699486, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748418663699882, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663700113, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663700271, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663700595, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663700936, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663701295, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663701450, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663701539, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663701673, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663701757, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663701861, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663701962, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663702112, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663702531, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663702589, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663702670, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663702751, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663702850, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663703006, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663703323, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663703412, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663703488, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663703578, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663703657, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663703745, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663703893, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663704226, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663704304, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663704398, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663704475, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663704538, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663704621, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663704848, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663704921, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663705076, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663705338, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663705756, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418663705872, "dur": 2125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663707997, "dur": 2129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663710127, "dur": 1751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663711878, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663713706, "dur": 2097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663715804, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663717339, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663719100, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663720930, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663722657, "dur": 1976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663724633, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663727894, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShapeEditor/EditorTool/GenericScriptablePath.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748418663726512, "dur": 2380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663728893, "dur": 1798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663730692, "dur": 1953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663732646, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663734153, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663735755, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663737115, "dur": 1825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663738941, "dur": 1654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663740595, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663742051, "dur": 1621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663743673, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663745385, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663747023, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663748596, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663750057, "dur": 1669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663751726, "dur": 1976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663753703, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663755085, "dur": 1657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663756742, "dur": 1833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663758576, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663760376, "dur": 1703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663762079, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663764015, "dur": 2032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663766048, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663766731, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663766795, "dur": 6042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418663772838, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663773084, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663774131, "dur": 3367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418663777499, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663777713, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663777881, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663777947, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418663779220, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663779374, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663779734, "dur": 2823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418663782559, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663782829, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663783575, "dur": 1742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418663785323, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663785684, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663786564, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663786787, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663786881, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418663787050, "dur": 2867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418663789918, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663790161, "dur": 1267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418663791428, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663791611, "dur": 2691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418663794303, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663794670, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663794872, "dur": 2407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663797279, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663798124, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418663798452, "dur": 215199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664013654, "dur": 3457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418664017112, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664017397, "dur": 3493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418664020892, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664020976, "dur": 3195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418664024172, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664024253, "dur": 3337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418664027591, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664027697, "dur": 5530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418664033228, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664033321, "dur": 3357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418664036734, "dur": 3623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418664040358, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664040423, "dur": 2772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418664043196, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664043482, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664043548, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664043645, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748418664043726, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664043833, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664043933, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748418664044149, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664044385, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664044457, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664044615, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748418664044694, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664044933, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664044991, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748418664045059, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664045143, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748418664045208, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664045367, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664045493, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664045658, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664045758, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748418664045837, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664046012, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748418664046088, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664046242, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664046313, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748418664046417, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664046629, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664046741, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664046915, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664046976, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748418664047036, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664047116, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748418664047167, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664047275, "dur": 1499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418664048817, "dur": 612220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663211339, "dur": 485905, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663697250, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663697776, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663698026, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663698377, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663698443, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_57AB718CCA0A791C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663698580, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663698643, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663698742, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663698813, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663699004, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663699089, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663699149, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_51CCB6F491E5150D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663699334, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663699433, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663699583, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663700029, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748418663700185, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663700353, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663700653, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663701032, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663701130, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748418663701245, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663701440, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663701544, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663701693, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663702084, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663702273, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663702638, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663702746, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748418663703094, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663703366, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663703471, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663703558, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663703619, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663703699, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663703851, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663703916, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663704061, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663704319, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663704389, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663704469, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663704550, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663704634, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663704859, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663704935, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663705072, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663705206, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663705268, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663705682, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418663705796, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663705890, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663708001, "dur": 2165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663710167, "dur": 1788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663711955, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663713798, "dur": 2053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663715852, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663717384, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663719165, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663720940, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663722683, "dur": 1917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663724600, "dur": 1927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663727945, "dur": 783, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteLitShaderGraph.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748418663726528, "dur": 2323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663728851, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663730688, "dur": 1951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663732640, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663734147, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663735735, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663737130, "dur": 1838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663738968, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663740612, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663742063, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663743678, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663745444, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663747107, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663748621, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663750135, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663751807, "dur": 1979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663753787, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663755186, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663756782, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663758697, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663760473, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663762132, "dur": 1869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663764001, "dur": 2060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663766062, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663766515, "dur": 4358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418663770874, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663771147, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663771222, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663771999, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663772060, "dur": 2720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418663774781, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663774959, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663775625, "dur": 4807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418663780433, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663780709, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663780822, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_DD9C61FBB7D63C03.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663780906, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663781069, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663781189, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_8E9298DC6B12ED57.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663781285, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_3971D385965D884C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663781393, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663781490, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663781688, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663782353, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418663784740, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663784900, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663785134, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663785366, "dur": 1905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418663787271, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663787378, "dur": 2596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418663789975, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663790199, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418663792452, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663792814, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418663792965, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663793059, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748418663793175, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663793237, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748418663793369, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663793479, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663795251, "dur": 2167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663797442, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663797718, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663797855, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418663798546, "dur": 214903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664013464, "dur": 3379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418664016890, "dur": 4162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418664021087, "dur": 4405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418664025493, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664025560, "dur": 4040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418664029601, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664029731, "dur": 3700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418664033432, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664033494, "dur": 3734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418664037259, "dur": 3265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418664040525, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664040635, "dur": 5613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418664046249, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664046585, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664046720, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664046809, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664046915, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664047042, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664047159, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748418664047256, "dur": 1276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418664048558, "dur": 612455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663211352, "dur": 485906, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663697263, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663697745, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663697978, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663698075, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663698172, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663698304, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663698415, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663698510, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663698622, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663698700, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663698782, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663698863, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663699016, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663699139, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663699226, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663699389, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663699476, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663699579, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418663699896, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663700047, "dur": 12226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663712274, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663712527, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663712598, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663712713, "dur": 4211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663716926, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663717010, "dur": 30057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663747069, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663747370, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663747431, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663747539, "dur": 3887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663751428, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663751545, "dur": 13535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663765081, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663765486, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663765591, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663765724, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663766377, "dur": 4739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663771117, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663771404, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663771469, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663771572, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663772660, "dur": 994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663773655, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663773913, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663774270, "dur": 2911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663777185, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663777540, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663777847, "dur": 5861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663783709, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663783916, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_BF69964832659484.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663783970, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663784169, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663784247, "dur": 3232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663787480, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663787764, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663788054, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663790782, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663790961, "dur": 7607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663798653, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418663798821, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418663799841, "dur": 135, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418663800034, "dur": 534312, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418664335828, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418664335798, "dur": 2124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418664338685, "dur": 261, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418664654706, "dur": 532, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418664338973, "dur": 316283, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418664660168, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418664660153, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418664660351, "dur": 594, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418664660950, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663211365, "dur": 485908, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663697279, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663697715, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663697807, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37EA32C02CB3EC76.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663698052, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_37E22A5ED73E2B2F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663698159, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663698257, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663698317, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663698374, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD8CC2F62B9395CF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663698480, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663698557, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663698668, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663698746, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663698847, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663698939, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663699083, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663699176, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663699257, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663699441, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663699552, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663699760, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663700063, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748418663700265, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748418663700386, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748418663700582, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663700679, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663701192, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663701336, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663701540, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663701683, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663701747, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663701820, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663701922, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663702010, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663702131, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748418663702459, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663702693, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663702802, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663702919, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663702983, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663703250, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663703371, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663703442, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663703535, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663703626, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663703725, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663703815, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663704250, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663704323, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663704408, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663704482, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663704561, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663704787, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663704880, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663704959, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663705056, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663705123, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663705220, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663705481, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663705770, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418663705882, "dur": 2193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663708075, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663710186, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663711913, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663713749, "dur": 2067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663715816, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663717353, "dur": 1777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663719130, "dur": 1730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663720860, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663722545, "dur": 2007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663724553, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663727929, "dur": 785, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShapeEditor/GUIFramework/LayoutData.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748418663726488, "dur": 2350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663728838, "dur": 1891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663730730, "dur": 1977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663732707, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663734205, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663735786, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663737224, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663739154, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663740719, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663742206, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663743793, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663745503, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663747099, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663748655, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663750104, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663751756, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663753669, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663755128, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663756746, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663758583, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663760348, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663762022, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663763905, "dur": 2102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663766008, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663766303, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663766422, "dur": 7964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418663774387, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663774680, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663775122, "dur": 6230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418663781352, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663781598, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663781663, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663782235, "dur": 3304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418663785540, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663785769, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663786612, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418663788976, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663789132, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663789776, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418663791289, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663791485, "dur": 1856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418663793342, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663793594, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_258B0455F8F51CDE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418663793653, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663793740, "dur": 2098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663795840, "dur": 1988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663797847, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663797931, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663798065, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418663798440, "dur": 215110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664013557, "dur": 5099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418664018716, "dur": 3135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418664021852, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664021925, "dur": 3894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418664025820, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664025906, "dur": 3473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418664029380, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664029561, "dur": 3409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418664032970, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664033041, "dur": 3193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418664036307, "dur": 3342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418664039650, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664039812, "dur": 3568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418664043381, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664043621, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664043768, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664043922, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664044178, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664044335, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664044405, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664044485, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748418664044586, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664044775, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664045013, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664045081, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748418664045146, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664045272, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748418664045345, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664045542, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748418664045604, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664045712, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748418664045801, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664045859, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748418664045941, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664046050, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664046141, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748418664046205, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664046451, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664046608, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748418664046661, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664046818, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664046907, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748418664046959, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664047093, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664047181, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748418664047249, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664047306, "dur": 612879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664660206, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748418664660188, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748418664660298, "dur": 69, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418664660371, "dur": 595, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748418663211378, "dur": 485904, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663697288, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663697711, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663698031, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_C9DCA0B70AE22DB0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663698320, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AB6C0CE8644C7690.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663698454, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663698588, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663698679, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663698767, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663698830, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663698995, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663699077, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663699202, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663699358, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663699444, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663699597, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663699966, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748418663700343, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418663700643, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748418663700956, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418663701501, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663701627, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663701723, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748418663701834, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663701923, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663702022, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748418663702326, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418663702588, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663702686, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663702764, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663702852, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663702919, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418663703104, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418663703381, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663703463, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663703525, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663703614, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663703686, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663703860, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663703930, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663704099, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418663704333, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663704407, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663704497, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663704567, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663704783, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663704863, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663704929, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663705040, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663705110, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663705177, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663705261, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663705880, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663705963, "dur": 1973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663707937, "dur": 2090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663710027, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663711794, "dur": 1901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663713696, "dur": 2029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663715726, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663717224, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663718993, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663720654, "dur": 1732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663722387, "dur": 1990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663724378, "dur": 1924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663726303, "dur": 1597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663727900, "dur": 784, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/Renderer2DMenus.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748418663727900, "dur": 2557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663730458, "dur": 1993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663732452, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663733944, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663735546, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663736947, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663738881, "dur": 1681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663740563, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663742010, "dur": 1603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663743613, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663745377, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663746983, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663748536, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663750067, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663751781, "dur": 2034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663753816, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663755234, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663756810, "dur": 1863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663758673, "dur": 1762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663760440, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663762120, "dur": 1941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663764062, "dur": 2058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663766122, "dur": 1241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663767403, "dur": 1773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418663769177, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663769390, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C5B5631EE4DDD44F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663769465, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663769610, "dur": 2093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663771704, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663771770, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663772241, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663772309, "dur": 1323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418663773633, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663773891, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663774393, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663774452, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418663775931, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663776168, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663776494, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418663777081, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663777227, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_1DF7BBC55D029A75.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663777376, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663778042, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663778343, "dur": 1712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418663780056, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663780309, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663780380, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_C4A720C3D568CE76.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663780445, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663780523, "dur": 3192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418663783716, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663783934, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663783996, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663784112, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663784200, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663784479, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663784710, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663784993, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663785221, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663785460, "dur": 2046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418663787507, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663787733, "dur": 2753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418663790487, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663790628, "dur": 1286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418663791915, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663792126, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663792277, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Editor.ref.dll_3C7C2928F291E90C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663792467, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663792697, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663792822, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418663792905, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663793021, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663793144, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748418663793196, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663793450, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663794225, "dur": 2474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663796700, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418663798442, "dur": 215120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664013565, "dur": 3109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418664016676, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664016802, "dur": 4153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418664020956, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664021034, "dur": 3241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418664024276, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664024351, "dur": 3767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418664028120, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664028292, "dur": 3777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418664032070, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664032150, "dur": 3847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418664036050, "dur": 3588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418664039639, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664039839, "dur": 3862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418664043702, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664043800, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748418664043856, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664043968, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664044215, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664044459, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664044629, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748418664044699, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664044854, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664044950, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748418664045032, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664045114, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664045264, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664045377, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664045464, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748418664045544, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664045736, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664045809, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664045917, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664045991, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748418664046073, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664046177, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748418664046264, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664046464, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664046561, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664046640, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664046942, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664047021, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664047139, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664047216, "dur": 1177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418664048453, "dur": 612598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663211391, "dur": 485900, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663697293, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663697786, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663698043, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663698390, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663698481, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663698609, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663698703, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663698778, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663698858, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663698969, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663699049, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663699136, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663699207, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663699389, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663699457, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663699542, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663699604, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663699859, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418663700372, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748418663700588, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418663700913, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748418663701148, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663701227, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663701353, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663701465, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663701584, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748418663701880, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663701969, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418663702470, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418663702563, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663702681, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663702798, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663702872, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663702998, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418663703356, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663703437, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663703517, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663703601, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663703672, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663703763, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418663704213, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663704285, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663704351, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663704432, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663704518, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663704594, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663704828, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663704897, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663705002, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663705089, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663705167, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663705235, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663705299, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418663705750, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418663705860, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663705923, "dur": 2064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663707987, "dur": 2177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663710164, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663711890, "dur": 1865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663713755, "dur": 2032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663715787, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663717295, "dur": 1815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663719111, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663720764, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663722479, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663724419, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663727920, "dur": 782, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/Camera/UniversalRenderPipelineCameraUI.Output.Skin.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748418663726375, "dur": 2327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663728703, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663730573, "dur": 2037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663732611, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663734135, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663735665, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663736986, "dur": 1838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663738824, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663740505, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663741967, "dur": 1573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663743541, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663745316, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663746966, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663748490, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663749902, "dur": 1696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663751598, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663753511, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663754981, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663756522, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663758289, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663760181, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663761791, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663763632, "dur": 2037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663765670, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663767033, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663767204, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663768473, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663768689, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663768751, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663768841, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663770070, "dur": 1630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663771751, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663772710, "dur": 1139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663773850, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663774300, "dur": 1779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663776086, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663776358, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663776635, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663777326, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663777528, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663777915, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663778570, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663778777, "dur": 1652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663780430, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663780579, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663781029, "dur": 3130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663784160, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663784305, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663784570, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663784811, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663785225, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663785343, "dur": 2005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663787349, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663787553, "dur": 2666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663790220, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663790417, "dur": 1233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663791651, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663792021, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663792487, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_CBD4E358CB8CDF43.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663792583, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418663792656, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663792777, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418663792855, "dur": 5722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418663798647, "dur": 215561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418664014212, "dur": 3535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418664017748, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418664017851, "dur": 5064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418664022959, "dur": 3347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418664026307, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418664026382, "dur": 3665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418664030048, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418664030144, "dur": 4908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418664035054, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418664035144, "dur": 3435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418664038629, "dur": 3507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418664042193, "dur": 6155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418664048445, "dur": 612549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418664666572, "dur": 1168, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 70122, "tid": 42, "ts": 1748418664730567, "dur": 4784, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 70122, "tid": 42, "ts": 1748418664735612, "dur": 4945, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 70122, "tid": 42, "ts": 1748418664697794, "dur": 46052, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}