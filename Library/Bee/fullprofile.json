{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 72182, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 72182, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 72182, "tid": 12, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 72182, "tid": 12, "ts": 1748424331593940, "dur": 2446, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 72182, "tid": 12, "ts": 1748424331604364, "dur": 1486, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 72182, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 72182, "tid": 1, "ts": 1748424328554810, "dur": 11192, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 72182, "tid": 1, "ts": 1748424328566005, "dur": 52690, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 72182, "tid": 1, "ts": 1748424328618705, "dur": 78158, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 72182, "tid": 12, "ts": 1748424331605856, "dur": 615, "ph": "X", "name": "", "args": {}}, {"pid": 72182, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328551213, "dur": 9666, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328560883, "dur": 3011682, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328562255, "dur": 7978, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328570240, "dur": 1677, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328571921, "dur": 16510, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328588453, "dur": 1216, "ph": "X", "name": "ProcessMessages 1603", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328589673, "dur": 106, "ph": "X", "name": "ReadAsync 1603", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328589787, "dur": 5, "ph": "X", "name": "ProcessMessages 7150", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328589794, "dur": 56, "ph": "X", "name": "ReadAsync 7150", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328589853, "dur": 2, "ph": "X", "name": "ProcessMessages 1239", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328589856, "dur": 74, "ph": "X", "name": "ReadAsync 1239", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328589949, "dur": 2, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328589953, "dur": 55, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590021, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590024, "dur": 55, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590084, "dur": 1, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590087, "dur": 45, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590135, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590139, "dur": 40, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590182, "dur": 2, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590185, "dur": 53, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590241, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590244, "dur": 49, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590296, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590492, "dur": 61, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590557, "dur": 3, "ph": "X", "name": "ProcessMessages 2517", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590562, "dur": 157, "ph": "X", "name": "ReadAsync 2517", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590734, "dur": 13, "ph": "X", "name": "ProcessMessages 2062", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590751, "dur": 82, "ph": "X", "name": "ReadAsync 2062", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590850, "dur": 2, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590853, "dur": 63, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590920, "dur": 2, "ph": "X", "name": "ProcessMessages 1178", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590926, "dur": 41, "ph": "X", "name": "ReadAsync 1178", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590970, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328590973, "dur": 47, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591023, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591035, "dur": 47, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591089, "dur": 4, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591096, "dur": 49, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591150, "dur": 8, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591159, "dur": 61, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591223, "dur": 1, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591226, "dur": 40, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591269, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591271, "dur": 46, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591322, "dur": 2, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591326, "dur": 56, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591393, "dur": 2, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591396, "dur": 46, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591448, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591451, "dur": 49, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591503, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591506, "dur": 40, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591559, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591561, "dur": 50, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591625, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591630, "dur": 57, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591700, "dur": 2, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591704, "dur": 65, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591772, "dur": 2, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591775, "dur": 43, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591828, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591841, "dur": 51, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591895, "dur": 2, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591899, "dur": 47, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591950, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328591952, "dur": 54, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328592010, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328592012, "dur": 50, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594274, "dur": 6, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594283, "dur": 76, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594364, "dur": 11, "ph": "X", "name": "ProcessMessages 6978", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594379, "dur": 175, "ph": "X", "name": "ReadAsync 6978", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594557, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594559, "dur": 34, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594597, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594600, "dur": 30, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594633, "dur": 25, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594660, "dur": 35, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594700, "dur": 3, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594703, "dur": 35, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594746, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328594752, "dur": 384, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595139, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595143, "dur": 35, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595183, "dur": 34, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595232, "dur": 3, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595239, "dur": 43, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595285, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595288, "dur": 33, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595324, "dur": 326, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595653, "dur": 6, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595663, "dur": 36, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595708, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595712, "dur": 73, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595788, "dur": 4, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595792, "dur": 33, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595830, "dur": 28, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595864, "dur": 40, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595908, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595910, "dur": 40, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595953, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595955, "dur": 33, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595991, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328595993, "dur": 29, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596026, "dur": 30, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596059, "dur": 538, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596600, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596603, "dur": 31, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596639, "dur": 36, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596679, "dur": 32, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596714, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596717, "dur": 40, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596759, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596761, "dur": 34, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328596799, "dur": 342, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328597144, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328597146, "dur": 43, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328597192, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328597194, "dur": 27, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328597222, "dur": 11, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328597236, "dur": 54, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328597295, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328597298, "dur": 731, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328598050, "dur": 19, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328598078, "dur": 79, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328598161, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328598164, "dur": 1067, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599240, "dur": 6, "ph": "X", "name": "ProcessMessages 6000", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599247, "dur": 33, "ph": "X", "name": "ReadAsync 6000", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599285, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599294, "dur": 48, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599347, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599350, "dur": 36, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599389, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599391, "dur": 36, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599432, "dur": 30, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599465, "dur": 3, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599470, "dur": 28, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599504, "dur": 30, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599539, "dur": 28, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599572, "dur": 30, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599606, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599608, "dur": 42, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599664, "dur": 41, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599709, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328599712, "dur": 336, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600052, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600054, "dur": 38, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600096, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600098, "dur": 41, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600142, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600144, "dur": 38, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600187, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600189, "dur": 37, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600233, "dur": 298, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600535, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600538, "dur": 52, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600593, "dur": 12, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600609, "dur": 34, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600651, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600654, "dur": 30, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600687, "dur": 2, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600693, "dur": 36, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600734, "dur": 2, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600738, "dur": 32, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600773, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600776, "dur": 50, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600828, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600830, "dur": 54, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600900, "dur": 5, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600919, "dur": 63, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600985, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328600988, "dur": 124, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601115, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601130, "dur": 64, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601202, "dur": 4, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601210, "dur": 70, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601287, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601290, "dur": 311, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601604, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601606, "dur": 33, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601645, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601647, "dur": 40, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601689, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601691, "dur": 35, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601729, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601731, "dur": 32, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601767, "dur": 79, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601849, "dur": 6, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601858, "dur": 33, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601898, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601900, "dur": 65, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601969, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328601971, "dur": 126, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602128, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602131, "dur": 62, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602207, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602210, "dur": 48, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602262, "dur": 7, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602279, "dur": 56, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602338, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602341, "dur": 53, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602404, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602406, "dur": 317, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602744, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602750, "dur": 39, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602793, "dur": 2, "ph": "X", "name": "ProcessMessages 1493", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602809, "dur": 36, "ph": "X", "name": "ReadAsync 1493", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602851, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602853, "dur": 41, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602899, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328602901, "dur": 31, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604118, "dur": 4, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604126, "dur": 59, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604192, "dur": 6, "ph": "X", "name": "ProcessMessages 6691", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604203, "dur": 113, "ph": "X", "name": "ReadAsync 6691", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604319, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604322, "dur": 33, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604360, "dur": 43, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604410, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604413, "dur": 44, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604459, "dur": 8, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604472, "dur": 51, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604526, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604528, "dur": 152, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604685, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604687, "dur": 35, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604732, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604734, "dur": 39, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604777, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604782, "dur": 33, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604817, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604821, "dur": 44, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604868, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604871, "dur": 38, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604913, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604917, "dur": 35, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604957, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604960, "dur": 30, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604996, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328604999, "dur": 29, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605033, "dur": 370, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605406, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605419, "dur": 35, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605459, "dur": 6, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605466, "dur": 36, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605512, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605515, "dur": 38, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605559, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605561, "dur": 165, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605729, "dur": 1, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605732, "dur": 42, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605779, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605782, "dur": 37, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605825, "dur": 6, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605834, "dur": 31, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605868, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328605870, "dur": 446, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606363, "dur": 9, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606381, "dur": 82, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606467, "dur": 4, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606473, "dur": 70, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606552, "dur": 14, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606568, "dur": 83, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606656, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606676, "dur": 67, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606749, "dur": 3, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606754, "dur": 84, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606849, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606862, "dur": 76, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606955, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328606961, "dur": 75, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607039, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607041, "dur": 182, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607229, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607231, "dur": 113, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607347, "dur": 3, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607352, "dur": 78, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607443, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607446, "dur": 83, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607594, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328607597, "dur": 828, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328608428, "dur": 6, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328608440, "dur": 60, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328608503, "dur": 1, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328608505, "dur": 49, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328608564, "dur": 3, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328608569, "dur": 57, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328608629, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328608637, "dur": 1012, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609654, "dur": 4, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609659, "dur": 59, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609730, "dur": 6, "ph": "X", "name": "ProcessMessages 7581", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609738, "dur": 49, "ph": "X", "name": "ReadAsync 7581", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609791, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609793, "dur": 40, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609837, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609840, "dur": 46, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609900, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609903, "dur": 47, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609954, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328609956, "dur": 76, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610036, "dur": 10, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610050, "dur": 62, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610116, "dur": 3, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610123, "dur": 58, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610185, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610187, "dur": 160, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610352, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610355, "dur": 35, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610395, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610400, "dur": 31, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610435, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610437, "dur": 37, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610477, "dur": 3, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610480, "dur": 32, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610517, "dur": 212, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610733, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610737, "dur": 43, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610790, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610792, "dur": 38, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610833, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610835, "dur": 37, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610878, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610883, "dur": 37, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610927, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610929, "dur": 40, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610971, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328610974, "dur": 30, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611006, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611010, "dur": 38, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611050, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611052, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611086, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611088, "dur": 381, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611472, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611473, "dur": 31, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611515, "dur": 7, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611526, "dur": 57, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611586, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611589, "dur": 45, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611650, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611719, "dur": 45, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611769, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611771, "dur": 90, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611868, "dur": 7, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611879, "dur": 52, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611934, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611936, "dur": 46, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611985, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328611988, "dur": 150, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612142, "dur": 7, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612151, "dur": 43, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612198, "dur": 7, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612214, "dur": 44, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612260, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612262, "dur": 29, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612295, "dur": 72, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612369, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612371, "dur": 30, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612404, "dur": 28, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612436, "dur": 35, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612474, "dur": 29, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612506, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612508, "dur": 100, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612614, "dur": 2, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612618, "dur": 58, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612679, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612682, "dur": 41, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612726, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612728, "dur": 33, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612766, "dur": 84, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612853, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612855, "dur": 30, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612890, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612895, "dur": 48, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612945, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328612947, "dur": 1013, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328613965, "dur": 5, "ph": "X", "name": "ProcessMessages 6720", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328613972, "dur": 33, "ph": "X", "name": "ReadAsync 6720", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614008, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614012, "dur": 40, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614056, "dur": 28, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614088, "dur": 154, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614244, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614246, "dur": 33, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614283, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614285, "dur": 36, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614325, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614327, "dur": 31, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614362, "dur": 36, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614403, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614405, "dur": 165, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614574, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614577, "dur": 42, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614622, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614625, "dur": 37, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614665, "dur": 1, "ph": "X", "name": "ProcessMessages 150", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614667, "dur": 42, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614712, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614714, "dur": 45, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614763, "dur": 1, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614765, "dur": 205, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614976, "dur": 2, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328614980, "dur": 51, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615035, "dur": 2, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615038, "dur": 46, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615087, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615089, "dur": 192, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615285, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615290, "dur": 79, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615372, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615374, "dur": 48, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615427, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615430, "dur": 44, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615477, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615481, "dur": 51, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615537, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615545, "dur": 201, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615750, "dur": 10, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615765, "dur": 51, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615819, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615822, "dur": 46, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615872, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615874, "dur": 40, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615917, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328615919, "dur": 226, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616149, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616151, "dur": 40, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616197, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616200, "dur": 35, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616238, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616240, "dur": 34, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616277, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616278, "dur": 32, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616314, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616316, "dur": 38, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616355, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616357, "dur": 28, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616387, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616389, "dur": 34, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616426, "dur": 3, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616430, "dur": 30, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616463, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616465, "dur": 30, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616497, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616499, "dur": 32, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616534, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616536, "dur": 32, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616572, "dur": 68, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616644, "dur": 34, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616688, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616690, "dur": 31, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616724, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616726, "dur": 39, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616771, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616774, "dur": 38, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616816, "dur": 114, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328616934, "dur": 436, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328617374, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328617377, "dur": 32, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328617411, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328617413, "dur": 792, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328618208, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328618210, "dur": 271, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328618485, "dur": 4, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328618490, "dur": 664, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328619165, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328619167, "dur": 304, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328619475, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328619477, "dur": 698, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328620178, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328620180, "dur": 260, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328620445, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328620447, "dur": 1061, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328621511, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328621513, "dur": 248, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328621766, "dur": 996, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328622765, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328622768, "dur": 39, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328622810, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328622812, "dur": 767, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328623583, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328623585, "dur": 33, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328623624, "dur": 98, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328623726, "dur": 960, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328624690, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328624693, "dur": 287, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328624982, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328624985, "dur": 1002, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328625990, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328625994, "dur": 38, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328626039, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328626041, "dur": 911, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328626957, "dur": 5, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328626964, "dur": 272, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328627239, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328627241, "dur": 1471, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328628733, "dur": 2, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328628736, "dur": 366, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328629107, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328629109, "dur": 308, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328629420, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328629423, "dur": 1596, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328631032, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328631036, "dur": 359, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328631408, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328631411, "dur": 908, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328632337, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328632339, "dur": 291, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328632634, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328632636, "dur": 1267, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328633907, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328633909, "dur": 269, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328634182, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328634183, "dur": 947, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328635135, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328635137, "dur": 258, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328635400, "dur": 3806, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328639217, "dur": 3, "ph": "X", "name": "ProcessMessages 1460", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328639221, "dur": 468, "ph": "X", "name": "ReadAsync 1460", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328639696, "dur": 3, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328639701, "dur": 89, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328639796, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328639811, "dur": 371, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328640185, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328640188, "dur": 1212, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328641405, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328641408, "dur": 51, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328641463, "dur": 1, "ph": "X", "name": "ProcessMessages 110", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328641465, "dur": 183, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328641652, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328641655, "dur": 2185, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328643846, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328643850, "dur": 256, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328644110, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328644113, "dur": 1893, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328646011, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328646014, "dur": 977, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328646999, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328647002, "dur": 1801, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328648832, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328648836, "dur": 203, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328649049, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328649058, "dur": 930, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328649992, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328649995, "dur": 324, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328650322, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328650326, "dur": 783, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328651113, "dur": 2, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328651116, "dur": 43, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328651168, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328651170, "dur": 39, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328651211, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328651215, "dur": 1379, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328652599, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328652601, "dur": 248, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328652852, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328652854, "dur": 36, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328652893, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328652894, "dur": 28, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328652926, "dur": 65, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328652995, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328652997, "dur": 1479, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328654481, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328654483, "dur": 866, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328655353, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328655355, "dur": 32, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328655389, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328655391, "dur": 931, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328656326, "dur": 38, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328656367, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328656371, "dur": 230, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328656607, "dur": 1832, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328658447, "dur": 1, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328658449, "dur": 2159, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328660612, "dur": 9, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328660622, "dur": 279, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328660905, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328660907, "dur": 2289, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328663202, "dur": 2, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328663205, "dur": 3069, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328666291, "dur": 4, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328666300, "dur": 2020, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328668327, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328668332, "dur": 413, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328668750, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328668753, "dur": 1880, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328670637, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328670640, "dur": 264, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328670908, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328670910, "dur": 1203, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328672117, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328672122, "dur": 85, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328672211, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328672214, "dur": 144, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328672362, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328672364, "dur": 1249, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328673621, "dur": 3, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328673625, "dur": 364, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328673993, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328673995, "dur": 3230, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328677233, "dur": 4, "ph": "X", "name": "ProcessMessages 2875", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328677238, "dur": 1293, "ph": "X", "name": "ReadAsync 2875", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328678538, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328678541, "dur": 261, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328678805, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328678807, "dur": 1445, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328680256, "dur": 3, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328680260, "dur": 152, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328680416, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328680417, "dur": 1362, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328681785, "dur": 7, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328681797, "dur": 239, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328682052, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328682058, "dur": 2684, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328684752, "dur": 3, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328684759, "dur": 306, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328685070, "dur": 2, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328685074, "dur": 3514, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328688596, "dur": 3, "ph": "X", "name": "ProcessMessages 2397", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328688600, "dur": 1114, "ph": "X", "name": "ReadAsync 2397", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328689717, "dur": 4, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328689723, "dur": 320, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328690047, "dur": 902, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328690952, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328690954, "dur": 35, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328690994, "dur": 376, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328691374, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328691375, "dur": 903, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328692283, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328692286, "dur": 395, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328692686, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328692689, "dur": 612, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693305, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693308, "dur": 53, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693365, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693367, "dur": 53, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693424, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693426, "dur": 44, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693474, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693476, "dur": 57, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693536, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693539, "dur": 76, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693618, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328693621, "dur": 884, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328694509, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328694511, "dur": 36, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328694551, "dur": 671, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328695227, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328695230, "dur": 271, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328695504, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328695506, "dur": 485, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328695993, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328695995, "dur": 35, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328696034, "dur": 242, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328696280, "dur": 1006, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328697291, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328697294, "dur": 280, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328697578, "dur": 7, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328697589, "dur": 1041, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328698636, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328698639, "dur": 524, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328699170, "dur": 4, "ph": "X", "name": "ProcessMessages 1811", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328699175, "dur": 950, "ph": "X", "name": "ReadAsync 1811", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328700133, "dur": 2, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328700137, "dur": 725, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328700870, "dur": 375, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701267, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701364, "dur": 6, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701372, "dur": 99, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701474, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701477, "dur": 61, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701542, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701544, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701656, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701659, "dur": 110, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701773, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701776, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701830, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701832, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701888, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328701890, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702004, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702007, "dur": 62, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702073, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702075, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702182, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702185, "dur": 182, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702371, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702376, "dur": 111, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702492, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702495, "dur": 55, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702554, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702556, "dur": 56, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702615, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702618, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702692, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702695, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702743, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702746, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702796, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702798, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702845, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328702999, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703001, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703105, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703108, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703210, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703212, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703267, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703270, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703326, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703328, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703384, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703387, "dur": 183, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703575, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328703577, "dur": 433, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328704017, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328704022, "dur": 318, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328704347, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328704350, "dur": 199, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328704554, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328704556, "dur": 326, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328704897, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328704901, "dur": 318, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705224, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705228, "dur": 137, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705370, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705372, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705483, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705485, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705572, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705575, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705644, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705646, "dur": 145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705796, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328705799, "dur": 223, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706026, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706028, "dur": 101, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706133, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706136, "dur": 122, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706264, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706433, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706439, "dur": 189, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706632, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706634, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706697, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706702, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706763, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706768, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706836, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706839, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706899, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706901, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706956, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328706958, "dur": 162, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707124, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707127, "dur": 71, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707204, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707286, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707288, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707356, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707358, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707430, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707432, "dur": 111, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707547, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707549, "dur": 233, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707787, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707791, "dur": 169, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707972, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328707975, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708039, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708042, "dur": 58, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708103, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708106, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708232, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708234, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708304, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708309, "dur": 68, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708395, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708398, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708455, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708458, "dur": 114, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708576, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708579, "dur": 262, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708844, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708847, "dur": 57, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708907, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328708910, "dur": 120, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328709034, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328709037, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328709098, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328709100, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328709168, "dur": 1421, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710597, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710601, "dur": 149, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710764, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710767, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710849, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710852, "dur": 65, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710921, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710926, "dur": 55, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710984, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328710986, "dur": 151, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711141, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711145, "dur": 78, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711227, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711231, "dur": 53, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711288, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711291, "dur": 54, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711349, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711352, "dur": 75, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711431, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711436, "dur": 60, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711499, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328711503, "dur": 3086, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328714597, "dur": 5, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328714603, "dur": 19301, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328733916, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328733920, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328734035, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328734037, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328734077, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328734080, "dur": 2583, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328736670, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328736672, "dur": 11000, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328747694, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328747697, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328747757, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328747759, "dur": 1232, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328748996, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328748999, "dur": 571, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328749575, "dur": 710, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328750290, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328750292, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328750449, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328750451, "dur": 341, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328750797, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328750799, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328750851, "dur": 432, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328751289, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328751452, "dur": 1214, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328752670, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328752672, "dur": 69, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328752746, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328752748, "dur": 204, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328752966, "dur": 254, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328753225, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328753227, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328753327, "dur": 626, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328753957, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328753960, "dur": 263, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328754228, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328754286, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328754349, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328754351, "dur": 230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328754585, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328754587, "dur": 317, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328754909, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328755065, "dur": 387, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328755456, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328755458, "dur": 300, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328755762, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328755765, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328755957, "dur": 1070, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328757034, "dur": 386, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328757423, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328757426, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328757545, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328757548, "dur": 879, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328758431, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328758434, "dur": 267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328758706, "dur": 905, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328759615, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328759623, "dur": 554, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760181, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760184, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760235, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760237, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760385, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760387, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760475, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760477, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760604, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328760606, "dur": 631, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328761242, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328761245, "dur": 155, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328761403, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328761406, "dur": 483, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328761892, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328761894, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328762063, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328762065, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328762138, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328762141, "dur": 452, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328762597, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328762599, "dur": 495, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328763098, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328763101, "dur": 815, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328763941, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328763946, "dur": 776, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328764729, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328764733, "dur": 1200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328765940, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328765944, "dur": 389, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328766338, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328766340, "dur": 672, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328767016, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328767018, "dur": 560, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328767581, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328767582, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328767697, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328767699, "dur": 693, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328768397, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328768399, "dur": 682, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328769084, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328769086, "dur": 356, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328769447, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328769449, "dur": 228, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328769683, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328769965, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328769968, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328770064, "dur": 319, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328770386, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328770388, "dur": 409, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328770827, "dur": 698, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328771529, "dur": 262, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328771797, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328771801, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328771868, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328771870, "dur": 464, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772339, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772341, "dur": 219, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772564, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772566, "dur": 152, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772732, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772735, "dur": 156, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772894, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772897, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772955, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328772957, "dur": 1271, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774233, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774235, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774302, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774306, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774377, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774380, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774427, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774429, "dur": 465, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774904, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328774907, "dur": 381, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328775292, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328775294, "dur": 736, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328776035, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328776038, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328776176, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328776178, "dur": 547, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328776733, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328776740, "dur": 963, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328777710, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328777712, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328777852, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328777854, "dur": 441, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328778318, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328778321, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328778433, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328778436, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328778496, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328778498, "dur": 546, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779049, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779051, "dur": 282, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779338, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779340, "dur": 311, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779655, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779657, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779714, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779720, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779882, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328779884, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780009, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780025, "dur": 67, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780119, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780122, "dur": 229, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780356, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780358, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780451, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780453, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780516, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780538, "dur": 372, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780977, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328780982, "dur": 217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328781204, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328781206, "dur": 167, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328781377, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328781380, "dur": 172, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328781557, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328781559, "dur": 502, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328782066, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328782068, "dur": 1025, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328783099, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328783102, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328783222, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328783225, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328783359, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328783361, "dur": 412, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328783777, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328783780, "dur": 77, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328783862, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328784076, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328784078, "dur": 673, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328784753, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424328784756, "dur": 273931, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329058700, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329058704, "dur": 87, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329058796, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329058799, "dur": 61, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329058865, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329058926, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329058928, "dur": 56, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329058989, "dur": 1, "ph": "X", "name": "ProcessMessages 4138", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329059000, "dur": 49, "ph": "X", "name": "ReadAsync 4138", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329059054, "dur": 52, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329059109, "dur": 2536, "ph": "X", "name": "ProcessMessages 4842", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329061653, "dur": 4092, "ph": "X", "name": "ReadAsync 4842", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329065752, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329065756, "dur": 1876, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329067639, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329067643, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329067779, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329067782, "dur": 780, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329068566, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329068569, "dur": 585, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329069156, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329069158, "dur": 1121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329070285, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329070287, "dur": 390, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329070680, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329070683, "dur": 586, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329071273, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329071276, "dur": 768, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329072048, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329072050, "dur": 840, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329072898, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329072900, "dur": 951, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329073857, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329073860, "dur": 1375, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329075240, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329075244, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329075311, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329075315, "dur": 1699, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329077020, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329077024, "dur": 1864, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329078894, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329078898, "dur": 564, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329079467, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329079469, "dur": 3431, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329082908, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329082920, "dur": 602, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329083529, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329083532, "dur": 422, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329083959, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329083962, "dur": 681, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329084651, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329084654, "dur": 2564, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329087224, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329087228, "dur": 1103, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329088335, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329088338, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329088439, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329088441, "dur": 801, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329089247, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329089250, "dur": 1932, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329091189, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329091193, "dur": 1424, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329092624, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329092627, "dur": 1880, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329094514, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329094518, "dur": 216, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329094739, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329094741, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329094855, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329094858, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329094923, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329094926, "dur": 350, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095281, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095286, "dur": 144, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095433, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095436, "dur": 69, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095508, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095510, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095564, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095566, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095659, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095661, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095722, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095724, "dur": 81, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095809, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095811, "dur": 86, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095901, "dur": 13, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329095917, "dur": 86, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096006, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096008, "dur": 90, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096102, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096105, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096193, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096196, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096299, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096301, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096372, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096374, "dur": 153, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096531, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096534, "dur": 132, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096670, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096672, "dur": 92, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096769, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096771, "dur": 116, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096891, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329096893, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097002, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097005, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097052, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097055, "dur": 87, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097163, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097166, "dur": 477, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097647, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097650, "dur": 216, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097870, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329097873, "dur": 623, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098503, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098507, "dur": 115, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098627, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098630, "dur": 101, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098736, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098738, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098812, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098814, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098904, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098907, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098986, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329098988, "dur": 186, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099184, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099187, "dur": 92, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099283, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099285, "dur": 140, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099429, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099432, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099552, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099554, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099767, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099769, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099848, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329099850, "dur": 283, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329100136, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329100139, "dur": 1098, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329101243, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424329101247, "dur": 1685554, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424330786811, "dur": 22, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424330786835, "dur": 5326, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424330792178, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424330792181, "dur": 764782, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331556971, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331556975, "dur": 61, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331557044, "dur": 57, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331557106, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331557108, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331557167, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331557169, "dur": 57, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331557231, "dur": 19, "ph": "X", "name": "ProcessMessages 2629", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331557252, "dur": 5204, "ph": "X", "name": "ReadAsync 2629", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331562461, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331562464, "dur": 2982, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331565458, "dur": 26, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331565486, "dur": 394, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331565885, "dur": 266, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 72182, "tid": 12884901888, "ts": 1748424331566154, "dur": 6367, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 72182, "tid": 12, "ts": 1748424331606474, "dur": 6159, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 72182, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 72182, "tid": 8589934592, "ts": 1748424328543907, "dur": 153137, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 72182, "tid": 8589934592, "ts": 1748424328697048, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 72182, "tid": 8589934592, "ts": 1748424328697055, "dur": 3815, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 72182, "tid": 12, "ts": 1748424331612635, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 72182, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 72182, "tid": 4294967296, "ts": 1748424328382770, "dur": 3192606, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 72182, "tid": 4294967296, "ts": 1748424328503882, "dur": 30853, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 72182, "tid": 4294967296, "ts": 1748424331575663, "dur": 8827, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 72182, "tid": 4294967296, "ts": 1748424331579357, "dur": 3193, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 72182, "tid": 4294967296, "ts": 1748424331584572, "dur": 21, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 72182, "tid": 12, "ts": 1748424331612643, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748424328543360, "dur": 7853, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748424328551263, "dur": 36666, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748424328588032, "dur": 98, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748424328588131, "dur": 179, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748424328588760, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328588839, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328588903, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328588967, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589108, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589168, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589229, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589290, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589350, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_9909B9D5D2D6D40E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589409, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589469, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589528, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_97FEC325F9337F2C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589595, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_A80C913DF2042397.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589653, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_888C5CF0B798DF8B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589799, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D0749362471C98A9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589856, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589910, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328589990, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328590059, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328590141, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_CAA42EBCF9A9A6C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328594318, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748424328598703, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607177, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607246, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607308, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607366, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607420, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607580, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607732, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_BF69964832659484.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607794, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607850, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607911, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748424328607972, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748424328608028, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748424328608093, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748424328616673, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748424328631485, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748424328631545, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748424328633153, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328640213, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748424328640702, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328641950, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748424328649178, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748424328649307, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748424328649541, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328665073, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748424328665559, "dur": 379, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328668802, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748424328669274, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328672652, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748424328672870, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328682183, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748424328682543, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328693214, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748424328694033, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748424328588319, "dur": 112328, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748424328700663, "dur": 2865464, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748424331566262, "dur": 82, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748424331566350, "dur": 117, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748424331566532, "dur": 1162, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748424328588255, "dur": 112419, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328700734, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748424328701270, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328701713, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328701930, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328701995, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328702073, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328702208, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328702279, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328702403, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328702475, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328702562, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328702661, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328702764, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328702902, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328703031, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328703292, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328703528, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328703667, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748424328703870, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328704441, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748424328704736, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328704897, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328705090, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748424328705355, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748424328705447, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328705764, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328706398, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748424328706748, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328707288, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748424328707492, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328707869, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328707969, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328708037, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328708308, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328708770, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328708973, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328709095, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328709427, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328711324, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328711404, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328711833, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748424328711911, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748424328712073, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328712185, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328712623, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328713394, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748424328714049, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748424328714408, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/InputManager.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748424328716248, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Characters/ExperienceSystem.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748424328714172, "dur": 4140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328718312, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328719397, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328720493, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328721555, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328722665, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328723742, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328724787, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328725915, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328728051, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ScriptableRendererFeatureProvider.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748424328727027, "dur": 2020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328729048, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328730348, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328731609, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328732834, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328734066, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328735159, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328736229, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328737387, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328738433, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328739931, "dur": 1990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328741922, "dur": 1621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328743544, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328744970, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328746068, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328747152, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328748416, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328749495, "dur": 5224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748424328754720, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328755216, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328756013, "dur": 5350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748424328761364, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328761999, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748424328762439, "dur": 2359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748424328764799, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328765239, "dur": 5423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748424328770736, "dur": 252, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329059109, "dur": 606, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424328771327, "dur": 288474, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748424329062598, "dur": 3578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748424329066178, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329066308, "dur": 3555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748424329069910, "dur": 3611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748424329073522, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329073585, "dur": 3595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748424329077181, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329077268, "dur": 3586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748424329080897, "dur": 5817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748424329086716, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329086802, "dur": 3546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748424329090349, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329090526, "dur": 4592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748424329095120, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329095205, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329095557, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329095900, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329096015, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329096273, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329096376, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329096719, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329096846, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748424329096899, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329097120, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329097260, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329097843, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329098466, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748424329098533, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329099100, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748424329099247, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329099359, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329099476, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748424329099537, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329099782, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329100000, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329100062, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748424329100747, "dur": 2465386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328588257, "dur": 112449, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328700722, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748424328701159, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328701234, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328701406, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37EA32C02CB3EC76.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328701888, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328702108, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328702186, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328702244, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328702354, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328702427, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328702517, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328702587, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328702805, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328702901, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328702993, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328703295, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748424328703974, "dur": 6135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328710110, "dur": 739, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328710849, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328711223, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748424328711722, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748424328711784, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748424328711875, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748424328711954, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748424328713090, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748424328713228, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748424328713754, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748424328714424, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328714918, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328716583, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328718246, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328719333, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328720444, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328721551, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328722660, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328723733, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328724782, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328725894, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328728104, "dur": 749, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ShaderGraph/Targets/UniversalUnlitSubTarget.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748424328726988, "dur": 2017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328729006, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328730276, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328731527, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328732737, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328733867, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328734948, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328736017, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328737139, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328738218, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328739629, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328741741, "dur": 1629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328743371, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328744843, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328745965, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328747057, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328748326, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328749365, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328749648, "dur": 1486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328751135, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328751385, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328751444, "dur": 2022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328753523, "dur": 1233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328754798, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328755548, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328755612, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328756324, "dur": 3222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328759547, "dur": 1486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328761043, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328761169, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328761740, "dur": 2310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328764051, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328764427, "dur": 6414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328770842, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328771191, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328771364, "dur": 1553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328772918, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328773050, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328773277, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328773554, "dur": 1133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328774687, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328774796, "dur": 1731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328776528, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328776734, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328778242, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328778392, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328778831, "dur": 2380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748424328781212, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328781712, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_AD2FFB7646FD92B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748424328781842, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328781916, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328781988, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328782096, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328782240, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328782320, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328782458, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328782514, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328782638, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328783070, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328783192, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328783309, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328783378, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328783524, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328783677, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424328783859, "dur": 278741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424329062608, "dur": 5371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748424329067980, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424329068284, "dur": 3560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748424329071845, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424329071953, "dur": 3784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748424329075738, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424329075817, "dur": 3658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748424329079539, "dur": 3869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748424329083409, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424329083899, "dur": 3792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748424329087692, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424329087786, "dur": 5198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748424329092985, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748424329093075, "dur": 8628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748424329101789, "dur": 2464327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328588257, "dur": 112462, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328700725, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328701291, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328701662, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328702084, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328702233, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328702368, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328702494, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328702556, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328702968, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328703069, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328703370, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328703590, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748424328703769, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748424328704008, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328704190, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748424328704577, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328704804, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328704941, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328705406, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328705774, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328706665, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748424328707053, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328707249, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328707930, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328708007, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748424328708277, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328708502, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328709013, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328709092, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328709404, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328709482, "dur": 1341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328710836, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328711115, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328711527, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748424328711790, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748424328711876, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748424328712140, "dur": 1486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328713643, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748424328713972, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328714451, "dur": 636, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Weapons/WeaponController.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748424328714120, "dur": 4934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328719055, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328720198, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328721171, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328722249, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328723331, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328724399, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328725505, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328726592, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328728066, "dur": 733, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/GlobalSettings/UniversalRenderPipelineGlobalSettingsUI.Drawers.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748424328727881, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328729814, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328731160, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328732325, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328733577, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328734670, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328735748, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328736831, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328737951, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328739162, "dur": 2084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328741247, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328743022, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328744463, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328745593, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328746698, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328747910, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328749049, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328749506, "dur": 18041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748424328767548, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328768159, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_4EC1FE8B3807FE28.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328768261, "dur": 1345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328769658, "dur": 3448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748424328773106, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328773186, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328773260, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328773484, "dur": 1280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748424328774765, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328774957, "dur": 1576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748424328776534, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328776716, "dur": 2956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748424328779673, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328779801, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748424328780248, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328780651, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328780726, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328780834, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328781024, "dur": 2889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748424328783914, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424328783976, "dur": 278610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424329062593, "dur": 5341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748424329067935, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424329068055, "dur": 3105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748424329071161, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424329071254, "dur": 4621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748424329075907, "dur": 3587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748424329079495, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424329079564, "dur": 4491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748424329084057, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424329084190, "dur": 5004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748424329089195, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424329089371, "dur": 4956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748424329094328, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748424329094430, "dur": 6258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748424329100737, "dur": 2465364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328588273, "dur": 112467, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328700748, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328701232, "dur": 6135, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328707450, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748424328707614, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328707697, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328707772, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748424328707958, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328708061, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748424328708787, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328708900, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328709007, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328709103, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328709441, "dur": 1989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748424328711447, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748424328711683, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748424328711816, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748424328711903, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748424328712040, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748424328712270, "dur": 1399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748424328713799, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748424328713977, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748424328714379, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328714854, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328716572, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328718109, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328719190, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328720297, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328721392, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328722497, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328723566, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328724644, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328725733, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328728058, "dur": 720, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Editor/MeshUtilities.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748424328726831, "dur": 1948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328728779, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328730078, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328731304, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328732462, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328733663, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328734749, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328735896, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328737091, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328738198, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328739553, "dur": 2022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328741576, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328743268, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328744710, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328745819, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328746890, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328748049, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328749191, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328749537, "dur": 8312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748424328757850, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328758175, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328759012, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328760155, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748424328762469, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328762628, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_E30EE1A78470D274.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328762711, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328763119, "dur": 2823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748424328765943, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328766423, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Runtime.ref.dll_DEAB70CB54ED1324.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328766527, "dur": 1019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328767595, "dur": 3778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748424328771374, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328771888, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328772264, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328772446, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328772752, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328772943, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328773211, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328773433, "dur": 1629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748424328775063, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328775416, "dur": 1728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748424328777197, "dur": 1706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748424328778903, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328779077, "dur": 2182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748424328781260, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328781827, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748424328781883, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328781999, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328782050, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328782316, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328782510, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328782646, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424328783848, "dur": 278848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424329062704, "dur": 5185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748424329067891, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424329067976, "dur": 3632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748424329071609, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424329071689, "dur": 3751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748424329075441, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424329075544, "dur": 3375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748424329078970, "dur": 5945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748424329084916, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424329085005, "dur": 4732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748424329089738, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424329089887, "dur": 3314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748424329093237, "dur": 6884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748424329100122, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424329100303, "dur": 1471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748424329101810, "dur": 2464310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328588281, "dur": 112490, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328700804, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328701240, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328701621, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328702054, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328702149, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328702218, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328702319, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328702383, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328702562, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328702916, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328703018, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328703145, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748424328703679, "dur": 6550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328710230, "dur": 682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328710940, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328711070, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328711324, "dur": 22655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328733980, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328734443, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328734526, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328734598, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328737167, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328737229, "dur": 10749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328747980, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328748203, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328748265, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328748365, "dur": 1068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328749462, "dur": 3589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328753052, "dur": 758, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328753912, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328754939, "dur": 2502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328757442, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328757605, "dur": 3076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328760681, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328761133, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328761215, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328763493, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328763646, "dur": 6239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328769886, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328770001, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_C4A720C3D568CE76.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328770083, "dur": 1365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328771448, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328771785, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328771882, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328772504, "dur": 11126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328783631, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424328783786, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748424328783944, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748424328784319, "dur": 278393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329062714, "dur": 3445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748424329066160, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329066257, "dur": 3372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748424329069666, "dur": 3727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748424329073400, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329073490, "dur": 3662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748424329077153, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329077471, "dur": 5207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748424329082679, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329082744, "dur": 5827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748424329088627, "dur": 4419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748424329093047, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329093123, "dur": 5128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748424329098252, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329098385, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748424329098448, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329098621, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748424329099080, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329099260, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329099382, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329099631, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329099709, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748424329099762, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329099865, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329099981, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424329100314, "dur": 2462601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748424331562936, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748424331562918, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748424331563046, "dur": 2989, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748424331566039, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328588304, "dur": 112529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328700839, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328701232, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328701335, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1996541149DA13E0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328701684, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328701982, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328702094, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328702160, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328702262, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328702345, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328702437, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328702516, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328702603, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328702752, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328702923, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328703125, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328703452, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328703614, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748424328703890, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328704136, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328704786, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328704861, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328705066, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328705141, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328706010, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748424328706305, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328706386, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328706629, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328706703, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328707021, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328707195, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328707255, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328707364, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748424328707648, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328707710, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328708134, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328708314, "dur": 2853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328711198, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328711734, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328711957, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748424328712125, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328712354, "dur": 1135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328713539, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748424328714295, "dur": 1995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328716290, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328717974, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328719121, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328720261, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328721333, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328722432, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328723535, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328724631, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328725763, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328728088, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/UpgradeCommon.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748424328726898, "dur": 2018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328728917, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328730201, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328731446, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328732622, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328733781, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328734846, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328735944, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328737108, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328738184, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328739585, "dur": 2055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328741641, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328743307, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328744798, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328745907, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328747020, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328748273, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328749326, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328749574, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748424328751419, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328751869, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328751922, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328752014, "dur": 1730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328753793, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328754878, "dur": 4966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748424328759844, "dur": 885, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328760737, "dur": 1019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328761756, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328761828, "dur": 8386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748424328770215, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328770648, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328770941, "dur": 4762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748424328775704, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328775824, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328775900, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328776449, "dur": 2817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748424328779267, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328779582, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748424328780208, "dur": 3166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748424328783375, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328783641, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424328783854, "dur": 278762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329062622, "dur": 3232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748424329065855, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329066250, "dur": 4637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748424329070888, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329070972, "dur": 3324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748424329074297, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329074374, "dur": 3934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748424329078316, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329078415, "dur": 3233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748424329081650, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329082076, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748424329082219, "dur": 5595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748424329087853, "dur": 3600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748424329091455, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329091610, "dur": 4292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748424329095903, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329096190, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329096591, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329096996, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329097417, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329097823, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329098280, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748424329098352, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329098505, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329098603, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329099134, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329099225, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748424329099293, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329099656, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329099913, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329100001, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748424329100464, "dur": 2465633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328588309, "dur": 112552, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328700869, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328701255, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328701372, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328701616, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328702119, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328702202, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328702266, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328702400, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328702460, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328702557, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328702615, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328702776, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328702911, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328702992, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328703074, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748424328703379, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328704182, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748424328704510, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748424328704753, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328704916, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328705367, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328705498, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748424328705866, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748424328706138, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748424328706320, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328706392, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328706615, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328706743, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328706802, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328707028, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328707503, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328707959, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748424328708226, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328708408, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328708995, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328709111, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328709408, "dur": 1267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328710676, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328710863, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328711376, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328711828, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748424328711930, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748424328712115, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328713023, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328713502, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328713578, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748424328714359, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328714544, "dur": 2023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328716567, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328718200, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328719317, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328720420, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328721514, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328722616, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328723672, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328724730, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328725853, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328728080, "dur": 742, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ShaderGUI/ShadingModels/LitDetailGUI.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748424328726913, "dur": 2060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328728973, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328730242, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328731502, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328732702, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328733860, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328734931, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328736023, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328737129, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328738223, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328739594, "dur": 1995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328741590, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328743288, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328744745, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328745884, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328746976, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328748203, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328749313, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328749567, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328750410, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328750850, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C5B5631EE4DDD44F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328750923, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328751014, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328753338, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328754527, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328755460, "dur": 1033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328756522, "dur": 3322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328759845, "dur": 975, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328760832, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_1DF7BBC55D029A75.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328760954, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328761692, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328761756, "dur": 1751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328763507, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328763728, "dur": 2935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328766664, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328766826, "dur": 3578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328770404, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328770521, "dur": 923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328771445, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328771907, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328772249, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328772434, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328772826, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328773029, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328773296, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328774570, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328775027, "dur": 1508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328776536, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328776709, "dur": 2140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328778850, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328778993, "dur": 1583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748424328780577, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328780789, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Editor.ref.dll_3C7C2928F291E90C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748424328780953, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328781197, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328781311, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748424328781486, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328781761, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328781975, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328782214, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328782321, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328782397, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328782487, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328782648, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328783398, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328783515, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424328783855, "dur": 278739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424329062600, "dur": 6078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748424329068679, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424329068994, "dur": 3626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748424329072622, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424329072740, "dur": 3038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748424329075779, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424329075873, "dur": 3500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748424329079374, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424329079465, "dur": 3687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748424329083189, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748424329083261, "dur": 5678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748424329089002, "dur": 4583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748424329093586, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424329093661, "dur": 6689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748424329100351, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748424329100453, "dur": 2465652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328588316, "dur": 112557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328700875, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328701283, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328701517, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328701585, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328701972, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328702078, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328702152, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328702213, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328702320, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328702401, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328702525, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328702605, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328702737, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328702790, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328702881, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328702941, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328703048, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748424328703440, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328703540, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328703912, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328704689, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328704838, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328705039, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328705113, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328706316, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328706617, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328706685, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328706753, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328707347, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328708018, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748424328708207, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748424328708294, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328708908, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748424328709192, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328709753, "dur": 1374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328711147, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328711619, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328711914, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748424328712038, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328712120, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328713218, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328713271, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328713962, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748424328714203, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328715667, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328717361, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328718767, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328719899, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328721008, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328722064, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328723159, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328724228, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328725330, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328726454, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328728100, "dur": 749, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/PostProcessDataEditor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748424328727707, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328729640, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328730993, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328732190, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328733419, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328734514, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328734610, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328734688, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328735876, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328736987, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328738091, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328739507, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328741493, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328743186, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328744642, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328745749, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328746849, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328748062, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328749215, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328749594, "dur": 3237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424328752831, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328753318, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328754586, "dur": 3049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424328757635, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328757981, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D33AAA747FCEED0A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328758032, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328758094, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328759200, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328759259, "dur": 2923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424328762183, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328762738, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328763155, "dur": 5378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424328768534, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328768958, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328770247, "dur": 4314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424328774562, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328774996, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328775470, "dur": 2624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424328778095, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328778285, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328778796, "dur": 3116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424328781913, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328782141, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328782490, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328782554, "dur": 1493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424328784047, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328784419, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748424328784641, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424328785268, "dur": 50, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424328785359, "dur": 2001966, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748424330789316, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748424330788728, "dur": 2762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748424330792391, "dur": 304, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424331557530, "dur": 386, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424330792748, "dur": 765195, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748424331562900, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748424331562883, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748424331563025, "dur": 83, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748424331563112, "dur": 2962, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748424331571230, "dur": 1131, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 72182, "tid": 12, "ts": 1748424331613742, "dur": 2973, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 72182, "tid": 12, "ts": 1748424331616876, "dur": 2332, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 72182, "tid": 12, "ts": 1748424331601083, "dur": 19546, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}