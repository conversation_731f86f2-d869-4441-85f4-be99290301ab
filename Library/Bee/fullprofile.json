{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 70593, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 70593, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 70593, "tid": 77, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 70593, "tid": 77, "ts": 1748420255889918, "dur": 3041, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 70593, "tid": 77, "ts": 1748420255950011, "dur": 3793, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 70593, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 70593, "tid": 1, "ts": 1748420251470016, "dur": 93149, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748420251563173, "dur": 216412, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748420251779601, "dur": 657039, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 70593, "tid": 77, "ts": 1748420255953815, "dur": 234, "ph": "X", "name": "", "args": {}}, {"pid": 70593, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251464423, "dur": 129248, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251593676, "dur": 4230580, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251596251, "dur": 11521, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251607782, "dur": 4279, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251612069, "dur": 19978, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251632059, "dur": 789, "ph": "X", "name": "ProcessMessages 8118", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251633241, "dur": 124, "ph": "X", "name": "ReadAsync 8118", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251633395, "dur": 12, "ph": "X", "name": "ProcessMessages 8127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634109, "dur": 104, "ph": "X", "name": "ReadAsync 8127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634238, "dur": 9, "ph": "X", "name": "ProcessMessages 7876", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634248, "dur": 85, "ph": "X", "name": "ReadAsync 7876", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634367, "dur": 3, "ph": "X", "name": "ProcessMessages 1283", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634373, "dur": 176, "ph": "X", "name": "ReadAsync 1283", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634568, "dur": 4, "ph": "X", "name": "ProcessMessages 2771", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634588, "dur": 116, "ph": "X", "name": "ReadAsync 2771", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634739, "dur": 2, "ph": "X", "name": "ProcessMessages 1270", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634743, "dur": 86, "ph": "X", "name": "ReadAsync 1270", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634833, "dur": 3, "ph": "X", "name": "ProcessMessages 1192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251634838, "dur": 157, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635018, "dur": 4, "ph": "X", "name": "ProcessMessages 1457", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635025, "dur": 113, "ph": "X", "name": "ReadAsync 1457", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635144, "dur": 4, "ph": "X", "name": "ProcessMessages 2015", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635150, "dur": 55, "ph": "X", "name": "ReadAsync 2015", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635211, "dur": 2, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635216, "dur": 122, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635343, "dur": 3, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635376, "dur": 114, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635494, "dur": 2, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251635498, "dur": 757, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251636268, "dur": 4, "ph": "X", "name": "ProcessMessages 1777", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251636281, "dur": 165, "ph": "X", "name": "ReadAsync 1777", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251636452, "dur": 3, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251636457, "dur": 148, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251638399, "dur": 85, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251638523, "dur": 176, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251638706, "dur": 13, "ph": "X", "name": "ProcessMessages 7455", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251638720, "dur": 103, "ph": "X", "name": "ReadAsync 7455", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251638854, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251638945, "dur": 124, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251639135, "dur": 3, "ph": "X", "name": "ProcessMessages 1447", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251639142, "dur": 136, "ph": "X", "name": "ReadAsync 1447", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251639380, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251639383, "dur": 811, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251640213, "dur": 15, "ph": "X", "name": "ProcessMessages 1581", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251640234, "dur": 86, "ph": "X", "name": "ReadAsync 1581", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251640325, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251640329, "dur": 278, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251640612, "dur": 5, "ph": "X", "name": "ProcessMessages 1774", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251640619, "dur": 2048, "ph": "X", "name": "ReadAsync 1774", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251642681, "dur": 4, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251642786, "dur": 1579, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251644462, "dur": 18, "ph": "X", "name": "ProcessMessages 8186", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251644491, "dur": 299, "ph": "X", "name": "ReadAsync 8186", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251644861, "dur": 41, "ph": "X", "name": "ProcessMessages 4971", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251644920, "dur": 104, "ph": "X", "name": "ReadAsync 4971", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251645088, "dur": 5, "ph": "X", "name": "ProcessMessages 1761", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251645115, "dur": 86, "ph": "X", "name": "ReadAsync 1761", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251645221, "dur": 3, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251645226, "dur": 104, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251645334, "dur": 3, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251645339, "dur": 86, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251645445, "dur": 3, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251645451, "dur": 18387, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251663871, "dur": 29, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251663906, "dur": 151, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664074, "dur": 15, "ph": "X", "name": "ProcessMessages 8184", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664093, "dur": 132, "ph": "X", "name": "ReadAsync 8184", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664248, "dur": 2, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664252, "dur": 163, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664461, "dur": 4, "ph": "X", "name": "ProcessMessages 1126", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664468, "dur": 124, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664598, "dur": 2, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664603, "dur": 110, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664729, "dur": 27, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664760, "dur": 130, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664920, "dur": 22, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251664944, "dur": 150, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251665117, "dur": 29, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251665152, "dur": 623, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251665797, "dur": 3, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251665802, "dur": 147, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251665973, "dur": 4, "ph": "X", "name": "ProcessMessages 1336", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251665980, "dur": 204, "ph": "X", "name": "ReadAsync 1336", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251666226, "dur": 15, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251666258, "dur": 134, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251666395, "dur": 3, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251666400, "dur": 558, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251666964, "dur": 16, "ph": "X", "name": "ProcessMessages 3914", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251666985, "dur": 268, "ph": "X", "name": "ReadAsync 3914", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667259, "dur": 2, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667262, "dur": 111, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667412, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667416, "dur": 109, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667555, "dur": 2, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667560, "dur": 130, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667732, "dur": 3, "ph": "X", "name": "ProcessMessages 1177", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667738, "dur": 83, "ph": "X", "name": "ReadAsync 1177", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667826, "dur": 3, "ph": "X", "name": "ProcessMessages 1329", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667831, "dur": 118, "ph": "X", "name": "ReadAsync 1329", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251667980, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251668007, "dur": 81, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251668108, "dur": 4, "ph": "X", "name": "ProcessMessages 1255", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251668115, "dur": 11448, "ph": "X", "name": "ReadAsync 1255", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251684782, "dur": 16, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251685224, "dur": 399, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251685642, "dur": 17, "ph": "X", "name": "ProcessMessages 8173", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251685666, "dur": 773, "ph": "X", "name": "ReadAsync 8173", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251688230, "dur": 6, "ph": "X", "name": "ProcessMessages 1530", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251688241, "dur": 29763, "ph": "X", "name": "ReadAsync 1530", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251718235, "dur": 18, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251718257, "dur": 175, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251718438, "dur": 15, "ph": "X", "name": "ProcessMessages 8187", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251718455, "dur": 122, "ph": "X", "name": "ReadAsync 8187", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251718625, "dur": 3, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251718631, "dur": 187, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251718956, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251718961, "dur": 217, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251719676, "dur": 5, "ph": "X", "name": "ProcessMessages 1654", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251719684, "dur": 226, "ph": "X", "name": "ReadAsync 1654", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251719967, "dur": 5, "ph": "X", "name": "ProcessMessages 1978", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251720355, "dur": 276, "ph": "X", "name": "ReadAsync 1978", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251720637, "dur": 8, "ph": "X", "name": "ProcessMessages 3659", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251721303, "dur": 1277, "ph": "X", "name": "ReadAsync 3659", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251729132, "dur": 12, "ph": "X", "name": "ProcessMessages 4222", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251729148, "dur": 7020, "ph": "X", "name": "ReadAsync 4222", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251736175, "dur": 14, "ph": "X", "name": "ProcessMessages 8151", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251736192, "dur": 194, "ph": "X", "name": "ReadAsync 8151", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251736735, "dur": 37, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251736798, "dur": 39420, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776230, "dur": 15, "ph": "X", "name": "ProcessMessages 8130", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776248, "dur": 98, "ph": "X", "name": "ReadAsync 8130", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776353, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776357, "dur": 103, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776468, "dur": 3, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776473, "dur": 105, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776583, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776587, "dur": 87, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776680, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251776684, "dur": 410, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777100, "dur": 5, "ph": "X", "name": "ProcessMessages 1781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777107, "dur": 121, "ph": "X", "name": "ReadAsync 1781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777234, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777258, "dur": 124, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777387, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777391, "dur": 99, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777497, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777501, "dur": 228, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777735, "dur": 3, "ph": "X", "name": "ProcessMessages 1251", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777741, "dur": 100, "ph": "X", "name": "ReadAsync 1251", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777846, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251777850, "dur": 159, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251778039, "dur": 3, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251778045, "dur": 188, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251778240, "dur": 2, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251778245, "dur": 102, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251778349, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251778352, "dur": 74, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251778432, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251778436, "dur": 714, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251779158, "dur": 7, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251779167, "dur": 2772, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251781950, "dur": 4, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251781956, "dur": 329, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251782292, "dur": 2, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251782297, "dur": 2843, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251785151, "dur": 4, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251785157, "dur": 1634, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251786801, "dur": 6, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251786809, "dur": 278, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251787099, "dur": 15, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251787131, "dur": 2506, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251789670, "dur": 5, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251789678, "dur": 2870, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251792582, "dur": 4, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251792589, "dur": 1982, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251794579, "dur": 3, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251794584, "dur": 2734, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251797327, "dur": 3, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251797332, "dur": 424, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251797763, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251797767, "dur": 834, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251798607, "dur": 2, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251798611, "dur": 971, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251799590, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251799595, "dur": 126, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251799727, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251799731, "dur": 1448, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251801198, "dur": 3, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251801203, "dur": 184, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251801394, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251801398, "dur": 2599, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251804005, "dur": 3, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251804011, "dur": 739, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251804757, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251804763, "dur": 2163, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251807009, "dur": 4, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251807015, "dur": 243, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251807337, "dur": 3, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251807343, "dur": 2669, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251810021, "dur": 4, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251810060, "dur": 486, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251810553, "dur": 3, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251810558, "dur": 1371, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251811981, "dur": 3, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251811986, "dur": 1652, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251813645, "dur": 4, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251813652, "dur": 3249, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251816910, "dur": 3, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251816915, "dur": 2159, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251819087, "dur": 3, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251819093, "dur": 872, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251819973, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251819978, "dur": 1716, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251824386, "dur": 6, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251824460, "dur": 1961, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251826431, "dur": 7, "ph": "X", "name": "ProcessMessages 1934", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251826441, "dur": 392, "ph": "X", "name": "ReadAsync 1934", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251827512, "dur": 30, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251827548, "dur": 454, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251828009, "dur": 3, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251828014, "dur": 1442, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251829597, "dur": 2206, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251831813, "dur": 2476, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251834302, "dur": 9, "ph": "X", "name": "ProcessMessages 3689", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251834358, "dur": 450, "ph": "X", "name": "ReadAsync 3689", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251834815, "dur": 3, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251834821, "dur": 2932, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251837780, "dur": 4, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251837787, "dur": 431, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251838224, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251838228, "dur": 547, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251838784, "dur": 12, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251838801, "dur": 159, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251838969, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251838973, "dur": 1172, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251840153, "dur": 4, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251840200, "dur": 1836, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251842043, "dur": 25, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251842070, "dur": 4999, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251847079, "dur": 4, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251847085, "dur": 283, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251847374, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251847378, "dur": 862, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251848247, "dur": 3, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251848252, "dur": 124, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251848381, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251848384, "dur": 1013, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251849402, "dur": 3, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251849442, "dur": 4276, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251853727, "dur": 4, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251853734, "dur": 7090, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251863559, "dur": 32, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251863595, "dur": 120, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251863721, "dur": 4, "ph": "X", "name": "ProcessMessages 1895", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251863728, "dur": 2854, "ph": "X", "name": "ReadAsync 1895", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251866590, "dur": 3, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251866596, "dur": 260, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251866867, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251866871, "dur": 886, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251867764, "dur": 4, "ph": "X", "name": "ProcessMessages 1289", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251867771, "dur": 4091, "ph": "X", "name": "ReadAsync 1289", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251871870, "dur": 4, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251871876, "dur": 1227, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251873162, "dur": 4, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251873169, "dur": 113, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251873288, "dur": 2, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251873292, "dur": 4695, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251877997, "dur": 30, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251878030, "dur": 6673, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251884712, "dur": 4, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251884719, "dur": 1028, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251885756, "dur": 3, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251885761, "dur": 24132, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251909903, "dur": 7, "ph": "X", "name": "ProcessMessages 2397", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251909913, "dur": 3605, "ph": "X", "name": "ReadAsync 2397", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251913526, "dur": 19, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251913548, "dur": 1466, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251915019, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251915023, "dur": 4856, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251919887, "dur": 16, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251919910, "dur": 7833, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251927801, "dur": 17, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251927840, "dur": 7847, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251935698, "dur": 5, "ph": "X", "name": "ProcessMessages 2346", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251935704, "dur": 6260, "ph": "X", "name": "ReadAsync 2346", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251941975, "dur": 4, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251941981, "dur": 6741, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251949061, "dur": 29, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251949094, "dur": 4156, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251953262, "dur": 5, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251953269, "dur": 9036, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251962329, "dur": 5, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251962338, "dur": 1961, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251964311, "dur": 4, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251964317, "dur": 168, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251964491, "dur": 3, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251964496, "dur": 82, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251964583, "dur": 2, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251964586, "dur": 114, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251964708, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251964712, "dur": 5823, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251970545, "dur": 3, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251970550, "dur": 3231, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251973792, "dur": 590, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251974388, "dur": 139, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251974535, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251974539, "dur": 1554, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976102, "dur": 7, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976111, "dur": 101, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976220, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976224, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976352, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976357, "dur": 108, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976485, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976489, "dur": 173, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976670, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976674, "dur": 168, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976848, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251976851, "dur": 356, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251977213, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251977217, "dur": 203, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251977427, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251977430, "dur": 312, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251977749, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251977753, "dur": 336, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251978116, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251978122, "dur": 92, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251978222, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251978226, "dur": 647, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251978881, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251978886, "dur": 247, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251979140, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251979144, "dur": 419, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251979584, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251979589, "dur": 107, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251979725, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251979747, "dur": 206, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251979960, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251979965, "dur": 82, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980053, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980056, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980172, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980175, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980365, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980369, "dur": 221, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980597, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980601, "dur": 262, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980870, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251980874, "dur": 1491, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251982374, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251982379, "dur": 143, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251982530, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251982534, "dur": 2102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251984645, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251984651, "dur": 198, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251984856, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251984861, "dur": 186, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251985054, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251985057, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251985166, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251985170, "dur": 1191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251986368, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251986372, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251986481, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251986484, "dur": 275, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251986804, "dur": 3, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251986809, "dur": 76, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251986893, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251986896, "dur": 1486, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251988404, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251988408, "dur": 325, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251988739, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251988744, "dur": 304, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251989054, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251989058, "dur": 119, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251989182, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251989185, "dur": 317, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251989508, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251989536, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251989828, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251989830, "dur": 719, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251990556, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251990561, "dur": 81, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251990647, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251990650, "dur": 356, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251991011, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251991015, "dur": 1030, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992051, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992056, "dur": 186, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992248, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992252, "dur": 130, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992389, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992394, "dur": 86, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992535, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992539, "dur": 83, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992628, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992631, "dur": 93, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992730, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992734, "dur": 242, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992982, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251992985, "dur": 1401, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251994394, "dur": 4, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251994400, "dur": 91, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251994497, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251994501, "dur": 84, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251994590, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251994637, "dur": 580, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251995224, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251995227, "dur": 1288, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251996549, "dur": 35, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251996591, "dur": 297, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251996895, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251996900, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251997108, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251997112, "dur": 332, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251997448, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420251997451, "dur": 3867, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252001327, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252001333, "dur": 958, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252002300, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252002305, "dur": 510, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252002825, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252002864, "dur": 99, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252002969, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252002972, "dur": 1048, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252004047, "dur": 5, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252004054, "dur": 87, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252004148, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252004151, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252004249, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252004252, "dur": 396, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252004653, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252004657, "dur": 1815, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252006480, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252006485, "dur": 424, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252006916, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252006922, "dur": 331, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252007260, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252007264, "dur": 232, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252007501, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252007504, "dur": 216, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252007727, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252007731, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252007862, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252007865, "dur": 214, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252008086, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252008089, "dur": 566, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252008661, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252008664, "dur": 678, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252009346, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252009350, "dur": 245, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252009602, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252009606, "dur": 296, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252009906, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252009909, "dur": 628, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252010542, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252010545, "dur": 3159, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252013714, "dur": 48, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252013814, "dur": 4089, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252017915, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252017919, "dur": 843, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252018770, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252018775, "dur": 555, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252019337, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252019342, "dur": 1495, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252020844, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252020848, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252020936, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252020939, "dur": 738, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252021698, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252021702, "dur": 427, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252022148, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252022152, "dur": 2419, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252024581, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252024585, "dur": 524, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252025116, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252025120, "dur": 805, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252025934, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252025939, "dur": 1219, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252027168, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252027172, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252027362, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252027384, "dur": 436, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252027827, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252027830, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252028007, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252028010, "dur": 817, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252028836, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252028840, "dur": 359, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252029206, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252029209, "dur": 813, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252030029, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252030033, "dur": 430, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252030469, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252030473, "dur": 106, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252030585, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252030589, "dur": 222, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252030818, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252030854, "dur": 1005, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252031868, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252031872, "dur": 1898, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252033779, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252033784, "dur": 1988, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252035781, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252035786, "dur": 289, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252036083, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252036086, "dur": 284, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252036377, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252036399, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252036663, "dur": 1620, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252038291, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252038512, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252038517, "dur": 197, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252038721, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252038724, "dur": 2985, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252041719, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252041724, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252041968, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252041971, "dur": 2941, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252044922, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252044927, "dur": 8538, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252053501, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252053507, "dur": 5458, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252058974, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252058979, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252059103, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252059106, "dur": 521, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252059634, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252059638, "dur": 2129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252061773, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252061777, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252061851, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252061955, "dur": 2731, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252064694, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252064700, "dur": 3094, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252067803, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252067808, "dur": 35842, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252103662, "dur": 1429, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252105097, "dur": 354, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252105457, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252105500, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252105777, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252105781, "dur": 883, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252106679, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252106687, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252106790, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252106814, "dur": 1028, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252107851, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252107915, "dur": 5774, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252113698, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252113704, "dur": 97126, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252210845, "dur": 7, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252210854, "dur": 33451, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252244318, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252244325, "dur": 23890, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252268234, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252268240, "dur": 22603, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252290858, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252290864, "dur": 24345, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252315233, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252315240, "dur": 10691, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252325985, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252325996, "dur": 1958, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252327964, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252327969, "dur": 21946, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252349926, "dur": 10, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252349938, "dur": 353, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252350299, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252350303, "dur": 388, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252350697, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252350701, "dur": 10832, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252361542, "dur": 15, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252361563, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252361701, "dur": 5, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252361708, "dur": 780, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252362495, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252362499, "dur": 20504, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252383014, "dur": 23, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252383054, "dur": 2910, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252385972, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252385977, "dur": 387, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252386371, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252386375, "dur": 490, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252386871, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252386887, "dur": 428, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252387323, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252387327, "dur": 10308, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252397649, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420252397656, "dur": 841384, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253239112, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253239118, "dur": 344, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253239470, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253239474, "dur": 155, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253239635, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253239638, "dur": 78, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253239721, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253239725, "dur": 70, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253239800, "dur": 1, "ph": "X", "name": "ProcessMessages 4138", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253240260, "dur": 224, "ph": "X", "name": "ReadAsync 4138", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253240496, "dur": 5, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253240508, "dur": 166, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253240681, "dur": 13458, "ph": "X", "name": "ProcessMessages 4842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253254149, "dur": 1329, "ph": "X", "name": "ReadAsync 4842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253255486, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253255492, "dur": 1393, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253256904, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253256913, "dur": 2168, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253259089, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253259093, "dur": 1950, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253261055, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253261061, "dur": 3580, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253264652, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253264657, "dur": 1412, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253266079, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253266083, "dur": 474, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253266568, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253266572, "dur": 2161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253268742, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253268747, "dur": 4327, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253273084, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253273088, "dur": 302, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253273434, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253273441, "dur": 3956, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253277406, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253277411, "dur": 1603, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253279026, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253279032, "dur": 1261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253280303, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253280308, "dur": 3045, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253283363, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253283369, "dur": 1978, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253285358, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253285363, "dur": 1626, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253286998, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253287003, "dur": 3112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253290149, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253290154, "dur": 1186, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253291348, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253291353, "dur": 8668, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253300030, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253300035, "dur": 506, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253300548, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253300552, "dur": 10036, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253310600, "dur": 10, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253310612, "dur": 162, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253310785, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253310789, "dur": 195, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253311013, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253311017, "dur": 139, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253311160, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253311164, "dur": 114, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253311284, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253311288, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253311382, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253311385, "dur": 908, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253312303, "dur": 12, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253312322, "dur": 287, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253312615, "dur": 5, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253312623, "dur": 102, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253312731, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253312735, "dur": 168, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253312908, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253312912, "dur": 131, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253313047, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253313051, "dur": 107, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253313164, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253313167, "dur": 110, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253313281, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253313285, "dur": 132, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253313423, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253313426, "dur": 579, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253314012, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253314017, "dur": 86, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253314109, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253314113, "dur": 821, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253314942, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253314946, "dur": 754, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253315770, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253315776, "dur": 743, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253316528, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253316532, "dur": 3302, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253319844, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420253319851, "dur": 1117672, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420254437655, "dur": 197, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420254437938, "dur": 9988, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420254447936, "dur": 16, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420254447955, "dur": 1344681, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255792656, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255792661, "dur": 103, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255792779, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255792783, "dur": 89, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255792885, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255792888, "dur": 82, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255792975, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255792978, "dur": 366, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255793349, "dur": 133, "ph": "X", "name": "ProcessMessages 2629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255793485, "dur": 10109, "ph": "X", "name": "ReadAsync 2629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255803687, "dur": 77, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255803787, "dur": 3324, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255807122, "dur": 118, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255807244, "dur": 437, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255807689, "dur": 2044, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748420255809743, "dur": 13363, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 70593, "tid": 77, "ts": 1748420255954053, "dur": 10909, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 70593, "tid": 8589934592, "ts": 1748420251443325, "dur": 993446, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748420252436777, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748420252436789, "dur": 31621, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 70593, "tid": 77, "ts": 1748420255964967, "dur": 68, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 70593, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 70593, "tid": 4294967296, "ts": 1748420251144055, "dur": 4691212, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748420251288245, "dur": 122094, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748420255836908, "dur": 34452, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748420255850102, "dur": 12827, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748420255871765, "dur": 61, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 70593, "tid": 77, "ts": 1748420255965039, "dur": 19, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748420251562522, "dur": 90, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420251580881, "dur": 59, "ph": "X", "name": "EmitBuildStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420251564882, "dur": 22116, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420251587089, "dur": 42790, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420251630017, "dur": 137, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748420251630155, "dur": 343, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420251630592, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1748420251630903, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251631530, "dur": 626, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251632193, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251633076, "dur": 354, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251636426, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251636529, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251636588, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251636882, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251638905, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251640883, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251642343, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251642530, "dur": 429, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251642967, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251643456, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251643529, "dur": 442, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251644229, "dur": 246, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251644803, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748420251647064, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251647247, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251647396, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251647501, "dur": 16615, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251664237, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251664402, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251665689, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251667049, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251667251, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_3F93312DF16ABE8A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251667393, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748420251669194, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_BF69964832659484.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251669753, "dur": 15838, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251685952, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_697AC546F729FDEE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251686630, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251686906, "dur": 155, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748420251687069, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251687125, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251687198, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251687410, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_3971D385965D884C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251687698, "dur": 19072, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251707200, "dur": 127, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_8E9298DC6B12ED57.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251707652, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_BC6ADFF987BC82A3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251707959, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251708062, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251708819, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Editor.ref.dll_3C7C2928F291E90C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251708882, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251708986, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748420251709048, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251709156, "dur": 227, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251709786, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251709895, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251709971, "dur": 8470, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251718453, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251718551, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251718763, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251718852, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748420251718970, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251719043, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251719244, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Psdimporter.Editor.ref.dll_47CE2970C8879D28.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251719402, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748420251719771, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748420251719837, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251719945, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251720112, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Editor.ref.dll_AB52260E0554293F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251720263, "dur": 141, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251720502, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251720785, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251720901, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251721291, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251721505, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251721582, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251721691, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748420251721907, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251722104, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251722280, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251722902, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251723176, "dur": 13031, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748420251736269, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251737243, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251737490, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251737699, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251738862, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251739252, "dur": 37043, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251776341, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251776719, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251776834, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748420251777003, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251777106, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251777255, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251777393, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251777524, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251777589, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251778047, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251778200, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748420251781611, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251781765, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251782152, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251784759, "dur": 197, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251784964, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251786476, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251786582, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251789432, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251789495, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251789590, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251794236, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251794335, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251803686, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251804390, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251806550, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251806996, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251809199, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251816625, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251818894, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251819817, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251819873, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251820865, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251821642, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251823521, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251823583, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Common.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251823644, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251824893, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251824959, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251826032, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251827792, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251827867, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251828646, "dur": 163, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251829670, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251829945, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251832950, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251833224, "dur": 150, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251837563, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251838053, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251838635, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251838760, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251839701, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251839761, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251841445, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251841604, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251846586, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251846647, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251847979, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251848046, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251848167, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251849221, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251853062, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251853151, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251853509, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251859313, "dur": 437, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251860651, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251861768, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251863068, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251866343, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251866723, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251867467, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251867563, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251868927, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251868984, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251869340, "dur": 261, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251872911, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251877529, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251882717, "dur": 250, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251885320, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251891466, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251891569, "dur": 254, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251896418, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251903368, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251903425, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251904377, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251919665, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251919764, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251927309, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251927380, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251927490, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251933913, "dur": 219, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251934201, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251941698, "dur": 148, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251948017, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251951216, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251952790, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251952922, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748420251957521, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251957638, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251964123, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251964232, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748420251964518, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748420251970392, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748420251630509, "dur": 339936, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420251970478, "dur": 3836496, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420255807004, "dur": 90, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420255807253, "dur": 108, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420255807454, "dur": 121, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420255807671, "dur": 2321, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748420251630397, "dur": 340152, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251970573, "dur": 2905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748420251973565, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748420251973762, "dur": 1619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251975382, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251975501, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251975605, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251975728, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3C55960BA9CB8745.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251975878, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251976007, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_E69ADDE2B61683F5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251976241, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251976319, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B92AE864B27146B1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251976681, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251976943, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251977317, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251977532, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251977818, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251977950, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251978224, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251978828, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251978892, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251979574, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251979741, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251979854, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748420251979921, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251980153, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251980279, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251980459, "dur": 3850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748420251984321, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748420251984490, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251985125, "dur": 4508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748420251989774, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748420251990449, "dur": 1409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748420251991891, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251992085, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251992204, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748420251992261, "dur": 1440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748420251993728, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748420251994100, "dur": 1499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748420251995601, "dur": 655, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251996270, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420251996913, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420251997009, "dur": 61499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748420252058510, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252058796, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252058883, "dur": 2756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420252061686, "dur": 197891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748420252259579, "dur": 7184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252266764, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748420252266934, "dur": 2137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_95D2DEE1BA1172D7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420252269073, "dur": 5976, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252275051, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_95D2DEE1BA1172D7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420252275168, "dur": 39160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420252314330, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252314490, "dur": 14065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748420252328562, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252329212, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_697AC546F729FDEE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420252329370, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252329555, "dur": 15029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748420252344586, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252345174, "dur": 18573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748420252363749, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252363981, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Editor.ref.dll_3C7C2928F291E90C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420252364060, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252364153, "dur": 1798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252365952, "dur": 2093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252368046, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252370032, "dur": 2316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252372350, "dur": 2869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252375220, "dur": 2267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252377501, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252378138, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252378488, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252379318, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252379621, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252379689, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252379775, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252381615, "dur": 3120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252384738, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252385190, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252385830, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252386170, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748420252386421, "dur": 1323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748420252387745, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420252387810, "dur": 856163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253243979, "dur": 13199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748420253257180, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253257289, "dur": 8908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748420253266198, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253266438, "dur": 6096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748420253272536, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253272621, "dur": 5907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748420253278530, "dur": 906, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253279455, "dur": 5797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748420253285254, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253285563, "dur": 7003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748420253292568, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253292723, "dur": 12423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748420253305148, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253305282, "dur": 10107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748420253315391, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253315807, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253316063, "dur": 3291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748420253319362, "dur": 2487648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251630389, "dur": 340106, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251970572, "dur": 2871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748420251973668, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251975190, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251975274, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251975542, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_C9DCA0B70AE22DB0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251975766, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251975895, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B66FF45CB39FB62A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251976078, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251976241, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AB6C0CE8644C7690.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251976360, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251976513, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251976678, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251976961, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251977335, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251977552, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251977824, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251978024, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251978246, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251978676, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251978800, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251979542, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251979659, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251979986, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251980138, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251980392, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251980610, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_F0DEE4EE0970DD2B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251980808, "dur": 1324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748420251982147, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748420251982315, "dur": 2083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748420251984399, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251984569, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251984761, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251984947, "dur": 1680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748420251986677, "dur": 1643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748420251988321, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251988606, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251988754, "dur": 3114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748420251991900, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748420251991997, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251992115, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748420251992173, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251992374, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748420251992817, "dur": 2518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748420251995358, "dur": 1369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420251996729, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420251996799, "dur": 12175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748420252008976, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420252009387, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_4EAD86AD4C1B715A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420252009454, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420252009530, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420252011009, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420252011125, "dur": 19066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748420252030192, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420252030426, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420252030502, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748420252031192, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420252031270, "dur": 9701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748420252040973, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420252041453, "dur": 19813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1748420252061268, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420252061387, "dur": 6512, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420253238606, "dur": 1853, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420252068592, "dur": 1171956, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1748420253243918, "dur": 11506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748420253255426, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420253255536, "dur": 5106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748420253260644, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420253261130, "dur": 11960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748420253273091, "dur": 986, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420253274091, "dur": 15092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748420253289185, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420253289484, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748420253289589, "dur": 11076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748420253300667, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748420253300821, "dur": 7022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748420253307897, "dur": 11390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748420253319379, "dur": 2487425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251630397, "dur": 340170, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251970578, "dur": 3067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251973646, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251973854, "dur": 1348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251975246, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251975376, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251975542, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251975681, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251975875, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251975967, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251976038, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251976245, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251976326, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_57AB718CCA0A791C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251976494, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251976596, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251976925, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251977213, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251977557, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251977773, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251978052, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251978211, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251979010, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251979509, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251979735, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251979872, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251980097, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251980235, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251980516, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251980836, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748420251982222, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748420251984456, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251984811, "dur": 1456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748420251986302, "dur": 1928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748420251988244, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748420251988335, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251988587, "dur": 3192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748420251991823, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251991926, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251992148, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251992445, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748420251992558, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748420251992642, "dur": 1576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748420251994247, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748420251994550, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748420251997123, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251997519, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420251997839, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420251997918, "dur": 5168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748420252003087, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252003483, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252003623, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252003756, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252003957, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252004064, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252004227, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252004476, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748420252006739, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252006830, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748420252006904, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252007113, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252007286, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252007385, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252007502, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252007626, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252007881, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252008007, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252008105, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252010415, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252011532, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252011619, "dur": 9671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748420252021291, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252021565, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252021816, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252022002, "dur": 5093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252027096, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252027227, "dur": 3126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252030354, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252030534, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252031244, "dur": 10182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748420252041428, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252041810, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Runtime.ref.dll_DEAB70CB54ED1324.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252041900, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252041977, "dur": 1422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252043401, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252043485, "dur": 31394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748420252074882, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252075226, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252075447, "dur": 1218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748420252076667, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252077042, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252077133, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252077284, "dur": 1456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252078742, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252078824, "dur": 2711, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252081556, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252081764, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252081882, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252082091, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Editor.ref.dll_5A2087A6EAF2CE17.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252082208, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252082310, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252082603, "dur": 24794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748420252107399, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252107749, "dur": 6591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252114349, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252114443, "dur": 4030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252118540, "dur": 2995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252121537, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252121767, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252121898, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252122031, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252122115, "dur": 200303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748420252322421, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252323054, "dur": 15745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748420252338801, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252339318, "dur": 12417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748420252351737, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252352044, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748420252352197, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252352320, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252352438, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748420252352498, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252352556, "dur": 2219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252354776, "dur": 2888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252357665, "dur": 2495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252360161, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252362076, "dur": 1869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252363946, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252365755, "dur": 2094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252367851, "dur": 1875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252369727, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252371991, "dur": 2794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252374786, "dur": 2391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252377178, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252377419, "dur": 84, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252377504, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252377649, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252378051, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252378255, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252378427, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252379329, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252379626, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252380512, "dur": 3162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252383674, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252384487, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252385191, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252385836, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252386714, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252387119, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252387223, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420252387304, "dur": 856621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253243940, "dur": 6061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748420253250003, "dur": 787, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253250828, "dur": 5759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748420253256588, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253256917, "dur": 8932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748420253265851, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253266224, "dur": 7088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748420253273313, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253273674, "dur": 9491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748420253283166, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253283410, "dur": 7703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748420253291115, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253291265, "dur": 8788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748420253300054, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253300259, "dur": 11268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748420253311528, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253311698, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253311878, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253311965, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253312032, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748420253312098, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253312175, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748420253312357, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253312423, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748420253312543, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253312691, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253312767, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253312831, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748420253312929, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253313064, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253313235, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748420253313294, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253313438, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748420253313508, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253313663, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748420253313855, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253314088, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253314177, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253314296, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748420253314720, "dur": 769, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253315503, "dur": 2263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748420253317813, "dur": 2489079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251630433, "dur": 340206, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251970668, "dur": 3084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251973753, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251973834, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251973906, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1996541149DA13E0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251974055, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251975199, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251975430, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_CAA42EBCF9A9A6C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251975650, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251975799, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_2EC518F8CEEB6CA9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251975957, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251976122, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251976303, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251976379, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251976528, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251976650, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251977028, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251977153, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251977498, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251977672, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251977931, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251978105, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_51CCB6F491E5150D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251978279, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251978439, "dur": 1068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251979508, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251979625, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251979831, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251980038, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251980209, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251980331, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251980550, "dur": 3597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420251984180, "dur": 2096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748420251986409, "dur": 1628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748420251988085, "dur": 2234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420251990420, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420251990564, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251990829, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748420251991893, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251992066, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251992224, "dur": 1940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748420251994283, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420251994566, "dur": 1123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420251995690, "dur": 855, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420251996555, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420251996869, "dur": 9227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252006098, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252006451, "dur": 2676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252009128, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252009206, "dur": 7981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252017189, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252017620, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D33AAA747FCEED0A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252017829, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252018072, "dur": 4418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252022492, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252022563, "dur": 5490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252028060, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252028301, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252030612, "dur": 6463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252037077, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252037475, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_E30EE1A78470D274.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252037545, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252037644, "dur": 10956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252048602, "dur": 1437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252050060, "dur": 13365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252063427, "dur": 1108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252064555, "dur": 6032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252070589, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252070859, "dur": 19872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252090732, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252091171, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Runtime.ref.dll_23B941DDE597517C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252091283, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252091593, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252091752, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252091876, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420252091942, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252092130, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252092236, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252092395, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252092528, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252092648, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252093188, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252093498, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252093681, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252093802, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252093927, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252094010, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252094124, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252094250, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252094340, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252094415, "dur": 3465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252097882, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252097992, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420252098061, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252098279, "dur": 4568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420252102849, "dur": 844, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252103695, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420252103775, "dur": 1684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420252105460, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252105587, "dur": 4400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252109989, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252110097, "dur": 1809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252111907, "dur": 1086, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252113012, "dur": 3143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252116161, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252116279, "dur": 4733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252121123, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252121280, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420252121421, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252121553, "dur": 1316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748420252122871, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252123463, "dur": 8173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252131638, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252132146, "dur": 82489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252214637, "dur": 26267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252240950, "dur": 86370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252327322, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252327682, "dur": 7026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252334710, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252335258, "dur": 14158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252349418, "dur": 877, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252350327, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_258B0455F8F51CDE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252350388, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252350511, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252350615, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748420252350697, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252350994, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748420252351136, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252351334, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252351418, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748420252351493, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252351692, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252351789, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748420252351846, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252351989, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252352123, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252352226, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252352322, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252352500, "dur": 2386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252354887, "dur": 2831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252357718, "dur": 2348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252360069, "dur": 3303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252363373, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252363467, "dur": 11212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748420252374680, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252375263, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_AD2FFB7646FD92B4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748420252375458, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252375567, "dur": 2132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252377700, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252378183, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252378532, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252379030, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252379401, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252379687, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252379968, "dur": 3144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252383114, "dur": 2151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252385946, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252386382, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252386760, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252386815, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252386932, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420252387311, "dur": 856673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253244011, "dur": 9530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748420253253543, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253253704, "dur": 13191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748420253266897, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253267037, "dur": 8337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748420253275375, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253275466, "dur": 5983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748420253281450, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253282057, "dur": 7951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748420253290009, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253290219, "dur": 8495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748420253298716, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253298835, "dur": 4692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748420253303529, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253303707, "dur": 5124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748420253308832, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253309102, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253309168, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253309264, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253309337, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253310087, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253310733, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253310959, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748420253311033, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253311454, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253311533, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253311610, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253311768, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253311914, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748420253311972, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253312090, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253312253, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748420253312307, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253312569, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748420253312622, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253312902, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253312981, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253313066, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253313134, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748420253313214, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253313331, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748420253313400, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253313573, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253313800, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253313920, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253313971, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748420253314072, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253314170, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253314272, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748420253314728, "dur": 798, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253315535, "dur": 3779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748420253319376, "dur": 2487674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251630480, "dur": 340247, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251970740, "dur": 2841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251973599, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251973675, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_9909B9D5D2D6D40E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251973795, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251973888, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_25D10CC8709418E4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251974019, "dur": 1206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251975246, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_888C5CF0B798DF8B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251975483, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251975602, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251976689, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251977059, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251977418, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251977601, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251977856, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251978069, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251978382, "dur": 848, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251979248, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251979659, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251979800, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748420251982197, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251982497, "dur": 6505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420251989100, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251989437, "dur": 4048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420251993486, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251993608, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251994010, "dur": 1033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420251995137, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251995384, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420251995449, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420251995706, "dur": 10390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420252006098, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252006463, "dur": 2987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252009496, "dur": 9290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420252018788, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252019233, "dur": 4735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252023970, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252024257, "dur": 4749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252029064, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252030606, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252030680, "dur": 28148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420252058830, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252059118, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_57B051C6BAF6DF9D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252059206, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252059297, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252061717, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252061828, "dur": 298574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420252360404, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252360842, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_BF69964832659484.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252360947, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252361114, "dur": 2828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252363948, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252364037, "dur": 21757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420252385796, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252386034, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420252386166, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252386381, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252386473, "dur": 1412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420252387965, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748420252388249, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420252389587, "dur": 156, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420252389803, "dur": 2047187, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748420254439581, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748420254439537, "dur": 5237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748420254447050, "dur": 688, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420255792411, "dur": 717, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748420254447794, "dur": 1345360, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748420255803211, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748420255803185, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748420255803528, "dur": 3159, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748420251630480, "dur": 340204, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251970695, "dur": 2883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251973583, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251973637, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251973778, "dur": 1451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251975266, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D0749362471C98A9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251975522, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251975647, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251975827, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251975981, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_72608D0D80FBEA70.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251976306, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251976423, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251976572, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251976691, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251977041, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251977268, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251977587, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251977659, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251977734, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251977885, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251977982, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251978201, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251978472, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251979534, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251979681, "dur": 2404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748420251982158, "dur": 2086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420251984245, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251984420, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251984759, "dur": 1936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420251986785, "dur": 1522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748420251988340, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251988607, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748420251988682, "dur": 1731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420251990450, "dur": 1373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420251991867, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420251991983, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251992157, "dur": 4885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420251997043, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251997157, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420251997431, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420251997974, "dur": 2172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748420252000147, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252000587, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C5B5631EE4DDD44F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252000698, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252000845, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420252000911, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252000974, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420252001031, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252001127, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420252001825, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252002129, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420252002343, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252002595, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252002813, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252002974, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252003142, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252003275, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252003409, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252003530, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252003671, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252003845, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252004047, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252004200, "dur": 2519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420252006727, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252006861, "dur": 2772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252009685, "dur": 1368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252011112, "dur": 9376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748420252020490, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252020767, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_1DF7BBC55D029A75.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252020830, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252020940, "dur": 5986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252026928, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252027054, "dur": 8303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748420252035359, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252035634, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252035761, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420252035827, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252035902, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420252035998, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748420252036078, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252036220, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252036494, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252036848, "dur": 21825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748420252058675, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252059060, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252061538, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252061642, "dur": 254355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748420252315999, "dur": 2636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252318654, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_FE8D1F6CE20A2409.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252319168, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252319355, "dur": 6085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252325442, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252325720, "dur": 32342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748420252358064, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252358347, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_7BBE081BF0E8AF4A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252358418, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252358606, "dur": 3638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252362246, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252362334, "dur": 17448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748420252379783, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252380290, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_BC6ADFF987BC82A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252380452, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252380553, "dur": 4786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748420252385409, "dur": 1285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748420252386696, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252386906, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420252387316, "dur": 856631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253243965, "dur": 7122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253251089, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253251575, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253251751, "dur": 4786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253256538, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253256807, "dur": 7731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253264540, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253264817, "dur": 7869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253272688, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253272795, "dur": 7395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253280192, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253280440, "dur": 6534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253286975, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253287135, "dur": 10375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253297512, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253297694, "dur": 6655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253304350, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253304524, "dur": 12445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748420253316971, "dur": 754, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748420253317838, "dur": 2489104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251630516, "dur": 340264, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251970801, "dur": 4366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251975284, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251975503, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251975570, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_37E22A5ED73E2B2F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251975797, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251975902, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251976108, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251976268, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251976423, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251976550, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251976707, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251976984, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251977364, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251977546, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251977796, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251977887, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251978055, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251978222, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251979057, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251979549, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251979753, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251979958, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251980178, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251980432, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251980641, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_E711E874CC38399E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251980822, "dur": 3733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748420251984556, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251984663, "dur": 1955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748420251986803, "dur": 1587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748420251988391, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251988603, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748420251988663, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251988827, "dur": 3041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748420251991869, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251991962, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251992144, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251992249, "dur": 4829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748420251997079, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251997465, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420251998143, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420251998225, "dur": 3347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748420252001573, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252002115, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252002490, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252002671, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252002898, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252003054, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252003212, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252003320, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252003451, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252003587, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252003657, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252003786, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252004001, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252004134, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252004275, "dur": 4111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748420252008388, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252008485, "dur": 1955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252010441, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252010549, "dur": 13852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748420252024402, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252024687, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252025160, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252025586, "dur": 4065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252029653, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252029744, "dur": 3643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748420252033395, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252033581, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252033717, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252033784, "dur": 2590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748420252036375, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252036729, "dur": 10692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748420252047423, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252047838, "dur": 19302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748420252067142, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252067509, "dur": 15370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748420252082880, "dur": 817, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252083742, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Path.Editor.ref.dll_91802D938EA41213.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252083863, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252084196, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_3F93312DF16ABE8A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252084331, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252084456, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252084520, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252084637, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_3971D385965D884C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252084747, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252084865, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_D292A1F7EB1601C3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252085031, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252085169, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_8E9298DC6B12ED57.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252085323, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252085445, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_DD9C61FBB7D63C03.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252085548, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252085654, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252085799, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252085983, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252086126, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252086399, "dur": 939, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252087358, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252087561, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252087724, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252088331, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252088745, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252088882, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252089008, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252089129, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252089263, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252089440, "dur": 648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252090105, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252090328, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252090423, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252090537, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252090655, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252090759, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252090862, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252090951, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252091141, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252091256, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252091404, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252091686, "dur": 5090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252096792, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252096894, "dur": 248212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748420252345108, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252345598, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252346193, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252346293, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252346421, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_CBF917C274624EE2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252346503, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252346654, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252346813, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Psdimporter.Editor.ref.dll_47CE2970C8879D28.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252346873, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252346978, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252347067, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252347142, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252347217, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252347365, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Editor.ref.dll_AB52260E0554293F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252347466, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252347544, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Editor.ref.dll_FD249531A54E376F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252347610, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252347700, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252347817, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252347928, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_94629FB7B2A83F68.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252348036, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252348148, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252348283, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252348408, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252348478, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252348610, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252348771, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252348898, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252349092, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252349194, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252349358, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252349518, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748420252349622, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252350167, "dur": 4248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748420252354416, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252354553, "dur": 10455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748420252365009, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252365281, "dur": 2061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252367343, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252369115, "dur": 2222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252371338, "dur": 2582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252373921, "dur": 2602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252376524, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252377417, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252377648, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252377741, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252377813, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252378191, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252378321, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252378431, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252379508, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252379694, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252379836, "dur": 3184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252383021, "dur": 2235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252385390, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252385834, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252386734, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252387281, "dur": 80640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252467995, "dur": 15561, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420252483557, "dur": 760446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253244023, "dur": 5983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748420253250008, "dur": 709, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253250763, "dur": 5748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748420253256512, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253256828, "dur": 9619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748420253266448, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253266791, "dur": 5403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748420253272196, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253272278, "dur": 6275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748420253278555, "dur": 992, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253279558, "dur": 6873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748420253286432, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253286582, "dur": 6226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748420253292809, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253293123, "dur": 13135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748420253306260, "dur": 1139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253307415, "dur": 11818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748420253319235, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748420253319388, "dur": 2487448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251630529, "dur": 340315, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251970848, "dur": 4245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251975127, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A49EDD7A339F9A85.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251975295, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251975560, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251975761, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251975888, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_EBB973DC47E5C128.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251976105, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251976303, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD8CC2F62B9395CF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251976465, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251976589, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251976943, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251977220, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251977547, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251977728, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251977916, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251978121, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251978464, "dur": 949, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251979426, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251979684, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251979823, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420251982421, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748420251984436, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251984831, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251984931, "dur": 1661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420251986737, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420251988250, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420251988399, "dur": 1885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420251990335, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748420251990609, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251990970, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420251992093, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251992278, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251992367, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420251992480, "dur": 1622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420251994163, "dur": 1242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748420251995406, "dur": 1111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251996530, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420251997363, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420251997436, "dur": 29859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748420252027296, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252027657, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252027768, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252027885, "dur": 2846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252030732, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252030810, "dur": 7130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748420252037941, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252038430, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252038518, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252038583, "dur": 3629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252042272, "dur": 27920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748420252070193, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252070390, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_C4A720C3D568CE76.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252070449, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252070527, "dur": 26275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748420252096803, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252097239, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252097302, "dur": 9188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252106492, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252106612, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252106722, "dur": 3355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252110078, "dur": 752, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252110860, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252111192, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252111484, "dur": 4621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252116107, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252116213, "dur": 5032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252121247, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252121380, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252121444, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420252121533, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252121680, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252121831, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252121924, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748420252122036, "dur": 193138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748420252315176, "dur": 791, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252315984, "dur": 16592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748420252332577, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252332945, "dur": 43080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748420252376026, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252376415, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748420252376506, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252376621, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252377297, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252377502, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252377744, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252378043, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252378188, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252378313, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252378529, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252379681, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252380405, "dur": 3318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252383724, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252384674, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252384761, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252384871, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252385228, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252385827, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252385961, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252386391, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252386731, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252386822, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252386913, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252387283, "dur": 96281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420252483566, "dur": 760480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253244048, "dur": 5238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748420253249289, "dur": 1449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253250782, "dur": 8107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748420253258890, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253259180, "dur": 10289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748420253269470, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253269590, "dur": 7806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748420253277490, "dur": 13892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748420253291383, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253291598, "dur": 12582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748420253304182, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253304327, "dur": 7659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748420253311987, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253312129, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253312219, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253312289, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253312361, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253312447, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748420253312530, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253312619, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748420253312716, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253312806, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253312978, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253313118, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748420253313182, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253313297, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253313406, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253313485, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253313598, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748420253313835, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253314042, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748420253314112, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253314475, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748420253314577, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253315005, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253315982, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748420253317143, "dur": 646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420253317801, "dur": 2485411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748420255803262, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748420255803233, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748420255803472, "dur": 3204, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748420255806696, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748420255818700, "dur": 2495, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 70593, "tid": 77, "ts": 1748420255977106, "dur": 13059, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 70593, "tid": 77, "ts": 1748420255990439, "dur": 9424, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 70593, "tid": 77, "ts": 1748420255934308, "dur": 70914, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}