{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 81053, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 81053, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 81053, "tid": 14, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 81053, "tid": 14, "ts": 1748515365080640, "dur": 2832, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 81053, "tid": 14, "ts": 1748515365090329, "dur": 1071, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 81053, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 81053, "tid": 1, "ts": 1748515364559334, "dur": 12336, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 81053, "tid": 1, "ts": 1748515364571674, "dur": 48477, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 81053, "tid": 1, "ts": 1748515364620160, "dur": 79879, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 81053, "tid": 14, "ts": 1748515365091407, "dur": 475, "ph": "X", "name": "", "args": {}}, {"pid": 81053, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364556703, "dur": 17240, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364573946, "dur": 486035, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364575100, "dur": 7274, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364582381, "dur": 1500, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364583884, "dur": 17084, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364600976, "dur": 595, "ph": "X", "name": "ProcessMessages 8118", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601576, "dur": 88, "ph": "X", "name": "ReadAsync 8118", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601687, "dur": 6, "ph": "X", "name": "ProcessMessages 8127", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601694, "dur": 97, "ph": "X", "name": "ReadAsync 8127", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601798, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601802, "dur": 43, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601861, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601889, "dur": 45, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601938, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601941, "dur": 40, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601985, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364601987, "dur": 37, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602027, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602034, "dur": 39, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602076, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602084, "dur": 46, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602134, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602136, "dur": 72, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602212, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602214, "dur": 84, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602301, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602305, "dur": 512, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602820, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602822, "dur": 45, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602870, "dur": 5, "ph": "X", "name": "ProcessMessages 7331", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602877, "dur": 71, "ph": "X", "name": "ReadAsync 7331", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602950, "dur": 1, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602952, "dur": 41, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602996, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364602999, "dur": 72, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603077, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603079, "dur": 35, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603118, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603121, "dur": 41, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603164, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603166, "dur": 35, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603205, "dur": 307, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603516, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603519, "dur": 39, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603561, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603563, "dur": 38, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603605, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603607, "dur": 265, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364603876, "dur": 656, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604545, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604549, "dur": 36, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604588, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604591, "dur": 39, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604635, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604639, "dur": 50, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604711, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604713, "dur": 42, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604758, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604760, "dur": 38, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604811, "dur": 4, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604817, "dur": 36, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604864, "dur": 86, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604953, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364604971, "dur": 40, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364605022, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364605032, "dur": 51, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364605092, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364605094, "dur": 1193, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606295, "dur": 5, "ph": "X", "name": "ProcessMessages 3916", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606302, "dur": 200, "ph": "X", "name": "ReadAsync 3916", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606506, "dur": 2, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606509, "dur": 36, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606549, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606550, "dur": 67, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606623, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606625, "dur": 31, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606661, "dur": 33, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606697, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606699, "dur": 32, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606734, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606736, "dur": 29, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606772, "dur": 39, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606815, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606818, "dur": 38, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364606862, "dur": 500, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364607368, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364607373, "dur": 117, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364607493, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364607499, "dur": 38, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364607548, "dur": 40, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364607595, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364607599, "dur": 354, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364607961, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364607967, "dur": 36, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608008, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608021, "dur": 42, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608064, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608066, "dur": 40, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608115, "dur": 686, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608804, "dur": 2, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608808, "dur": 61, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608876, "dur": 3, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608885, "dur": 94, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608982, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364608986, "dur": 358, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364609351, "dur": 2, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364609355, "dur": 62, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364609423, "dur": 2, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364609427, "dur": 80, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364609510, "dur": 11, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364609524, "dur": 75, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364609603, "dur": 3, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364609609, "dur": 1158, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364610772, "dur": 7, "ph": "X", "name": "ProcessMessages 5313", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364610785, "dur": 47, "ph": "X", "name": "ReadAsync 5313", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364610836, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364610838, "dur": 420, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611263, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611266, "dur": 43, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611313, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611318, "dur": 37, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611359, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611362, "dur": 35, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611401, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611405, "dur": 254, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611663, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611667, "dur": 36, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611708, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611710, "dur": 44, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611759, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611761, "dur": 33, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611798, "dur": 184, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611986, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364611989, "dur": 32, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612023, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612026, "dur": 33, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612062, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612065, "dur": 28, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612098, "dur": 33, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612134, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612139, "dur": 40, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612182, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612190, "dur": 36, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612231, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612233, "dur": 38, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612273, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612279, "dur": 28, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612310, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612312, "dur": 336, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612651, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612657, "dur": 33, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612692, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612695, "dur": 38, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612742, "dur": 35, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612782, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612786, "dur": 67, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612866, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612868, "dur": 34, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612905, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612908, "dur": 33, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612943, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612946, "dur": 29, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612983, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364612985, "dur": 266, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613266, "dur": 2, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613271, "dur": 63, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613355, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613358, "dur": 65, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613426, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613432, "dur": 33, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613468, "dur": 52, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613528, "dur": 34, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613571, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613573, "dur": 40, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613616, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613618, "dur": 35, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613657, "dur": 34, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364613694, "dur": 202, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615126, "dur": 2, "ph": "X", "name": "ProcessMessages 1004", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615130, "dur": 61, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615197, "dur": 4, "ph": "X", "name": "ProcessMessages 5099", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615203, "dur": 34, "ph": "X", "name": "ReadAsync 5099", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615240, "dur": 3, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615245, "dur": 365, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615615, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615618, "dur": 68, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615688, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615691, "dur": 147, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615840, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615843, "dur": 38, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615883, "dur": 1, "ph": "X", "name": "ProcessMessages 1670", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615886, "dur": 31, "ph": "X", "name": "ReadAsync 1670", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615920, "dur": 30, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615958, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364615961, "dur": 306, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616270, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616273, "dur": 35, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616310, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616312, "dur": 32, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616347, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616349, "dur": 36, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616389, "dur": 33, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616425, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616427, "dur": 38, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616473, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616475, "dur": 37, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616515, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616517, "dur": 34, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616555, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616558, "dur": 38, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616599, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616601, "dur": 130, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616739, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616742, "dur": 35, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616779, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616782, "dur": 36, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616821, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616823, "dur": 29, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616854, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616856, "dur": 68, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616927, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616932, "dur": 29, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364616965, "dur": 34, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617005, "dur": 33, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617041, "dur": 3, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617047, "dur": 326, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617378, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617381, "dur": 32, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617416, "dur": 7, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617425, "dur": 34, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617462, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617464, "dur": 30, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617499, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617502, "dur": 29, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617533, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617535, "dur": 39, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617578, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617580, "dur": 34, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617617, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617619, "dur": 32, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617654, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617656, "dur": 33, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617693, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617697, "dur": 32, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617734, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617738, "dur": 34, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617774, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617777, "dur": 32, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617813, "dur": 31, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617846, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617848, "dur": 33, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364617885, "dur": 188, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618076, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618081, "dur": 54, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618139, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618142, "dur": 89, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618234, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618237, "dur": 380, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618621, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618623, "dur": 33, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618659, "dur": 36, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618699, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618701, "dur": 50, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618768, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618769, "dur": 63, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618836, "dur": 70, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618910, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618911, "dur": 32, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618948, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618950, "dur": 31, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364618985, "dur": 30, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619018, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619021, "dur": 302, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619326, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619327, "dur": 35, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619366, "dur": 29, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619398, "dur": 28, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619429, "dur": 33, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619466, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619468, "dur": 161, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619632, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619634, "dur": 34, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619670, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619672, "dur": 31, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619706, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619708, "dur": 31, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619743, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619745, "dur": 33, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619780, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619783, "dur": 37, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619824, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619826, "dur": 163, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619992, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364619995, "dur": 33, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620032, "dur": 28, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620063, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620099, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620101, "dur": 32, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620138, "dur": 402, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620545, "dur": 32, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620580, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620582, "dur": 36, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620622, "dur": 61, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620684, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620686, "dur": 213, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620903, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620905, "dur": 32, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620940, "dur": 3, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620944, "dur": 31, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620979, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364620981, "dur": 36, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621020, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621021, "dur": 30, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621055, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621113, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621115, "dur": 41, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621159, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621161, "dur": 40, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621205, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621207, "dur": 53, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621264, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621267, "dur": 278, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621548, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621550, "dur": 30, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621585, "dur": 29, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621624, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621626, "dur": 38, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621667, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621669, "dur": 228, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621901, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621903, "dur": 42, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621949, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621951, "dur": 38, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621993, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364621994, "dur": 30, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622028, "dur": 27, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622125, "dur": 29, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622158, "dur": 29, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622190, "dur": 35, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622229, "dur": 33, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622266, "dur": 28, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622298, "dur": 74, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622375, "dur": 5, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622381, "dur": 32, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622417, "dur": 32, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622454, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622456, "dur": 36, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622495, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622497, "dur": 152, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622652, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622654, "dur": 32, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622689, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622690, "dur": 33, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622726, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622728, "dur": 38, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622769, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622770, "dur": 38, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622814, "dur": 156, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364622974, "dur": 34, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623012, "dur": 31, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623046, "dur": 29, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623077, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623078, "dur": 37, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623119, "dur": 65, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623188, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623191, "dur": 33, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623228, "dur": 28, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623259, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623261, "dur": 32, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623297, "dur": 186, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623485, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623487, "dur": 30, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623526, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623528, "dur": 29, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623560, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623561, "dur": 34, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623605, "dur": 3, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623609, "dur": 29, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364623641, "dur": 405, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624050, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624052, "dur": 28, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624083, "dur": 29, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624116, "dur": 28, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624147, "dur": 36, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624187, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624189, "dur": 129, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624320, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624321, "dur": 32, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624357, "dur": 31, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624392, "dur": 27, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624422, "dur": 7, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624433, "dur": 50, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624489, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624494, "dur": 51, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624549, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624551, "dur": 159, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624714, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624718, "dur": 58, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624780, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624783, "dur": 40, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624828, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364624830, "dur": 169, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625003, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625006, "dur": 47, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625057, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625065, "dur": 51, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625120, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625122, "dur": 98, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625223, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625228, "dur": 462, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625696, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625699, "dur": 45, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625747, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625749, "dur": 46, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625798, "dur": 3, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625802, "dur": 55, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625861, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625863, "dur": 62, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625929, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625932, "dur": 41, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364625978, "dur": 36, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626017, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626020, "dur": 38, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626063, "dur": 34, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626100, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626103, "dur": 54, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626161, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626163, "dur": 43, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626209, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626211, "dur": 38, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626252, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626254, "dur": 149, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626407, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626409, "dur": 38, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626450, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626453, "dur": 33, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626492, "dur": 43, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626538, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364626540, "dur": 1095, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364627640, "dur": 6, "ph": "X", "name": "ProcessMessages 5929", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364627648, "dur": 531, "ph": "X", "name": "ReadAsync 5929", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364628192, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364628195, "dur": 304, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364628504, "dur": 593, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364629100, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364629102, "dur": 324, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364629430, "dur": 640, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364630074, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364630076, "dur": 267, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364630348, "dur": 719, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364631070, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364631072, "dur": 287, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364631366, "dur": 927, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364632296, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364632298, "dur": 58, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364632362, "dur": 205, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364632571, "dur": 3, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364632575, "dur": 882, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364633465, "dur": 25, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364633493, "dur": 228, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364633725, "dur": 2566, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364636298, "dur": 2, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364636302, "dur": 970, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364637277, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364637280, "dur": 152, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364637436, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364637439, "dur": 992, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364638435, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364638438, "dur": 297, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364638740, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364638742, "dur": 992, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364639738, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364639740, "dur": 364, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364640109, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364640111, "dur": 791, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364640905, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364640907, "dur": 347, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364641262, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364641264, "dur": 1634, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364642903, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364642906, "dur": 249, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364643160, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364643162, "dur": 322, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364643488, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364643490, "dur": 697, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364644190, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364644192, "dur": 110, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364644309, "dur": 488, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364644800, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364644802, "dur": 290, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364645095, "dur": 3, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364645099, "dur": 707, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364645818, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364645820, "dur": 112, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364645936, "dur": 729, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364646668, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364646672, "dur": 29, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364646704, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364646708, "dur": 244, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364646958, "dur": 971, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364647931, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364647932, "dur": 240, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364648176, "dur": 1104, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364649285, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364649288, "dur": 360, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364649653, "dur": 1382, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364651039, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364651041, "dur": 328, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364651374, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364651376, "dur": 896, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364652279, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364652282, "dur": 281, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364652573, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364652576, "dur": 485, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364653065, "dur": 6, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364653075, "dur": 285, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364653363, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364653367, "dur": 981, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364654360, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364654363, "dur": 312, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364654679, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364654682, "dur": 869, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364655554, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364655557, "dur": 105, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364655666, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364655669, "dur": 4119, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364659795, "dur": 4, "ph": "X", "name": "ProcessMessages 3689", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364659801, "dur": 1266, "ph": "X", "name": "ReadAsync 3689", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364661072, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364661075, "dur": 243, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364661322, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364661324, "dur": 1156, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364662485, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364662488, "dur": 134, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364662625, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364662634, "dur": 1271, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364663909, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364663912, "dur": 1072, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364664995, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364664998, "dur": 808, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364665815, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364665818, "dur": 133, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364665955, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364665958, "dur": 1526, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364667499, "dur": 5, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364667506, "dur": 320, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364667834, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364667836, "dur": 1587, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364669428, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364669432, "dur": 261, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364669696, "dur": 4, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364669701, "dur": 1568, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364671282, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364671286, "dur": 1287, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364672579, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364672581, "dur": 1137, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364673723, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364673726, "dur": 318, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364674047, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364674049, "dur": 2258, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364676318, "dur": 2, "ph": "X", "name": "ProcessMessages 1658", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364676323, "dur": 1987, "ph": "X", "name": "ReadAsync 1658", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364678320, "dur": 7, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364678337, "dur": 96, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364678446, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364678448, "dur": 2031, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364680488, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364680491, "dur": 205, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364680700, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364680701, "dur": 1758, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364682466, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364682469, "dur": 640, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364683112, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364683115, "dur": 34, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364683153, "dur": 33, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364683192, "dur": 130, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364683326, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364683328, "dur": 1298, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364684637, "dur": 5, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364684648, "dur": 73, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364684724, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364684727, "dur": 3403, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364688136, "dur": 3, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364688141, "dur": 791, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364688948, "dur": 5, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364688956, "dur": 76, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364689044, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364689048, "dur": 1659, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364690713, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364690715, "dur": 319, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364691038, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364691039, "dur": 834, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364691877, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364691879, "dur": 279, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364692162, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364692164, "dur": 548, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364692718, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364692720, "dur": 258, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364692983, "dur": 465, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364693451, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364693453, "dur": 254, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364693712, "dur": 661, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364694376, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364694378, "dur": 143, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364694525, "dur": 661, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364695189, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364695191, "dur": 303, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364695498, "dur": 488, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364695990, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364695992, "dur": 255, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364696251, "dur": 680, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364696934, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364696936, "dur": 127, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364697067, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364697069, "dur": 35, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364697108, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364697110, "dur": 34, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364697157, "dur": 57, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364697227, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364697230, "dur": 1414, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364698648, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364698650, "dur": 335, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364698989, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364698991, "dur": 688, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364699683, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364699684, "dur": 282, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364699971, "dur": 1010, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364700986, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364700989, "dur": 254, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364701245, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364701247, "dur": 953, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364702205, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364702208, "dur": 282, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364702495, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364702497, "dur": 721, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703221, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703223, "dur": 264, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703491, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703494, "dur": 37, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703536, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703538, "dur": 38, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703580, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703582, "dur": 31, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703622, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364703624, "dur": 1017, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364704646, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364704649, "dur": 640, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364705294, "dur": 552, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364705852, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364705977, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364705997, "dur": 271, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706272, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706274, "dur": 90, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706369, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706371, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706556, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706558, "dur": 245, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706808, "dur": 12, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706827, "dur": 133, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706964, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364706967, "dur": 89, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707064, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707081, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707206, "dur": 8, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707220, "dur": 67, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707291, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707294, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707355, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707368, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707453, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707455, "dur": 102, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707561, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707564, "dur": 172, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707741, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707743, "dur": 54, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707804, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364707808, "dur": 199, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708012, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708015, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708073, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708076, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708229, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708232, "dur": 73, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708309, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708312, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708378, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708380, "dur": 73, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708457, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708459, "dur": 116, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708580, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708583, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708637, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708639, "dur": 56, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708700, "dur": 118, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708822, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708825, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708902, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708905, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708962, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364708964, "dur": 174, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709144, "dur": 232, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709380, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709383, "dur": 110, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709497, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709500, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709565, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709568, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709628, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709630, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709848, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709850, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709950, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364709953, "dur": 113, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710071, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710073, "dur": 188, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710265, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710268, "dur": 207, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710479, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710482, "dur": 67, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710555, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710557, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710715, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710717, "dur": 159, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710880, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364710883, "dur": 232, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711124, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711127, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711183, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711185, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711293, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711295, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711424, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711427, "dur": 85, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711516, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711519, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711699, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711702, "dur": 83, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711789, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711792, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711916, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364711919, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364712009, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364712011, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364712074, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364712076, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364712279, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364713392, "dur": 115, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364713516, "dur": 5, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364713522, "dur": 106, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364713632, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364713635, "dur": 395, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714035, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714039, "dur": 55, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714099, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714102, "dur": 100, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714250, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714253, "dur": 69, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714326, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714329, "dur": 118, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714459, "dur": 15, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714476, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714553, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714745, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714747, "dur": 236, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714993, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364714996, "dur": 139, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715140, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715142, "dur": 389, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715538, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715541, "dur": 114, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715660, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715666, "dur": 142, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715821, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715824, "dur": 137, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715974, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364715982, "dur": 99, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716085, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716089, "dur": 124, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716218, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716220, "dur": 90, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716330, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716334, "dur": 131, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716472, "dur": 10, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716487, "dur": 89, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716589, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716597, "dur": 102, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716704, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716706, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716790, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716793, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716881, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364716887, "dur": 461, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364717353, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364717356, "dur": 136, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364717496, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364717498, "dur": 243, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364717745, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364717753, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364717964, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364717967, "dur": 192, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364718162, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364718164, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364718302, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364718304, "dur": 429, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364718737, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364718740, "dur": 226, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364718970, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364718972, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719081, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719084, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719360, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719363, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719437, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719442, "dur": 78, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719524, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719526, "dur": 62, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719592, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719594, "dur": 94, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719693, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719695, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364719773, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364720009, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364720011, "dur": 14712, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364734731, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364734737, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364734846, "dur": 880, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364735731, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364735733, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364735869, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364735871, "dur": 1448, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364737325, "dur": 8549, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364745882, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364745886, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364745973, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364745975, "dur": 1025, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364747005, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364747008, "dur": 620, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364747633, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364747635, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364747690, "dur": 687, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364748382, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364748385, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364748504, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364748506, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364748625, "dur": 498, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364749127, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364749129, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364749189, "dur": 835, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750027, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750030, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750132, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750134, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750308, "dur": 293, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750606, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750609, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750760, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750891, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364750944, "dur": 214, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364751162, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364751164, "dur": 182, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364751351, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364751515, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364751520, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364751576, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364751578, "dur": 472, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364752057, "dur": 1086, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364753147, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364753149, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364753280, "dur": 821, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364754104, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364754106, "dur": 365, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364754476, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364754478, "dur": 1110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364755593, "dur": 758, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364756356, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364756358, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364756473, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364756475, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364756659, "dur": 493, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364757158, "dur": 1334, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364758496, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364758498, "dur": 232, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364758735, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364758812, "dur": 201, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364759017, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364759019, "dur": 309, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364759334, "dur": 1213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364760551, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364760553, "dur": 512, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364761070, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364761072, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364761140, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364761308, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364761311, "dur": 306, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364761621, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364761623, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364761821, "dur": 340, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364762165, "dur": 304, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364762473, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364762475, "dur": 567, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763048, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763211, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763321, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763323, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763424, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763426, "dur": 294, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763728, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763731, "dur": 57, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763791, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763801, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763877, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364763879, "dur": 231, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364764115, "dur": 198, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364764316, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364764318, "dur": 222, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364764544, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364764547, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364764708, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364764710, "dur": 302, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364765019, "dur": 267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364765289, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364765291, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364765843, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364765845, "dur": 265, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364766115, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364766117, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364766306, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364766489, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364766606, "dur": 259, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364766869, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364766871, "dur": 1744, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364768620, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364768623, "dur": 930, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364769558, "dur": 707, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364770270, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364770374, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364770377, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364770600, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364770602, "dur": 1370, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364771979, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364771982, "dur": 445, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364772431, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364772433, "dur": 341, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364772785, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364772794, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364772946, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364772949, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364773008, "dur": 327, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364773339, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364773341, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364773487, "dur": 314, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364773807, "dur": 822, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364774634, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364774637, "dur": 2785, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364777430, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364777433, "dur": 1145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364778588, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364778592, "dur": 629, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364779226, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364779233, "dur": 916, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780155, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780158, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780334, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780336, "dur": 86, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780426, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780428, "dur": 186, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780619, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780621, "dur": 312, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780939, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364780942, "dur": 218, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781166, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781168, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781333, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781335, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781462, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781464, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781544, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781546, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781711, "dur": 154, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781869, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364781871, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364782001, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364782003, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364782083, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364782254, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364782256, "dur": 221, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364782481, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364782484, "dur": 470, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364782959, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364782961, "dur": 270, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364783237, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364783240, "dur": 1060, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364784304, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364784306, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364784446, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364784449, "dur": 670, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364785133, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364785139, "dur": 1261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364786404, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364786406, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364786556, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364786559, "dur": 2051, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364788617, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364788621, "dur": 3036, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364791664, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364791668, "dur": 666, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364792340, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364792343, "dur": 216, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364792563, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364792565, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364792698, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364792703, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364792763, "dur": 491, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364793258, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364793261, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364793422, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364793516, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364793519, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364793656, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364793658, "dur": 711, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364794377, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364794380, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364794437, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364794440, "dur": 136, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364794581, "dur": 428, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364795012, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364795197, "dur": 494, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515364795695, "dur": 223936, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365019642, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365019646, "dur": 70, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365019722, "dur": 58, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365019785, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365019787, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365019856, "dur": 4, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365019862, "dur": 51, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365019935, "dur": 48, "ph": "X", "name": "ReadAsync 4142", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365019989, "dur": 54, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365020047, "dur": 1571, "ph": "X", "name": "ProcessMessages 4838", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365021623, "dur": 3706, "ph": "X", "name": "ReadAsync 4838", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365025335, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365025339, "dur": 554, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365025898, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365025902, "dur": 2908, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365028816, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365028819, "dur": 188, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365029013, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365029015, "dur": 384, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365029405, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365029407, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365029619, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365029622, "dur": 868, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365030496, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365030499, "dur": 2407, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365032913, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365032917, "dur": 394, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365033317, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365033320, "dur": 341, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365033678, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365033681, "dur": 1748, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365035435, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365035438, "dur": 686, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365036128, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365036130, "dur": 470, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365036605, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365036608, "dur": 647, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365037261, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365037263, "dur": 948, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365038216, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365038219, "dur": 2233, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365040459, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365040462, "dur": 256, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365040722, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365040725, "dur": 281, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365041011, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365041013, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365041118, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365041121, "dur": 2548, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365043674, "dur": 20, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365043697, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365043779, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365043781, "dur": 364, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365044149, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365044151, "dur": 542, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365044696, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365044698, "dur": 290, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365044993, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365044995, "dur": 2122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047122, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047124, "dur": 439, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047568, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047571, "dur": 153, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047728, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047731, "dur": 154, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047890, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047892, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047967, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365047970, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048024, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048026, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048104, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048222, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048428, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048433, "dur": 73, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048510, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048512, "dur": 115, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048632, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048634, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048728, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048730, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048799, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048802, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048876, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365048878, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049006, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049008, "dur": 83, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049110, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049113, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049207, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049307, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049310, "dur": 129, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049443, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049446, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049525, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049528, "dur": 48, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049580, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049582, "dur": 139, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049726, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049728, "dur": 101, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049832, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049835, "dur": 103, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049941, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365049944, "dur": 76, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050025, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050027, "dur": 58, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050089, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050092, "dur": 70, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050164, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050166, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050264, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050268, "dur": 76, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050348, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050351, "dur": 86, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050441, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050443, "dur": 151, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050598, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050665, "dur": 139, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050807, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050809, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050882, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365050884, "dur": 644, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365051531, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365051534, "dur": 1049, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365052587, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365052591, "dur": 595, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365053190, "dur": 267, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 81053, "tid": 12884901888, "ts": 1748515365053460, "dur": 6446, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 81053, "tid": 14, "ts": 1748515365091885, "dur": 3590, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 81053, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 81053, "tid": 8589934592, "ts": 1748515364552769, "dur": 147289, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 81053, "tid": 8589934592, "ts": 1748515364700062, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 81053, "tid": 8589934592, "ts": 1748515364700070, "dur": 3001, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 81053, "tid": 14, "ts": 1748515365095478, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 81053, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 81053, "tid": 4294967296, "ts": 1748515364408098, "dur": 653932, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 81053, "tid": 4294967296, "ts": 1748515364515351, "dur": 27936, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 81053, "tid": 4294967296, "ts": 1748515365062312, "dur": 8762, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 81053, "tid": 4294967296, "ts": 1748515365065885, "dur": 3484, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 81053, "tid": 4294967296, "ts": 1748515365071153, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 81053, "tid": 14, "ts": 1748515365095498, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748515364558470, "dur": 5259, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748515364563774, "dur": 35421, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748515364599309, "dur": 132, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748515364599442, "dur": 183, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748515364600520, "dur": 504, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748515364601573, "dur": 161, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748515364602210, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748515364602954, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748515364608920, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748515364613358, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748515364642475, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748515364678382, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748515364697008, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748515364698915, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748515364599634, "dur": 105005, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748515364704652, "dur": 347978, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748515365052840, "dur": 177, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748515365053073, "dur": 1148, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748515364599560, "dur": 105102, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364704666, "dur": 13367, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364718034, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748515364718804, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748515364719491, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748515364719900, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364719988, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364721708, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364723656, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364724642, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364725674, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364726625, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364727761, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364729024, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364730436, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364731787, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364733161, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364734872, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/UniversalAnalytics.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748515364734291, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364736174, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364737390, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364738752, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364740015, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364741313, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364742529, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364743799, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364745168, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364746505, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364747004, "dur": 4158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364751163, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364751473, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_A298F0F0964CDA5C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364751571, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364752011, "dur": 3268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364755280, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364755537, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364756290, "dur": 1908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364758199, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364758698, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364758790, "dur": 4165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364762956, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364763180, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364763638, "dur": 2320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364765959, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364766242, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364766829, "dur": 4136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364770966, "dur": 1401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364772420, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364772690, "dur": 1792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364774483, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364774644, "dur": 2546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364777191, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364777361, "dur": 2577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364779940, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364780083, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364780167, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364780279, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_258B0455F8F51CDE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364780544, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364780649, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364780771, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364780833, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364780994, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364781055, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364781138, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364781258, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364781335, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364781545, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364781648, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364781747, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364781933, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364782039, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748515364782099, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364782295, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364782516, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364782603, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364782682, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364782790, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364782947, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364783109, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364783178, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364783329, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364784720, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364785913, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364787329, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364788568, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364788673, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364789875, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364790993, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364792322, "dur": 939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515364793263, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748515364793412, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364794144, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748515364794206, "dur": 227572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515365021782, "dur": 3039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748515365024822, "dur": 642, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515365025486, "dur": 3126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748515365028613, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515365028981, "dur": 3752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748515365032735, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515365032886, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748515365036488, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515365036566, "dur": 3530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748515365040097, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515365040170, "dur": 3492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748515365043668, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515365043793, "dur": 3345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748515365047139, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748515365047225, "dur": 5206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748515365052432, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364599561, "dur": 105109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364704713, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748515364704928, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364705406, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364705646, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A49EDD7A339F9A85.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364706113, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364706228, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364706714, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364707020, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364707123, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364707320, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364707414, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364707585, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364707642, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364707933, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364708005, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364708160, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364708244, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748515364708590, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364708715, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_0118987859331F44.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364708966, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748515364709313, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364709401, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364709472, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364709897, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748515364710092, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364710151, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364710444, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364710707, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364711634, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364711828, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364712750, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748515364712926, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364713291, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364713635, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364714266, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364714727, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364715604, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364715699, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364715866, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364716018, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364716167, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364716286, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364716384, "dur": 1562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364717982, "dur": 1308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364719338, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748515364719464, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364719611, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364719689, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364721055, "dur": 2076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364723131, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364724314, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364725244, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364726255, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364727452, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364728622, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364730067, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364731746, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364733189, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364734826, "dur": 666, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Editor/AssemblyInfo.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748515364734271, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364736091, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364737274, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364738517, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364739847, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364741066, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364742326, "dur": 1477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364743807, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364745331, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364746817, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364747176, "dur": 1550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364748727, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364749096, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364749182, "dur": 1058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364750265, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364750943, "dur": 5399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364756343, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364756625, "dur": 1608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364758233, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364758424, "dur": 1838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364760263, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364760517, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364762832, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364763247, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364763643, "dur": 2017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364765661, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364765807, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364766465, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364767151, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364767715, "dur": 2537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364770253, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515364770525, "dur": 1352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364771927, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364772681, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364773270, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364773738, "dur": 18769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364792567, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364792754, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364793526, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364793671, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364794461, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364794563, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364795007, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748515364795179, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748515364795625, "dur": 226161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365021794, "dur": 3812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748515365025635, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748515365025941, "dur": 3357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748515365029299, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365029562, "dur": 3726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748515365033289, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365033386, "dur": 3314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748515365036701, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365036760, "dur": 4193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748515365040954, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365041076, "dur": 3443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748515365044520, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365044597, "dur": 4094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748515365048692, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365048848, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365048985, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365049161, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365049291, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365049436, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365049660, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365049850, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365049902, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748515365050063, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365050302, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365050396, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365050652, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748515365050723, "dur": 1809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364599573, "dur": 105136, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364704714, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748515364704951, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364705295, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364705466, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364705528, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364705631, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364706101, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364706246, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_CAA42EBCF9A9A6C9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364706430, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364706633, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364706846, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364707029, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364707143, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364707297, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364707420, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364707590, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364707988, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364708417, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364708972, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748515364709388, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364709931, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_FB7A597403E5FDBB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364710124, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364710239, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364710876, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364711177, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364711415, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748515364711472, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364711537, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364712281, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364712669, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364712786, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748515364712973, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364713237, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364713487, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364714049, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748515364714464, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364714861, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364715516, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364716228, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364716372, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364716484, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364716887, "dur": 1018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364717930, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364718599, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364719250, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364719309, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748515364719674, "dur": 1799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364721474, "dur": 1911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364723385, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364724491, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364725500, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364726460, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364727564, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364728928, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364730347, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364731654, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364733334, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364734864, "dur": 667, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ShaderGUI/Shaders/LitShader.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748515364734450, "dur": 1919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364736370, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364737638, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364738969, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364740205, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364741513, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364742719, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364743851, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364745366, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364746894, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364747183, "dur": 1118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748515364748301, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364748591, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364750103, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364750566, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364751129, "dur": 5083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748515364756213, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364756437, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364757106, "dur": 1481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748515364758587, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364758780, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364759279, "dur": 2838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748515364762118, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364762440, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364763018, "dur": 15230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748515364778249, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364778596, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364779029, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364779159, "dur": 12279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748515364791439, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364791694, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364792089, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748515364793028, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364793258, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748515364793396, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748515364793912, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515364794027, "dur": 227724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515365021765, "dur": 4630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748515365026396, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515365026501, "dur": 3839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748515365030341, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515365030458, "dur": 4230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748515365034689, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515365034813, "dur": 3406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748515365038225, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515365038359, "dur": 3833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748515365042193, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515365042282, "dur": 3303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748515365045586, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515365045719, "dur": 5588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748515365051308, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515365051411, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748515365051487, "dur": 1109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364599583, "dur": 105134, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364704724, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364705288, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364705576, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1996541149DA13E0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364705846, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_888C5CF0B798DF8B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364706194, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364706325, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364706610, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364706777, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364706891, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B92AE864B27146B1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364706948, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364707107, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364707271, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364707347, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364707548, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364707658, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364707776, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364707959, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364708039, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364708208, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364708311, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364708599, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364708924, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364709407, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364709586, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364709780, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748515364709998, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364710135, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364710259, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364710840, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748515364711036, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748515364711224, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364711436, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364711499, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364712043, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364712916, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364713103, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364713289, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364713497, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364714185, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364714255, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364714853, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364715410, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364715464, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364715604, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364715730, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364715901, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364716027, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364716205, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364716328, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364716463, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364716574, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364717443, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364718635, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364719289, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748515364719733, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364721372, "dur": 1915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364723287, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364724436, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364725418, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364726411, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364727529, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364728809, "dur": 1490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364730300, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364731651, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364732879, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364734843, "dur": 668, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Editor/SkinningModule/Selection/SerializableSelection.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748515364734002, "dur": 1718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364735721, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364736895, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364738231, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364739553, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364740832, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364742162, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364743688, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364745422, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364746498, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364746935, "dur": 13796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748515364760732, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364761064, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364761126, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364761785, "dur": 1893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748515364763678, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364763824, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364764685, "dur": 19462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748515364784148, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364784248, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_CBF917C274624EE2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364784299, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364784371, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748515364785050, "dur": 3346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748515364788397, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364788534, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364788658, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364788986, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364790118, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364791323, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364791395, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364791505, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364791642, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515364792325, "dur": 229439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365021768, "dur": 4109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748515365025878, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365026008, "dur": 3304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748515365029318, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365029455, "dur": 3910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748515365033366, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365033435, "dur": 3758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748515365037194, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365037359, "dur": 3307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748515365040667, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365040784, "dur": 4059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748515365044844, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365044930, "dur": 4910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748515365049841, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365050076, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365050214, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365050426, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748515365050626, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748515365050852, "dur": 1753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364599595, "dur": 105134, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364704735, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364705227, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364705538, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364705803, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364705910, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D0749362471C98A9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364706767, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364707077, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364707235, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364707376, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364707489, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364707566, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364707796, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364707920, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364708125, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364708221, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364708468, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364708554, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364708650, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748515364709299, "dur": 4214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748515364713514, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364713931, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748515364714359, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748515364714627, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748515364714928, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748515364715678, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364715840, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364716003, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364716082, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364716253, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364716346, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364716465, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748515364716521, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748515364717215, "dur": 1809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748515364719039, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748515364719457, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364719561, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748515364719900, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364719977, "dur": 1742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364721720, "dur": 1999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364723720, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364724709, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364725729, "dur": 2689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364728419, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364729873, "dur": 2725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364732599, "dur": 1616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364734803, "dur": 681, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Editor/SkinningModule/MathUtility.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748515364734216, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364735966, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364737222, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364738426, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364739782, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364741030, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364742337, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364743662, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364745140, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364746448, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364746974, "dur": 5640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748515364752615, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364753243, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364754439, "dur": 6708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748515364761148, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364761639, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748515364762137, "dur": 1863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748515364764000, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364764641, "dur": 3107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748515364767829, "dur": 1031, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365019503, "dur": 545, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515364769586, "dur": 250541, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748515365021755, "dur": 3113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748515365024869, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365025236, "dur": 3405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748515365028642, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365028770, "dur": 3718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748515365032489, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365032615, "dur": 3686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748515365036302, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365036392, "dur": 4043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748515365040436, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365040549, "dur": 3441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748515365043991, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365044098, "dur": 3367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748515365047466, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365047582, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365047806, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365047865, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365048092, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365048167, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365048312, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365048375, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365048573, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748515365049056, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365049461, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365049553, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365049660, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748515365049750, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365050067, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365050377, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365050462, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748515365050654, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748515365050803, "dur": 1799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364599607, "dur": 105132, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364704745, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364705227, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364705528, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364705873, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364706145, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364706639, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_37E22A5ED73E2B2F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364706919, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364707039, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364707310, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364707416, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364707489, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364707553, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364707763, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364707911, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364707998, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364708178, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364708320, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364708549, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364708615, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364708902, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364709048, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748515364709306, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748515364709578, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364709640, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748515364709912, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748515364710131, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364710191, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748515364710508, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748515364710871, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748515364711157, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364711444, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364711649, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364712061, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748515364712374, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364712797, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748515364712956, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364713266, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364713476, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364713997, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748515364714246, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364714746, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364714954, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364715479, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364715607, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364715754, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364715923, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364716059, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364716223, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364716320, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364716480, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364716667, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364716865, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364717873, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364718238, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364718918, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748515364719642, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Weapons/WeaponData.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748515364720772, "dur": 1623, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Weapons/ProjectileController.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748515364722549, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Utils/ObjectPool.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748515364719642, "dur": 6445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364726088, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364727971, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364729341, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364731205, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364732443, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364734838, "dur": 664, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Editor/SkinningModule/SkinningCache/BoneCacheExtensions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748515364733912, "dur": 1694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364735636, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364735729, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364735835, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364737012, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364738242, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364739704, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364740960, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364742250, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364743644, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364745090, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364746476, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364746958, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364747024, "dur": 24809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748515364771834, "dur": 1069, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364772914, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_4EC1FE8B3807FE28.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364772995, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364773360, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364773433, "dur": 18006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748515364791440, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364791675, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748515364792063, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748515364792607, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515364792686, "dur": 229123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515365021811, "dur": 3145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748515365024957, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515365025495, "dur": 4988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748515365030484, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515365031068, "dur": 4895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748515365035964, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515365036069, "dur": 3631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748515365039701, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515365039840, "dur": 3803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748515365043645, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515365043754, "dur": 3345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748515365047100, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515365047159, "dur": 5273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748515365052438, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748515365052545, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364599619, "dur": 105129, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364704753, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364705227, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364705567, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_25D10CC8709418E4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364705832, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_97FEC325F9337F2C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364706140, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364706518, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364706596, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364706861, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364706930, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364707094, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364707200, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364707282, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364707759, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364707970, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364708114, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364708182, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364708334, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364708545, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364708607, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364708691, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_F0DEE4EE0970DD2B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364708849, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364709424, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364709613, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364710000, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364710092, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364710166, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364710460, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364710691, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364711304, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748515364711487, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364711550, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364711757, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364711956, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748515364712244, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364712636, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364712953, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364713192, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364713258, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364713558, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364714258, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364714532, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364714948, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364715077, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748515364715524, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364715601, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364715675, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364715822, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364715966, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364716075, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364716215, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364716306, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364716443, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364717165, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364718128, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364719177, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748515364719284, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364719387, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364721275, "dur": 1703, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/DataManager.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748515364722979, "dur": 581, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/AudioManager.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748515364719661, "dur": 5768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364725430, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364726415, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364727542, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364728961, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364730408, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364731667, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364733105, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364734852, "dur": 671, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Editor/SkinningModule/CopyTool.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748515364734256, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364736002, "dur": 1603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364737606, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364738902, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364740248, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364741523, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364743096, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364743989, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364745275, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364746748, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364747061, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748515364747941, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364748356, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C5B5631EE4DDD44F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364748477, "dur": 1443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364749920, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364749977, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364750855, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364751313, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748515364753699, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364754017, "dur": 1570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364755614, "dur": 2466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748515364758081, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364758482, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364758959, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748515364761154, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364761272, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364761803, "dur": 2058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748515364763862, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364764036, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364764480, "dur": 16982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748515364781463, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364781962, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_C4A720C3D568CE76.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364782045, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364782186, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364782892, "dur": 3203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748515364786096, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364786320, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Editor.ref.dll_3C7C2928F291E90C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364786403, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364786470, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364787610, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364788261, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364788399, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364788552, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364788809, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364789992, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364791051, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364791447, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364791506, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364792324, "dur": 1204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515364793530, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748515364793636, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748515364794355, "dur": 227442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365021803, "dur": 3154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748515365024958, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365025439, "dur": 3325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748515365028765, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365028875, "dur": 3915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748515365032791, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365033038, "dur": 3391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748515365036430, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365036608, "dur": 4372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748515365040981, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365041144, "dur": 3233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748515365044378, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365044505, "dur": 4786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748515365049292, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365049687, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748515365049784, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365050009, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365050069, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365050305, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365050391, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365050536, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748515365050658, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365050770, "dur": 1764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748515365052536, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364599630, "dur": 105132, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364704764, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364705225, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364705518, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364705621, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364706101, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364706220, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364706731, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_72608D0D80FBEA70.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364706966, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364707110, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364707214, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364707368, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364707534, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364707672, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364707802, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364708019, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364708152, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364708220, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364708313, "dur": 838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748515364709167, "dur": 7473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364716640, "dur": 723, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364717406, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364717599, "dur": 16605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364734205, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364734671, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364734747, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364734831, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364737286, "dur": 7863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364745150, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364745778, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364745849, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364745975, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364746920, "dur": 3035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364749956, "dur": 675, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364750724, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364751189, "dur": 1725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364752915, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364753094, "dur": 10185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364763280, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364763767, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364763841, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364764181, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364764453, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364764528, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364764859, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364765207, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364766011, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364766069, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364766515, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364766579, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748515364767165, "dur": 2866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364770032, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364770339, "dur": 1531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364771915, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364772564, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364772966, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364774471, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364774551, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364777190, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364777375, "dur": 5359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748515364782735, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364783238, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364783934, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364785109, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364786365, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364787572, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364788669, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364789847, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364790966, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515364792345, "dur": 229424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365021775, "dur": 2855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748515365024632, "dur": 842, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365025485, "dur": 4108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748515365029594, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365029745, "dur": 3676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748515365033422, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365033803, "dur": 3370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748515365037174, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365037315, "dur": 3244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748515365040560, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365040659, "dur": 3313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748515365043973, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365044070, "dur": 3274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748515365047344, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365047466, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365047559, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365047707, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748515365047781, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365048000, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365048167, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365048513, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365048639, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365049063, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365049171, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365049429, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365049505, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365049606, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365049753, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365049833, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365049975, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365050094, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748515365050149, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365050238, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365050306, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365050397, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365050495, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748515365050713, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748515365051504, "dur": 1114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748515365058172, "dur": 875, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 81053, "tid": 14, "ts": 1748515365096181, "dur": 2664, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 81053, "tid": 14, "ts": 1748515365099032, "dur": 2699, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 81053, "tid": 14, "ts": 1748515365087690, "dur": 15218, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}