{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 70593, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 70593, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 70593, "tid": 14, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 70593, "tid": 14, "ts": 1748419588008308, "dur": 3189, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 70593, "tid": 14, "ts": 1748419588017846, "dur": 1221, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 70593, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 70593, "tid": 1, "ts": 1748419587212782, "dur": 12987, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748419587225773, "dur": 64360, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748419587290144, "dur": 232618, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 70593, "tid": 14, "ts": 1748419588019085, "dur": 596, "ph": "X", "name": "", "args": {}}, {"pid": 70593, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587208517, "dur": 24134, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587232655, "dur": 749959, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587234261, "dur": 10432, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587244702, "dur": 2150, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587246857, "dur": 17796, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587264693, "dur": 1019, "ph": "X", "name": "ProcessMessages 6946", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587265720, "dur": 94, "ph": "X", "name": "ReadAsync 6946", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587265819, "dur": 27, "ph": "X", "name": "ProcessMessages 8136", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587265852, "dur": 296, "ph": "X", "name": "ReadAsync 8136", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266153, "dur": 17, "ph": "X", "name": "ProcessMessages 2442", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266174, "dur": 86, "ph": "X", "name": "ReadAsync 2442", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266264, "dur": 2, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266274, "dur": 59, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266338, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266341, "dur": 79, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266424, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266426, "dur": 284, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266714, "dur": 5, "ph": "X", "name": "ProcessMessages 1850", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266721, "dur": 133, "ph": "X", "name": "ReadAsync 1850", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266862, "dur": 2, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266865, "dur": 75, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266944, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587266947, "dur": 184, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587267135, "dur": 1, "ph": "X", "name": "ProcessMessages 1026", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587267138, "dur": 63, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587267205, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587267208, "dur": 80, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587267292, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587268456, "dur": 183, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587268649, "dur": 21, "ph": "X", "name": "ProcessMessages 5599", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587268673, "dur": 746, "ph": "X", "name": "ReadAsync 5599", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587269431, "dur": 6, "ph": "X", "name": "ProcessMessages 1322", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587269439, "dur": 91, "ph": "X", "name": "ReadAsync 1322", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587269535, "dur": 11, "ph": "X", "name": "ProcessMessages 3633", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587269548, "dur": 346, "ph": "X", "name": "ReadAsync 3633", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587270029, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587270031, "dur": 1407, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587271450, "dur": 6, "ph": "X", "name": "ProcessMessages 6571", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587271461, "dur": 70, "ph": "X", "name": "ReadAsync 6571", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587271553, "dur": 2, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587271557, "dur": 46, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587271607, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587271609, "dur": 279, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587271892, "dur": 2, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587271896, "dur": 135, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272035, "dur": 2, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272038, "dur": 202, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272244, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272247, "dur": 411, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272677, "dur": 2, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272681, "dur": 73, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272757, "dur": 16, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272779, "dur": 60, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272881, "dur": 2, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272885, "dur": 64, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272953, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587272955, "dur": 151, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587273113, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587273117, "dur": 80, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587273223, "dur": 12, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587273239, "dur": 75, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587273320, "dur": 2, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587273325, "dur": 69, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587273400, "dur": 582, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587273986, "dur": 2, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587273989, "dur": 38, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274029, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274042, "dur": 47, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274093, "dur": 23, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274118, "dur": 62, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274191, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274194, "dur": 62, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274270, "dur": 516, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274790, "dur": 5, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274796, "dur": 78, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274884, "dur": 2, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274887, "dur": 81, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274971, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587274974, "dur": 918, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587275896, "dur": 2, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587275906, "dur": 103, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587276023, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587276026, "dur": 3132, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279164, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279168, "dur": 81, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279253, "dur": 7, "ph": "X", "name": "ProcessMessages 8042", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279261, "dur": 63, "ph": "X", "name": "ReadAsync 8042", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279329, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279332, "dur": 73, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279411, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279414, "dur": 86, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279504, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279507, "dur": 367, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279879, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587279881, "dur": 119, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587280048, "dur": 2, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587280051, "dur": 92, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587280155, "dur": 3, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587280189, "dur": 2039, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282234, "dur": 10, "ph": "X", "name": "ProcessMessages 8166", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282245, "dur": 51, "ph": "X", "name": "ReadAsync 8166", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282299, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282302, "dur": 94, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282402, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282405, "dur": 57, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282467, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282470, "dur": 77, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282552, "dur": 2, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282556, "dur": 58, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282618, "dur": 5, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587282624, "dur": 536, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283165, "dur": 2, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283168, "dur": 69, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283242, "dur": 3, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283246, "dur": 59, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283307, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283309, "dur": 60, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283374, "dur": 1, "ph": "X", "name": "ProcessMessages 1064", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283377, "dur": 50, "ph": "X", "name": "ReadAsync 1064", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283432, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283434, "dur": 48, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283487, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283490, "dur": 371, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283866, "dur": 2, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283870, "dur": 67, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283940, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587283945, "dur": 103, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284071, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284073, "dur": 596, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284674, "dur": 2, "ph": "X", "name": "ProcessMessages 1402", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284679, "dur": 36, "ph": "X", "name": "ReadAsync 1402", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284717, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284719, "dur": 31, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284756, "dur": 181, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284942, "dur": 2, "ph": "X", "name": "ProcessMessages 1456", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284945, "dur": 47, "ph": "X", "name": "ReadAsync 1456", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587284996, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587285000, "dur": 34, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587285045, "dur": 3, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587285049, "dur": 1889, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587286944, "dur": 8, "ph": "X", "name": "ProcessMessages 6273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587286955, "dur": 56, "ph": "X", "name": "ReadAsync 6273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287015, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287018, "dur": 62, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287084, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287086, "dur": 58, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287148, "dur": 1, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287153, "dur": 151, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287308, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287310, "dur": 532, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287847, "dur": 2, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287849, "dur": 46, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287899, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287901, "dur": 43, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287955, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587287958, "dur": 271, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288234, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288237, "dur": 59, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288300, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288303, "dur": 53, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288359, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288362, "dur": 51, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288417, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288423, "dur": 47, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288473, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587288475, "dur": 567, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289048, "dur": 2, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289052, "dur": 50, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289105, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289107, "dur": 50, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289161, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289163, "dur": 46, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289216, "dur": 416, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289636, "dur": 2, "ph": "X", "name": "ProcessMessages 1227", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289639, "dur": 42, "ph": "X", "name": "ReadAsync 1227", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289685, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289687, "dur": 65, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289755, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289757, "dur": 50, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289812, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289814, "dur": 45, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289863, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289866, "dur": 40, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289909, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587289912, "dur": 148, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290063, "dur": 7, "ph": "X", "name": "ProcessMessages 1509", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290073, "dur": 42, "ph": "X", "name": "ReadAsync 1509", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290119, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290121, "dur": 37, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290160, "dur": 1, "ph": "X", "name": "ProcessMessages 129", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290163, "dur": 60, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290232, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290234, "dur": 60, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290297, "dur": 1, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290300, "dur": 56, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290359, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290361, "dur": 52, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290417, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290419, "dur": 146, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290568, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290571, "dur": 54, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290629, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290631, "dur": 160, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290794, "dur": 2, "ph": "X", "name": "ProcessMessages 1392", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290797, "dur": 48, "ph": "X", "name": "ReadAsync 1392", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290857, "dur": 2, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290860, "dur": 127, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290991, "dur": 2, "ph": "X", "name": "ProcessMessages 1371", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587290995, "dur": 47, "ph": "X", "name": "ReadAsync 1371", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291055, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291057, "dur": 144, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291207, "dur": 2, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291210, "dur": 45, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291259, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291262, "dur": 43, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291308, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291311, "dur": 43, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291357, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291359, "dur": 53, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291415, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291418, "dur": 41, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291462, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291464, "dur": 31, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291498, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291500, "dur": 179, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291684, "dur": 1, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291686, "dur": 46, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291737, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291739, "dur": 43, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291785, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291787, "dur": 42, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291832, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291835, "dur": 45, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291883, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291885, "dur": 46, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291934, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291936, "dur": 55, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291995, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587291997, "dur": 132, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292138, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292140, "dur": 34, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292177, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292179, "dur": 44, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292227, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292229, "dur": 60, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292294, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292297, "dur": 394, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292694, "dur": 1, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292696, "dur": 37, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292735, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292738, "dur": 39, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292780, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292782, "dur": 49, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292835, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587292958, "dur": 48, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587293009, "dur": 2, "ph": "X", "name": "ProcessMessages 1566", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587293013, "dur": 1298, "ph": "X", "name": "ReadAsync 1566", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587294318, "dur": 9, "ph": "X", "name": "ProcessMessages 8127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587294329, "dur": 113, "ph": "X", "name": "ReadAsync 8127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587294446, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587294448, "dur": 372, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587294824, "dur": 2, "ph": "X", "name": "ProcessMessages 1866", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587294827, "dur": 41, "ph": "X", "name": "ReadAsync 1866", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587294871, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587294873, "dur": 492, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295372, "dur": 2, "ph": "X", "name": "ProcessMessages 1070", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295375, "dur": 54, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295434, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295437, "dur": 44, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295487, "dur": 4, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295492, "dur": 34, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295532, "dur": 35, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295570, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295572, "dur": 72, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295650, "dur": 2, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295654, "dur": 98, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295755, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295758, "dur": 45, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295807, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295810, "dur": 73, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295886, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295889, "dur": 53, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295945, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295947, "dur": 41, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295991, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587295994, "dur": 30, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296027, "dur": 66, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296098, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296100, "dur": 41, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296144, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296154, "dur": 40, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296200, "dur": 43, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296247, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296249, "dur": 207, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296460, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296462, "dur": 36, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296502, "dur": 4, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296507, "dur": 41, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296552, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296554, "dur": 51, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296608, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296611, "dur": 158, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296773, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296775, "dur": 46, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296825, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296828, "dur": 49, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296879, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296881, "dur": 34, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587296920, "dur": 282, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297206, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297209, "dur": 51, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297267, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297269, "dur": 59, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297333, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297336, "dur": 62, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297402, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297412, "dur": 56, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297471, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297486, "dur": 54, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297542, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297545, "dur": 53, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297604, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297607, "dur": 48, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297660, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297662, "dur": 47, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297713, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297715, "dur": 35, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297753, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297755, "dur": 33, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297791, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297793, "dur": 28, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297836, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297839, "dur": 54, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297897, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297900, "dur": 42, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297946, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587297955, "dur": 42, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587298001, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587298003, "dur": 43, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587298049, "dur": 7, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587298059, "dur": 203, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587298266, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587298272, "dur": 517, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587298794, "dur": 2, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587298797, "dur": 1948, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587300751, "dur": 3, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587300756, "dur": 2139, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587302901, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587302905, "dur": 180, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587303090, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587303092, "dur": 2503, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587305601, "dur": 10, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587305613, "dur": 1180, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587306801, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587306805, "dur": 2215, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587309025, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587309029, "dur": 492, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587309541, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587309544, "dur": 4233, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587313784, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587313788, "dur": 665, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587314465, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587314468, "dur": 6064, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587320572, "dur": 3, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587320581, "dur": 6013, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587326600, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587326604, "dur": 248, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587326856, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587326859, "dur": 4663, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587331531, "dur": 2, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587331535, "dur": 2566, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587334107, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587334110, "dur": 288, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587334403, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587334405, "dur": 1800, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587336210, "dur": 4, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587336216, "dur": 442, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587336662, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587336665, "dur": 556, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587337226, "dur": 2, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587337229, "dur": 360, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587337594, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587337597, "dur": 649, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587338250, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587338253, "dur": 401, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587338658, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587338662, "dur": 1691, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587340361, "dur": 2, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587340364, "dur": 309, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587340677, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587340680, "dur": 2039, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587342725, "dur": 3, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587342729, "dur": 941, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587343675, "dur": 2, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587343678, "dur": 46, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587343728, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587343734, "dur": 718, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587344456, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587344459, "dur": 50, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587344513, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587344515, "dur": 350, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587344869, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587344871, "dur": 2589, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587347466, "dur": 2, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587347470, "dur": 723, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587348196, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587348198, "dur": 294, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587348496, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587348498, "dur": 1935, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587350447, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587350451, "dur": 187, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587350642, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587350645, "dur": 2573, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587353225, "dur": 3, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587353229, "dur": 3594, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587356834, "dur": 3, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587356839, "dur": 224, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587357069, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587357072, "dur": 301, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587357379, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587357385, "dur": 1753, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587359145, "dur": 3, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587359149, "dur": 273, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587359426, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587359435, "dur": 3170, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587362613, "dur": 3, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587362618, "dur": 69, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587362702, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587362708, "dur": 1635, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587364350, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587364354, "dur": 295, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587364667, "dur": 2, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587364670, "dur": 72, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587364753, "dur": 8, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587364762, "dur": 1011, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587365781, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587365785, "dur": 1932, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587367750, "dur": 6, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587367772, "dur": 637, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587368414, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587368417, "dur": 74107, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587442535, "dur": 5, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587442541, "dur": 45878, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587488459, "dur": 4, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587492491, "dur": 138, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587492635, "dur": 3, "ph": "X", "name": "ProcessMessages 1198", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587492865, "dur": 4872, "ph": "X", "name": "ReadAsync 1198", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587497745, "dur": 3, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587497751, "dur": 800, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587498614, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587498619, "dur": 1525, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587500154, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587500158, "dur": 874, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587501074, "dur": 3, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587501079, "dur": 288, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587501372, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587501375, "dur": 2510, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587503893, "dur": 3, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587503897, "dur": 3620, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587507534, "dur": 4, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587507541, "dur": 1733, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587509285, "dur": 3, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587509303, "dur": 336, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587509645, "dur": 43, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587509689, "dur": 2104, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587511800, "dur": 6, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587511808, "dur": 391, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587512204, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587512207, "dur": 3343, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587515559, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587515563, "dur": 476, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587516062, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587516065, "dur": 3398, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587519471, "dur": 10, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587519485, "dur": 262, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587519751, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587519754, "dur": 3299, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587523059, "dur": 2, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587523062, "dur": 63, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587523130, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587523132, "dur": 801, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587523939, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587523943, "dur": 2588, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587526537, "dur": 3, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587526541, "dur": 445, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587526991, "dur": 12, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587527008, "dur": 2001, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587529016, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587529020, "dur": 511, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587529539, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587529542, "dur": 2237, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587531787, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587531790, "dur": 223, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587532018, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587532021, "dur": 2661, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587534690, "dur": 3, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587534695, "dur": 348, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587535051, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587535054, "dur": 1457, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587536519, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587536522, "dur": 355, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587536883, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587536886, "dur": 716, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587537609, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587537613, "dur": 405, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587538024, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587538027, "dur": 651, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587538682, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587538685, "dur": 270, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587538960, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587538962, "dur": 946, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587539916, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587539918, "dur": 320, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587540242, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587540245, "dur": 783, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587541031, "dur": 2, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587541034, "dur": 332, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587541370, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587541373, "dur": 618, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587541995, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587541997, "dur": 277, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587542277, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587542279, "dur": 602, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587542886, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587542888, "dur": 30, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587542922, "dur": 292, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587543217, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587543220, "dur": 35, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587543259, "dur": 30, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587543292, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587543294, "dur": 44, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587543342, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587543344, "dur": 775, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587544123, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587544125, "dur": 37, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587544165, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587544167, "dur": 726, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587544897, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587544899, "dur": 307, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587545210, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587545212, "dur": 1174, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587546391, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587546395, "dur": 214, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587546612, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587546615, "dur": 1138, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587547757, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587547759, "dur": 103, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587547866, "dur": 786, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587548655, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587548657, "dur": 278, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587548938, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587548940, "dur": 42, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587548985, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587548987, "dur": 39, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587549029, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587549030, "dur": 31, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587549065, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587549067, "dur": 33, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587549104, "dur": 1337, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587550446, "dur": 2, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587550449, "dur": 399, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587550853, "dur": 363, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551221, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551317, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551322, "dur": 94, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551420, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551423, "dur": 94, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551522, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551524, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551593, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551596, "dur": 122, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551722, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551725, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551783, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551799, "dur": 74, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551877, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551880, "dur": 63, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551977, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587551980, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552037, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552045, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552098, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552100, "dur": 107, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552212, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552214, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552275, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552378, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552380, "dur": 110, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552494, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552497, "dur": 55, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552554, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552557, "dur": 101, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552662, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552665, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552718, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552720, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552799, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552823, "dur": 89, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552916, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587552919, "dur": 113, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553035, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553037, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553154, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553157, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553217, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553220, "dur": 190, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553414, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553417, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553474, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553477, "dur": 107, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553587, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553590, "dur": 156, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553749, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553751, "dur": 203, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553959, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587553961, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554026, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554031, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554083, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554086, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554218, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554220, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554274, "dur": 151, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554429, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554432, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554488, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554699, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554701, "dur": 103, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554808, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554810, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554928, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587554931, "dur": 108, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587555043, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587555053, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587555106, "dur": 304, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587555414, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587555417, "dur": 188, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587555609, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587555612, "dur": 182, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587555798, "dur": 10, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587555812, "dur": 274, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556091, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556094, "dur": 100, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556198, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556200, "dur": 256, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556460, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556463, "dur": 430, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556897, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556900, "dur": 64, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556968, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587556971, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587557116, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587557119, "dur": 101, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587557225, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587557229, "dur": 230, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587557463, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587557467, "dur": 99, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587557570, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587557572, "dur": 645, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587558223, "dur": 9, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587558238, "dur": 136, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587558378, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587558381, "dur": 90, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587558476, "dur": 9, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587558490, "dur": 472, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587558967, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587558970, "dur": 84, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587559059, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587559061, "dur": 440, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587559508, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587559510, "dur": 632, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560203, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560209, "dur": 160, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560377, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560380, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560545, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560572, "dur": 135, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560711, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560714, "dur": 129, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560849, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560863, "dur": 78, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560945, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587560948, "dur": 217, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561169, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561172, "dur": 73, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561256, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561259, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561319, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561322, "dur": 307, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561633, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561636, "dur": 94, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561734, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561736, "dur": 89, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561840, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561843, "dur": 54, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561900, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587561903, "dur": 144, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562062, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562064, "dur": 122, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562191, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562194, "dur": 222, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562421, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562425, "dur": 80, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562509, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562512, "dur": 98, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562613, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562616, "dur": 90, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562709, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562712, "dur": 82, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562798, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562801, "dur": 80, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562886, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562888, "dur": 78, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562971, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587562974, "dur": 65, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563043, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563046, "dur": 60, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563109, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563112, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563167, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563169, "dur": 166, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563338, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563340, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563411, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563416, "dur": 81, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563501, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563504, "dur": 59, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563566, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563568, "dur": 73, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563645, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563648, "dur": 80, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563731, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563734, "dur": 81, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563819, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587563821, "dur": 223, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587564061, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587564063, "dur": 21561, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587585635, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587585640, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587585717, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587585720, "dur": 3327, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587589056, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587589061, "dur": 11447, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587600518, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587600522, "dur": 64, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587600590, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587600592, "dur": 1254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587601852, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587601855, "dur": 483, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587602343, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587602345, "dur": 302, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587602655, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587602666, "dur": 649, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587603320, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587603322, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587603481, "dur": 745, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587604229, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587604231, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587604397, "dur": 1839, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587606243, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587606246, "dur": 345, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587606596, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587606599, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587606741, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587606744, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587606868, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587607065, "dur": 1209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608279, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608281, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608423, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608425, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608547, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608549, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608751, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608753, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608824, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587608826, "dur": 217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587609048, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587609050, "dur": 658, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587609712, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587609714, "dur": 170, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587609896, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587609901, "dur": 673, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587610578, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587610580, "dur": 3577, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614164, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614168, "dur": 187, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614361, "dur": 15, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614385, "dur": 184, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614574, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614576, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614671, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614673, "dur": 81, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614758, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614760, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614816, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614820, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614902, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587614904, "dur": 355, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587615263, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587615265, "dur": 296, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587615566, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587615568, "dur": 1193, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587616767, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587616770, "dur": 406, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587617181, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587617184, "dur": 458, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587617645, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587617647, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587617819, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587617821, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587617976, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587617978, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587618174, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587618176, "dur": 1380, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587619564, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587619570, "dur": 2983, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587622561, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587622565, "dur": 1105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587623677, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587623679, "dur": 331, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587624015, "dur": 13, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587624033, "dur": 2040, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626077, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626079, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626155, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626158, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626368, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626371, "dur": 48, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626423, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626425, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626502, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626507, "dur": 435, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626946, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587626952, "dur": 312, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587627267, "dur": 748, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587628019, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587628102, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587628104, "dur": 227, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587628336, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587628338, "dur": 804, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587629146, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587629149, "dur": 675, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587629828, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587629831, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587629889, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587629891, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587629947, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587629950, "dur": 116, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587630070, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587630073, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587630263, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587630266, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587630409, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587630412, "dur": 650, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587631066, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587631069, "dur": 483, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587631556, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587631558, "dur": 489, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587632060, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587632062, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587632179, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587632181, "dur": 553, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587632738, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587632740, "dur": 372, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587633118, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587633395, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587633397, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587633577, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587633580, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587633666, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587633763, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587633765, "dur": 341, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587634111, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587634113, "dur": 477, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587634594, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587634597, "dur": 2417, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587637021, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587637023, "dur": 372, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587637404, "dur": 7, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587637413, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587637607, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587637611, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587637678, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587637680, "dur": 393, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587638080, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587638083, "dur": 642, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587638731, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587638734, "dur": 1069, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587639810, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587639813, "dur": 242, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587640069, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587640072, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587640299, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587640301, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587640494, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587640496, "dur": 1202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587641705, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587641709, "dur": 510, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587642223, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587642234, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587642316, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587642319, "dur": 647, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587642972, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587642987, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643088, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643091, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643196, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643199, "dur": 602, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643806, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643809, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643867, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643869, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643944, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587643946, "dur": 81, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644031, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644033, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644098, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644279, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644289, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644363, "dur": 168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644535, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644537, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644709, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587644711, "dur": 928, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587645643, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587645645, "dur": 184, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587645833, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587645835, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587645975, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587645977, "dur": 199, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587646181, "dur": 345, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587646538, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587646541, "dur": 95, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587646642, "dur": 392, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647038, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647040, "dur": 47, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647092, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647239, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647242, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647383, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647385, "dur": 383, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647773, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647775, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647868, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587647870, "dur": 408, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587648282, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587648284, "dur": 286737, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935032, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935036, "dur": 85, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935128, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935131, "dur": 68, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935203, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935205, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935275, "dur": 70, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935361, "dur": 42, "ph": "X", "name": "ReadAsync 4142", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935408, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935410, "dur": 98, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587935512, "dur": 2782, "ph": "X", "name": "ProcessMessages 4838", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587938301, "dur": 4372, "ph": "X", "name": "ReadAsync 4838", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587942691, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587942697, "dur": 127, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587942828, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587942830, "dur": 849, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587943683, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587943685, "dur": 1306, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587944995, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587945001, "dur": 484, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587945489, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587945491, "dur": 1539, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587947037, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587947040, "dur": 272, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587947318, "dur": 1528, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587948851, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587948854, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587948941, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587948944, "dur": 1662, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587950640, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587950644, "dur": 316, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587950965, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587950967, "dur": 2410, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587953381, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587953384, "dur": 680, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587954078, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587954082, "dur": 310, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587954398, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587954400, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587954601, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587954603, "dur": 2841, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587957451, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587957455, "dur": 456, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587957917, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587957919, "dur": 523, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587958447, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587958450, "dur": 2856, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587961313, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587961317, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587961395, "dur": 298, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587961698, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587961851, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587961853, "dur": 315, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587962174, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587962177, "dur": 468, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587962650, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587962652, "dur": 2073, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587964731, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587964734, "dur": 628, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587965367, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587965370, "dur": 342, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587965717, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587965749, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587965880, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587965882, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587965976, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587965979, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966061, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966064, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966182, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966185, "dur": 263, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966452, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966455, "dur": 76, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966535, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966537, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966642, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966644, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966792, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966794, "dur": 180, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966979, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587966982, "dur": 116, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967101, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967104, "dur": 108, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967216, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967218, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967276, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967279, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967374, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967376, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967481, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967483, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967596, "dur": 4, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967606, "dur": 142, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967752, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967754, "dur": 94, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967853, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967855, "dur": 114, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967973, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587967975, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968041, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968043, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968125, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968127, "dur": 140, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968271, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968273, "dur": 85, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968363, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968365, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968464, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968466, "dur": 56, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968526, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968528, "dur": 113, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968645, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968648, "dur": 78, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968730, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968732, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968847, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968849, "dur": 67, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968921, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968923, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968995, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587968998, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969096, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969099, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969177, "dur": 11, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969191, "dur": 118, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969313, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969315, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969444, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969447, "dur": 88, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969539, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969541, "dur": 70, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969616, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969618, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969669, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969809, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587969811, "dur": 1315, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587971157, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587971160, "dur": 394, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587971559, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587971562, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587971706, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587971708, "dur": 266, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587971979, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587971982, "dur": 488, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587972476, "dur": 272, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419587972752, "dur": 9808, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 70593, "tid": 14, "ts": 1748419588019684, "dur": 3134, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 70593, "tid": 8589934592, "ts": 1748419587204410, "dur": 318382, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748419587522797, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748419587522807, "dur": 4336, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 70593, "tid": 14, "ts": 1748419588022821, "dur": 15, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 70593, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419587047354, "dur": 937207, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419587163564, "dur": 28904, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419587984883, "dur": 9221, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419587989372, "dur": 2990, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419587994194, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 70593, "tid": 14, "ts": 1748419588022839, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748419587215151, "dur": 7623, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419587222879, "dur": 41202, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419587264191, "dur": 97, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748419587264288, "dur": 180, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419587265436, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587265738, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587266061, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_72608D0D80FBEA70.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587266163, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_E69ADDE2B61683F5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587266486, "dur": 179, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587267077, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587267326, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587267440, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587267797, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587267948, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587268211, "dur": 425, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587268736, "dur": 227, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587269163, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587272603, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587272767, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748419587272825, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587277558, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419587282484, "dur": 615, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587286958, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587287027, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748419587287988, "dur": 129, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419587289095, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748419587294619, "dur": 524, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419587296190, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419587296436, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419587301092, "dur": 238, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587301392, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587303545, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587306192, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587310084, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587321115, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587331525, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587339343, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587341322, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587345195, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587359836, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748419587368344, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587390057, "dur": 29653, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587419720, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748419587422539, "dur": 221, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587488721, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587489192, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587492887, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748419587495884, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748419587499571, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587501632, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587509941, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748419587510322, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587523833, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748419587524545, "dur": 143, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587527292, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748419587535335, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419587535779, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419587264477, "dur": 286750, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419587551240, "dur": 421551, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419587972929, "dur": 159, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419587973204, "dur": 1518, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748419587264398, "dur": 286852, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587551254, "dur": 5451, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587556720, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587556871, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587557852, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587557977, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587558897, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587559091, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748419587559348, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587559751, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587559988, "dur": 1340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587561389, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587561580, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587561817, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587561923, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587562100, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587562275, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587562482, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587562606, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587562859, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587563134, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587563197, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587563375, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587563565, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587563663, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587563896, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587564218, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587564316, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419587564539, "dur": 2213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587566753, "dur": 1965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587568719, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587569702, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587570950, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587572422, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587574018, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587575174, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587576386, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587577575, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587578757, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587580181, "dur": 716, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/Converter/ReadonlyMaterialConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419587580036, "dur": 2014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587582050, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587583478, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587584895, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587586084, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587587767, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@14.1.0/Editor/Data/Nodes/Procedural/Noise/VoronoiNode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419587587767, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587589681, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587591134, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587592488, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587593843, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587595202, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587596578, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587598005, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587599918, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587601533, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419587602448, "dur": 11004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587613453, "dur": 1498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587614960, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419587615055, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419587615500, "dur": 2151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587617652, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587617860, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419587618358, "dur": 7634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587625993, "dur": 789, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587626829, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_C4A720C3D568CE76.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419587626934, "dur": 2746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587629681, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587629888, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419587630647, "dur": 3011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587633659, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587633810, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419587634841, "dur": 2778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587637624, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587637759, "dur": 2605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587640365, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587640504, "dur": 2896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587643401, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587643639, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587646040, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587646452, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587646503, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587646992, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587647043, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587647322, "dur": 291765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587939090, "dur": 7143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587946234, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587946327, "dur": 3305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587949634, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587949706, "dur": 5016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587954762, "dur": 3938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587958701, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587958813, "dur": 3839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587962700, "dur": 3714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419587966415, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587966679, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587967405, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587968177, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587968362, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587968537, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419587968588, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587968713, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587968863, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587968931, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587969151, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587969607, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587969700, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587969788, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587969888, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748419587970017, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587970123, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587970242, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748419587970312, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587970399, "dur": 1440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419587971896, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587264399, "dur": 286859, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587551299, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748419587551662, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587551884, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587552086, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D0749362471C98A9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587552293, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587552560, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587552781, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587552870, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587552955, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587553248, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587553368, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748419587553680, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_0118987859331F44.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587553984, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748419587554161, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748419587554349, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587554976, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587555256, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748419587555479, "dur": 1638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587557144, "dur": 2149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587559293, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587559804, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587559868, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587561373, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587561574, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587561770, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587561882, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587562043, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587562192, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587562508, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587562607, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587562838, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587563185, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587563353, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587563534, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587563612, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587563736, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587563826, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587564088, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587564291, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419587564847, "dur": 754, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/InputManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419587566047, "dur": 931, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/DataManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419587566978, "dur": 724, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/AudioManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419587564531, "dur": 5062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587569593, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587570778, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587572170, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587573784, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587574951, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587576188, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587577358, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587578623, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587580191, "dur": 722, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ShaderGraph/Nodes/UniversalSampleBufferNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419587579825, "dur": 1998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587581824, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587583248, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587584731, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587585981, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587587432, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587588950, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587590416, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587591707, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587593079, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587594415, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587595796, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587597294, "dur": 1930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587599225, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587600776, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587602131, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587602679, "dur": 4160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587606846, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587607314, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_A298F0F0964CDA5C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587607395, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587607460, "dur": 1672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587609180, "dur": 3258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587612439, "dur": 2454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587614953, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587615409, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587616001, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587618251, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587618743, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Runtime.ref.dll_DEAB70CB54ED1324.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587618806, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587618894, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587620188, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587620273, "dur": 6999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587627273, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587627701, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587628814, "dur": 9233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587638048, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587638383, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587638457, "dur": 909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587639368, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587639431, "dur": 3956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587643388, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587643926, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587644676, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587644739, "dur": 1700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587646440, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587646595, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.Editor.ref.dll_B6E543B7064A5CCA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419587646725, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587646791, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587647041, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587647100, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587647327, "dur": 291863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587939194, "dur": 2512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587941708, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587942224, "dur": 3512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587945737, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587945836, "dur": 4155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587950000, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587950082, "dur": 4249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587954332, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587954643, "dur": 3710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587958354, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587958420, "dur": 3658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587962079, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587962163, "dur": 3260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587965424, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587965500, "dur": 6218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419587971719, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419587971873, "dur": 846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587264411, "dur": 286879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587551300, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748419587551651, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587551874, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_710F5443A18E10C1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587552059, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_97FEC325F9337F2C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587552191, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587552500, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_EBB973DC47E5C128.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587552553, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AB6C0CE8644C7690.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587552829, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587552933, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587553012, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587553158, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587553260, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587553328, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587553417, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587553478, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587553750, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587554010, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748419587554293, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587554504, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587554782, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587554844, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_FB7A597403E5FDBB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587555075, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587555724, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748419587555904, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587556105, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587556386, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587556515, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587556616, "dur": 1336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587558015, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587558310, "dur": 956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587559285, "dur": 1332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587560628, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587560849, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587561028, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587561193, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587561327, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587561410, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587561620, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748419587561858, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587561939, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587562119, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587562307, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587562407, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587562560, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587562697, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587562966, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748419587563213, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587563319, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587563419, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587563591, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587563678, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587563799, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587563860, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587564296, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419587564526, "dur": 917, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Weapons/WeaponData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419587566123, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Utils/ObjectPool.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419587567853, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/UI/EnemyHealthBar.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419587564526, "dur": 5875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587570402, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587571744, "dur": 1703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587573448, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587574628, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587575811, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587576995, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587578253, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587580197, "dur": 729, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ShaderGraph/UniversalStructs.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419587579372, "dur": 2078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587581451, "dur": 1412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587582864, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587584342, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587585646, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587586359, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419587586410, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587586495, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587587960, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587589574, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587590993, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587592365, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587593771, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587595111, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587596479, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587597914, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587599785, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587601387, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587602458, "dur": 6576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587609035, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587609597, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_4EAD86AD4C1B715A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587609673, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587609756, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587610473, "dur": 4311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587614785, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587615329, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419587615923, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587615983, "dur": 1962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587617946, "dur": 1001, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587618962, "dur": 7186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1748419587626200, "dur": 1172, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587935677, "dur": 585, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587628055, "dur": 308266, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1748419587938995, "dur": 5211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587944207, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587944446, "dur": 4568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587949020, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587949393, "dur": 3597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587952991, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587953074, "dur": 3493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587956568, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587956632, "dur": 3972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587960606, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587960681, "dur": 3903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587964586, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587964672, "dur": 5737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419587970498, "dur": 1987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419587972507, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587264422, "dur": 286878, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587551307, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587551786, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587552003, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A49EDD7A339F9A85.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587552181, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587552360, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587552765, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587552858, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587552920, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587553283, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587553356, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587553432, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587553669, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587553797, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587554570, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748419587554822, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748419587555052, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748419587555227, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587555488, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748419587555688, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587555815, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587556323, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587556438, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587556591, "dur": 923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587557548, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587557752, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587557861, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587558323, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587558895, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587559010, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748419587559303, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587559715, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587560037, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587560133, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587562260, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587562478, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587562583, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587562668, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587563139, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587563262, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587563349, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587563475, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587563601, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587563701, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587563963, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587564199, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587564450, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419587564628, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587564823, "dur": 2212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587567035, "dur": 1846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587568881, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587569841, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587571157, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587572608, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587574149, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587575275, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587576494, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587577743, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587580173, "dur": 718, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ShaderGUI/Shaders/UnlitShader.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419587578914, "dur": 1983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587580897, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587582229, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587583684, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587585088, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587586229, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587587744, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587589178, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587590740, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587592000, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587593371, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587594742, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587596127, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587597545, "dur": 1921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587599467, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587600994, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587602243, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587602568, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587603342, "dur": 709, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587604061, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C5B5631EE4DDD44F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587604122, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587604191, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587606942, "dur": 1972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587608915, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587609012, "dur": 1408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587610421, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587610483, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587611341, "dur": 2989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587614331, "dur": 588, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587614929, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_1DF7BBC55D029A75.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587614983, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587615074, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587615616, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587615673, "dur": 11218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587626892, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587627209, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587627273, "dur": 1236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587628543, "dur": 12750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587641294, "dur": 727, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587642041, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587642111, "dur": 923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587643084, "dur": 3356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587646441, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587646591, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_7BBE081BF0E8AF4A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587646679, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587646747, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587647276, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587648007, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587648124, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587648519, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419587648651, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587649055, "dur": 290301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587939358, "dur": 4725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587944084, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587944434, "dur": 4978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587949413, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587949503, "dur": 3976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587953480, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587953608, "dur": 3476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587957139, "dur": 3887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587961027, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587961404, "dur": 4356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587965813, "dur": 6142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419587971962, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587972134, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419587972288, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587264435, "dur": 286876, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587551317, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587551764, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587552117, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587552425, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_2EC518F8CEEB6CA9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587552516, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587552746, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587552812, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587552882, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587552976, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587553033, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587553186, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587553303, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587553370, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587553454, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419587554026, "dur": 8824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587562851, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587563113, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587563945, "dur": 20982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587584929, "dur": 1339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587586340, "dur": 3315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587589656, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587589729, "dur": 11183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587600914, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587601207, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587601266, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587601356, "dur": 1025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587602420, "dur": 4242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587606663, "dur": 889, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587607623, "dur": 1596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587609220, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587609290, "dur": 2971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587612262, "dur": 2619, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587614896, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587615665, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587616122, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587616325, "dur": 7712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587624038, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587624390, "dur": 2577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587626968, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587627118, "dur": 3078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587630198, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587630749, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_8E9298DC6B12ED57.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587630902, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587631163, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587631819, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587632269, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587632796, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587633424, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419587634136, "dur": 3456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587637594, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587638085, "dur": 2519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587640605, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587640793, "dur": 1902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587642696, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587642839, "dur": 4100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587646939, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587647325, "dur": 291816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587939144, "dur": 5568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587944766, "dur": 3018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587947785, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587947867, "dur": 3684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587951552, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587951701, "dur": 3655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587955358, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587955439, "dur": 6909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587962380, "dur": 3517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587965898, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419587966115, "dur": 6426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419587972633, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587264453, "dur": 286869, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587551328, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587551744, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587551798, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_25D10CC8709418E4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587551969, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587552220, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587552477, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587552686, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587552897, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587553043, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587553148, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587553257, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587553345, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587553470, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587553635, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419587554344, "dur": 7923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587562269, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587562816, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419587563244, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419587563330, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587563386, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587563575, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587563669, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587563777, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419587564133, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419587564212, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419587564454, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587564530, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587564604, "dur": 2266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587566870, "dur": 1898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587568768, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587569723, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587571013, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587572447, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587574004, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587575144, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587576374, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587577588, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587578781, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587580164, "dur": 723, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/Camera/UniversalRenderPipelineCameraUI.Environment.Skin.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419587580085, "dur": 1986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587582072, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587583529, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587584957, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587586173, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587587796, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587589262, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587590802, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587592098, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587593486, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587594843, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587596216, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587597677, "dur": 1966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587599643, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587601147, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587602331, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587602633, "dur": 2150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587604784, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587604993, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587605068, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587605143, "dur": 2592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587607736, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587607796, "dur": 1587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587609458, "dur": 3056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587612514, "dur": 2358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587614939, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587615355, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587615954, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587616033, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587618406, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587618521, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Editor.ref.dll_8237F4420D536756.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587618602, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587618716, "dur": 5823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587624540, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587624779, "dur": 5318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587630098, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587630625, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587630706, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587630790, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587630861, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587631061, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587631149, "dur": 15786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587646936, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587647146, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419587647347, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587647802, "dur": 291484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587939288, "dur": 2827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587942116, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587942242, "dur": 3656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587945898, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587945956, "dur": 3693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587949650, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587949746, "dur": 4429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587954176, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587954581, "dur": 3604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587958186, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587958275, "dur": 3750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587962067, "dur": 5946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419587968223, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419587968341, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587968502, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587968566, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587968674, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748419587968742, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587968964, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419587969025, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587969291, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587969471, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587969620, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587970051, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748419587970121, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587970211, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587970334, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587970438, "dur": 1833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419587972320, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587264462, "dur": 286871, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587551339, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587551773, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587552083, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_888C5CF0B798DF8B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587552351, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587552741, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587552801, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587552863, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587552964, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587553021, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587553170, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587553271, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587553344, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587553427, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587553500, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587553657, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_F0DEE4EE0970DD2B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587553966, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587554377, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587554609, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748419587554866, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587555016, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587555231, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587555462, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587556058, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748419587556146, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587556428, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587556567, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587556623, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587556935, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587557717, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748419587557996, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587558993, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587559073, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748419587559297, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587559723, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587559837, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587560251, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587560435, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587561541, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587561710, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587561852, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587561964, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587562138, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587562285, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587562464, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587562553, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587562691, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587563312, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587563374, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587563546, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587563625, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587563739, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587563954, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419587564515, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587564580, "dur": 2207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587566788, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587568736, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587569714, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587570959, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587572185, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587573862, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587574995, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587576233, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587577415, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587578687, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587580148, "dur": 723, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/RendererFeatures/NewRendererFeatureDropdownItem.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419587579915, "dur": 2054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587581969, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587583416, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587584855, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587586121, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587587754, "dur": 1922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587589677, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587591117, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587592477, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587593867, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587595225, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587596596, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587598041, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587599932, "dur": 1579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587601512, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587602420, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587602542, "dur": 23165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587625709, "dur": 1408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587627126, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_4EC1FE8B3807FE28.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587627233, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587628476, "dur": 5796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587634273, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587634536, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587635363, "dur": 3270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587638634, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587638781, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_697AC546F729FDEE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587638899, "dur": 2050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587640950, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587641051, "dur": 2590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587643642, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587643964, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419587644804, "dur": 1903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587646708, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587646946, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587647042, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587647097, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587647319, "dur": 292010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587939336, "dur": 5060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587944397, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587944494, "dur": 3301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587947797, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587947918, "dur": 3467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587951386, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587951524, "dur": 3361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587954886, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587954948, "dur": 3517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587958466, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587958571, "dur": 4189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587962761, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587963072, "dur": 6474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419587969547, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587969654, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748419587969718, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587969814, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587969890, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748419587970017, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587970103, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587970221, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587970374, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419587970555, "dur": 2045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587264473, "dur": 286870, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587551346, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587551808, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1996541149DA13E0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587552201, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587552758, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587552830, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587552913, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587553054, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_51CCB6F491E5150D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587553155, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587553240, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587553644, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587553847, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587554049, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748419587554281, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587554803, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748419587555088, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587555575, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748419587555819, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587556079, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587556354, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587556413, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587556525, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587556863, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587557224, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748419587557565, "dur": 1642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587559248, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587559678, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587559774, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587560408, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587560521, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587561362, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587561551, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587561740, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587561865, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587561987, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587562133, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587562465, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587562548, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587562698, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587563133, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587563194, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587563336, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587563458, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587563597, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587563688, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587563911, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587564243, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419587564541, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587564732, "dur": 2117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587566850, "dur": 1926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587568777, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587569751, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587571001, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587572399, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587573996, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587575135, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587576344, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587577519, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587578730, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587580169, "dur": 711, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/GlobalSettings/UniversalRenderPipelineGlobalSettingsUI.Skin.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419587580018, "dur": 2005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587582023, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587583426, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587584882, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587586098, "dur": 1744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587587843, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587589286, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587590783, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587592061, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587593440, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587594818, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587596178, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587597588, "dur": 1921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587599509, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587601041, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587602287, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587603327, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587603391, "dur": 6752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587610144, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587610551, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587610633, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587611311, "dur": 3219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587614531, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587615064, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587615477, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587615538, "dur": 1443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587616982, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587617464, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587617541, "dur": 4632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587622174, "dur": 1036, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587623229, "dur": 5509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587628739, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587629035, "dur": 1238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587630273, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587630620, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587630929, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587631084, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587631683, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587631773, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587632322, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587632940, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587633486, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587634271, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587634353, "dur": 3581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587637935, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587638425, "dur": 2689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587641115, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587641218, "dur": 3185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587644404, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587644819, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587644903, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587644993, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587645098, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587645157, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587645297, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587645483, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587646141, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587646195, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587646455, "dur": 94, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587646549, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587646787, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419587647398, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587647874, "dur": 291448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587939331, "dur": 3232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587942564, "dur": 939, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587943546, "dur": 2929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587946476, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587946833, "dur": 3734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587950569, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587950647, "dur": 4329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587954977, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587955248, "dur": 3676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587958925, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587959010, "dur": 3510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587962521, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587962582, "dur": 3676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587966259, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587966344, "dur": 6071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419587972416, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419587972503, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419587981232, "dur": 1329, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 70593, "tid": 14, "ts": 1748419588024124, "dur": 3054, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 70593, "tid": 14, "ts": 1748419588027491, "dur": 3385, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 70593, "tid": 14, "ts": 1748419588015638, "dur": 17167, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}