{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 70122, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 70122, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 70122, "tid": 9, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 70122, "tid": 9, "ts": 1748418229892409, "dur": 1974, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 70122, "tid": 9, "ts": 1748418229900739, "dur": 1326, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 70122, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 70122, "tid": 1, "ts": 1748418225961491, "dur": 11342, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70122, "tid": 1, "ts": 1748418225972837, "dur": 47007, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70122, "tid": 1, "ts": 1748418226019853, "dur": 68313, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 70122, "tid": 9, "ts": 1748418229902072, "dur": 486, "ph": "X", "name": "", "args": {}}, {"pid": 70122, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418225958868, "dur": 21479, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418225980351, "dur": 3892542, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418225981707, "dur": 7394, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418225989110, "dur": 1274, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418225990387, "dur": 16127, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226006521, "dur": 678, "ph": "X", "name": "ProcessMessages 1805", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007204, "dur": 97, "ph": "X", "name": "ReadAsync 1805", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007308, "dur": 9, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007330, "dur": 81, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007421, "dur": 1, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007424, "dur": 96, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007526, "dur": 2, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007529, "dur": 40, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007572, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007575, "dur": 48, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007626, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007631, "dur": 38, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007674, "dur": 4, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007679, "dur": 35, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007725, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007727, "dur": 46, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007777, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007780, "dur": 51, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007834, "dur": 2, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007837, "dur": 37, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007877, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007878, "dur": 54, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007936, "dur": 3, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226007940, "dur": 96, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008040, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008042, "dur": 89, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008135, "dur": 2, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008138, "dur": 51, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008192, "dur": 13, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008213, "dur": 60, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008276, "dur": 1, "ph": "X", "name": "ProcessMessages 1398", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008278, "dur": 68, "ph": "X", "name": "ReadAsync 1398", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008349, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008351, "dur": 60, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008430, "dur": 16, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008449, "dur": 59, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008511, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008513, "dur": 68, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008598, "dur": 2, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008602, "dur": 114, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008721, "dur": 2, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008725, "dur": 58, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008792, "dur": 3, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008796, "dur": 50, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008849, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008851, "dur": 50, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008905, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008907, "dur": 44, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008954, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008957, "dur": 38, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226008998, "dur": 57, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009059, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009065, "dur": 55, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009126, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009129, "dur": 52, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009184, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009186, "dur": 49, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009239, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009241, "dur": 37, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009288, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009291, "dur": 57, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009351, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009354, "dur": 42, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009400, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009402, "dur": 56, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009466, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226009472, "dur": 41, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226010793, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226010797, "dur": 58, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226010859, "dur": 3, "ph": "X", "name": "ProcessMessages 2232", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226010863, "dur": 118, "ph": "X", "name": "ReadAsync 2232", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226010985, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226010989, "dur": 50, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011041, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011046, "dur": 50, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011100, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011103, "dur": 34, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011143, "dur": 2, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011151, "dur": 41, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011196, "dur": 35, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011236, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011239, "dur": 38, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011279, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011281, "dur": 111, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011395, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011398, "dur": 78, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011479, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011484, "dur": 34, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011522, "dur": 32, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011556, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011558, "dur": 33, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011595, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226011598, "dur": 406, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012007, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012009, "dur": 70, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012083, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012088, "dur": 36, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012127, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012132, "dur": 38, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012178, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012181, "dur": 184, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012368, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012370, "dur": 161, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012534, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012536, "dur": 35, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012574, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012584, "dur": 33, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012623, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012625, "dur": 221, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012849, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226012852, "dur": 478, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013336, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013338, "dur": 42, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013383, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013385, "dur": 65, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013454, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013457, "dur": 37, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013504, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013506, "dur": 59, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013576, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013578, "dur": 39, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013620, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013633, "dur": 36, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013671, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013674, "dur": 48, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013730, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226013732, "dur": 340, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226014075, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226014089, "dur": 37, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226014129, "dur": 4, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015311, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015362, "dur": 5, "ph": "X", "name": "ProcessMessages 5758", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015368, "dur": 33, "ph": "X", "name": "ReadAsync 5758", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015410, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015412, "dur": 32, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015447, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015449, "dur": 42, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015493, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015495, "dur": 28, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015527, "dur": 28, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015562, "dur": 27, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015594, "dur": 31, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015628, "dur": 30, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015669, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015671, "dur": 147, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015822, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015824, "dur": 32, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015859, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015867, "dur": 107, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015977, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226015979, "dur": 47, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016032, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016035, "dur": 36, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016073, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016075, "dur": 207, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016284, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016285, "dur": 38, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016327, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016329, "dur": 35, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016367, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016370, "dur": 42, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016415, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016420, "dur": 29, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016452, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016454, "dur": 28, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016486, "dur": 63, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016551, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016553, "dur": 33, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016589, "dur": 35, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016629, "dur": 32, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016663, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016666, "dur": 147, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016817, "dur": 35, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016857, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016860, "dur": 38, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016899, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016901, "dur": 52, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016957, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226016963, "dur": 48, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017016, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017018, "dur": 239, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017261, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017263, "dur": 35, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017301, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017303, "dur": 31, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017337, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017339, "dur": 31, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017373, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017375, "dur": 28, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017406, "dur": 67, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017478, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017480, "dur": 32, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017515, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017517, "dur": 124, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017644, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017648, "dur": 38, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017689, "dur": 1, "ph": "X", "name": "ProcessMessages 1265", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017692, "dur": 34, "ph": "X", "name": "ReadAsync 1265", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017729, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017731, "dur": 31, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017765, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017767, "dur": 40, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017810, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017813, "dur": 47, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017863, "dur": 1, "ph": "X", "name": "ProcessMessages 97", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226017865, "dur": 337, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018207, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018209, "dur": 34, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018246, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018248, "dur": 43, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018294, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018297, "dur": 39, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018341, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018343, "dur": 71, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018417, "dur": 30, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018452, "dur": 26, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018481, "dur": 27, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018512, "dur": 29, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018544, "dur": 27, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018574, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018576, "dur": 248, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018829, "dur": 4, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018834, "dur": 49, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018886, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018889, "dur": 35, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018927, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226018929, "dur": 89, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019022, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019025, "dur": 38, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019066, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019068, "dur": 39, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019110, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019113, "dur": 31, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019147, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019149, "dur": 156, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019308, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019311, "dur": 33, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019347, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019352, "dur": 35, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019390, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019393, "dur": 31, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019427, "dur": 29, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019460, "dur": 379, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019843, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019846, "dur": 47, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019896, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019899, "dur": 38, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019940, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019942, "dur": 40, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019985, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226019987, "dur": 174, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020165, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020168, "dur": 38, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020210, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020212, "dur": 34, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020251, "dur": 28, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020282, "dur": 61, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020347, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020349, "dur": 30, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020383, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020385, "dur": 35, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020423, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020426, "dur": 32, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020462, "dur": 3, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020466, "dur": 33, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020501, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020503, "dur": 33, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020540, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020542, "dur": 29, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020575, "dur": 31, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020608, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020610, "dur": 31, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020644, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020646, "dur": 32, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020681, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020683, "dur": 60, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020746, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020748, "dur": 31, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020784, "dur": 30, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020816, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020818, "dur": 31, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020853, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020855, "dur": 29, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020888, "dur": 29, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020921, "dur": 34, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020961, "dur": 29, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226020998, "dur": 32, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021033, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021035, "dur": 32, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021070, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021073, "dur": 31, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021109, "dur": 131, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021243, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021245, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021281, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021283, "dur": 29, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021316, "dur": 29, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021348, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021350, "dur": 29, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021383, "dur": 63, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021449, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021451, "dur": 31, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021485, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021487, "dur": 32, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021523, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021525, "dur": 30, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021560, "dur": 27, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021591, "dur": 66, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021660, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021662, "dur": 32, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021698, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021700, "dur": 32, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021734, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021737, "dur": 31, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021772, "dur": 26, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021802, "dur": 31, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021838, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021840, "dur": 35, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021878, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021880, "dur": 30, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021912, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021914, "dur": 28, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021945, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021947, "dur": 31, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226021982, "dur": 59, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022043, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022046, "dur": 30, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022080, "dur": 29, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022114, "dur": 29, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022146, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022149, "dur": 32, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022184, "dur": 190, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022378, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022380, "dur": 36, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022420, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022422, "dur": 36, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022461, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022464, "dur": 36, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022503, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022505, "dur": 29, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022538, "dur": 86, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022627, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022629, "dur": 29, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022661, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022663, "dur": 34, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022705, "dur": 31, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022738, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022740, "dur": 29, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022772, "dur": 25, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022801, "dur": 26, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022830, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022832, "dur": 32, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022867, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022869, "dur": 30, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022904, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022906, "dur": 29, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022939, "dur": 33, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226022976, "dur": 141, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023121, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023123, "dur": 34, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023159, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023161, "dur": 32, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023196, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023201, "dur": 36, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023241, "dur": 29, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023274, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023277, "dur": 85, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023365, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023366, "dur": 28, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023398, "dur": 29, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023429, "dur": 4, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023434, "dur": 31, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023468, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023470, "dur": 34, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023508, "dur": 64, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023575, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023577, "dur": 30, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023610, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023612, "dur": 44, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023658, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023661, "dur": 31, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023694, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023696, "dur": 61, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023759, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023760, "dur": 33, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023797, "dur": 31, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023831, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023833, "dur": 28, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023863, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023865, "dur": 31, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023900, "dur": 29, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023932, "dur": 27, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023964, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226023965, "dur": 38, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024007, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024012, "dur": 35, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024050, "dur": 29, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024083, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024085, "dur": 30, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024119, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024151, "dur": 31, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024185, "dur": 34, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024222, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024225, "dur": 32, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024260, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024262, "dur": 31, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024296, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024298, "dur": 31, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024333, "dur": 33, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024369, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024371, "dur": 30, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024407, "dur": 29, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024440, "dur": 30, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024472, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024474, "dur": 25, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024503, "dur": 147, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024653, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024654, "dur": 40, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024698, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024701, "dur": 35, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024738, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024741, "dur": 33, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024776, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024778, "dur": 30, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024810, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024881, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024914, "dur": 1, "ph": "X", "name": "ProcessMessages 114", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024916, "dur": 33, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024956, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024958, "dur": 34, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024995, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226024997, "dur": 29, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025030, "dur": 29, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025063, "dur": 33, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025099, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025101, "dur": 34, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025139, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025141, "dur": 30, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025176, "dur": 30, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025208, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025209, "dur": 27, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025239, "dur": 154, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025397, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025399, "dur": 31, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025435, "dur": 27, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025465, "dur": 28, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025496, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025498, "dur": 33, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025534, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025536, "dur": 135, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025674, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025676, "dur": 43, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025722, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025725, "dur": 47, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025781, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025784, "dur": 42, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025828, "dur": 6, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025837, "dur": 88, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025927, "dur": 1, "ph": "X", "name": "ProcessMessages 1046", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025930, "dur": 48, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025981, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226025983, "dur": 43, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226026029, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226026031, "dur": 42, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226026077, "dur": 1, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226026079, "dur": 134, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226026216, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226026218, "dur": 32, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226026254, "dur": 1098, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027363, "dur": 58, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027435, "dur": 6, "ph": "X", "name": "ProcessMessages 8140", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027442, "dur": 29, "ph": "X", "name": "ReadAsync 8140", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027476, "dur": 28, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027507, "dur": 38, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027549, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027551, "dur": 38, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027591, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027593, "dur": 145, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027742, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027745, "dur": 35, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027785, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027788, "dur": 33, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027822, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027824, "dur": 35, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027863, "dur": 31, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027901, "dur": 35, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027940, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226027945, "dur": 51, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028000, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028003, "dur": 49, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028061, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028063, "dur": 38, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028104, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028107, "dur": 31, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028148, "dur": 1, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028150, "dur": 32, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028187, "dur": 32, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028221, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028224, "dur": 35, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028262, "dur": 30, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028295, "dur": 42, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028340, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028343, "dur": 38, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028388, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028390, "dur": 33, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028426, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028428, "dur": 33, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028463, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028465, "dur": 31, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028500, "dur": 33, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028538, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028540, "dur": 38, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028581, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028584, "dur": 57, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028645, "dur": 286, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028934, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028936, "dur": 32, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028977, "dur": 2, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226028980, "dur": 39, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029023, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029025, "dur": 33, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029068, "dur": 30, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029101, "dur": 1, "ph": "X", "name": "ProcessMessages 121", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029103, "dur": 45, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029151, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029152, "dur": 33, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029190, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029193, "dur": 36, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029232, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029234, "dur": 31, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029273, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029275, "dur": 155, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029434, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226029436, "dur": 611, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226030057, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226030059, "dur": 265, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226030332, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226030334, "dur": 644, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226030981, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226030983, "dur": 291, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226031278, "dur": 532, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226031813, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226031817, "dur": 278, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226032098, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226032101, "dur": 770, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226032874, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226032876, "dur": 38, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226032917, "dur": 268, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226033187, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226033189, "dur": 2347, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226035543, "dur": 2, "ph": "X", "name": "ProcessMessages 1553", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226035546, "dur": 430, "ph": "X", "name": "ReadAsync 1553", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226035979, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226035981, "dur": 36, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226036020, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226036024, "dur": 96, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226036123, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226036125, "dur": 711, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226036840, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226036842, "dur": 292, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226037137, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226037140, "dur": 851, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226037994, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226037999, "dur": 240, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226038242, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226038244, "dur": 560, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226038809, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226038811, "dur": 268, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226039083, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226039085, "dur": 885, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226039974, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226039976, "dur": 29, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226040009, "dur": 240, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226040252, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226040256, "dur": 750, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226041011, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226041013, "dur": 64, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226041081, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226041084, "dur": 537, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226041624, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226041626, "dur": 29, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226041661, "dur": 251, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226041915, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226041920, "dur": 461, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226042384, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226042386, "dur": 33, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226042423, "dur": 235, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226042661, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226042664, "dur": 591, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226043257, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226043259, "dur": 38, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226043300, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226043302, "dur": 231, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226043543, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226043544, "dur": 657, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226044204, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226044206, "dur": 266, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226044475, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226044477, "dur": 545, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226045026, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226045030, "dur": 29, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226045063, "dur": 299, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226045366, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226045368, "dur": 621, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226045992, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226045994, "dur": 31, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226046029, "dur": 241, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226046275, "dur": 683, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226046965, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226046967, "dur": 189, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226047160, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226047162, "dur": 707, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226047879, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226047881, "dur": 351, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226048235, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226048237, "dur": 836, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226049076, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226049078, "dur": 326, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226049407, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226049409, "dur": 831, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226050253, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226050255, "dur": 315, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226050573, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226050575, "dur": 606, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226051184, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226051186, "dur": 31, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226051222, "dur": 251, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226051478, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226051480, "dur": 654, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226052139, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226052142, "dur": 316, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226052461, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226052463, "dur": 3575, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226056049, "dur": 5, "ph": "X", "name": "ProcessMessages 4434", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226056055, "dur": 199, "ph": "X", "name": "ReadAsync 4434", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226056262, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226056264, "dur": 248, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226056516, "dur": 585, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226057104, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226057106, "dur": 261, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226057371, "dur": 761, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226058135, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226058136, "dur": 287, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226058428, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226058430, "dur": 568, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226059001, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226059011, "dur": 309, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226059324, "dur": 1465, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226060798, "dur": 2, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226060801, "dur": 573, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226061378, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226061381, "dur": 275, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226061659, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226061661, "dur": 946, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226062611, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226062614, "dur": 269, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226062904, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226062906, "dur": 677, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226063586, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226063588, "dur": 294, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226063898, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226063901, "dur": 448, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226064352, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226064355, "dur": 132, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226064490, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226064492, "dur": 386, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226064882, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226064884, "dur": 41, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226064929, "dur": 333, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226065265, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226065267, "dur": 362, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226065638, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226065640, "dur": 247, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226065890, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226065893, "dur": 2745, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226068646, "dur": 3, "ph": "X", "name": "ProcessMessages 2094", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226068651, "dur": 171, "ph": "X", "name": "ReadAsync 2094", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226068836, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226068840, "dur": 242, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226069086, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226069089, "dur": 1452, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226070548, "dur": 3, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226070552, "dur": 384, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226070940, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226070942, "dur": 272, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226071219, "dur": 1616, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226072849, "dur": 2, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226072853, "dur": 390, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226073250, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226073252, "dur": 345, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226073602, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226073608, "dur": 529, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226074142, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226074148, "dur": 304, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226074457, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226074459, "dur": 2095, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226076562, "dur": 4, "ph": "X", "name": "ProcessMessages 1685", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226076568, "dur": 508, "ph": "X", "name": "ReadAsync 1685", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226077081, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226077083, "dur": 395, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226077484, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226077487, "dur": 853, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226078345, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226078348, "dur": 396, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226078749, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226078751, "dur": 525, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079281, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079284, "dur": 277, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079565, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079568, "dur": 35, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079606, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079608, "dur": 32, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079644, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079646, "dur": 40, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079688, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079690, "dur": 35, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226079732, "dur": 736, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226080471, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226080473, "dur": 279, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226080756, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226080761, "dur": 749, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226081514, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226081517, "dur": 340, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226081862, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226081864, "dur": 513, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226082380, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226082382, "dur": 241, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226082627, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226082630, "dur": 852, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226083486, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226083488, "dur": 349, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226083841, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226083843, "dur": 992, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226084838, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226084840, "dur": 314, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226085164, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226085166, "dur": 60, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226085229, "dur": 2, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226085239, "dur": 47, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226085290, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226085292, "dur": 39, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226085334, "dur": 3, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226085338, "dur": 742, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226086084, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226086087, "dur": 741, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226086832, "dur": 422, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087259, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087323, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087327, "dur": 134, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087466, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087468, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087579, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087584, "dur": 87, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087686, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087688, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087749, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226087751, "dur": 260, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088016, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088019, "dur": 60, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088083, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088085, "dur": 320, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088409, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088412, "dur": 141, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088566, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088569, "dur": 187, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088761, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088764, "dur": 203, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088972, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226088975, "dur": 51, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089031, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089033, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089177, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089179, "dur": 104, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089287, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089289, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089443, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089446, "dur": 86, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089548, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089552, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089624, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089627, "dur": 68, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089700, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089702, "dur": 47, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089755, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226089801, "dur": 294, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090100, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090103, "dur": 80, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090187, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090190, "dur": 104, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090297, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090299, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090454, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090456, "dur": 257, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090718, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090720, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090830, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226090833, "dur": 220, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091063, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091066, "dur": 127, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091197, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091200, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091268, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091270, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091353, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091355, "dur": 539, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091899, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091902, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091980, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226091984, "dur": 226, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092215, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092217, "dur": 217, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092439, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092446, "dur": 371, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092822, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092825, "dur": 83, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092913, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092915, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092995, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226092998, "dur": 221, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093223, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093226, "dur": 92, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093323, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093325, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093425, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093428, "dur": 121, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093553, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093556, "dur": 122, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093691, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093694, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093763, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093766, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093908, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226093913, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094091, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094094, "dur": 54, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094153, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094155, "dur": 60, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094220, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094222, "dur": 217, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094444, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094447, "dur": 73, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094526, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094529, "dur": 78, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094611, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094614, "dur": 194, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094813, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094815, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094956, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226094959, "dur": 124, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095087, "dur": 38, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095130, "dur": 64, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095205, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095207, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095272, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095293, "dur": 95, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095393, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095396, "dur": 140, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095540, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095543, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095601, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095606, "dur": 78, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095689, "dur": 269, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095963, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226095966, "dur": 80, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096051, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096053, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096138, "dur": 272, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096414, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096417, "dur": 79, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096500, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096503, "dur": 48, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096556, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096558, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096653, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096655, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096745, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096748, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096808, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096810, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096878, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096881, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096944, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226096946, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097021, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097024, "dur": 135, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097163, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097166, "dur": 141, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097311, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097314, "dur": 65, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097383, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097386, "dur": 73, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097463, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097465, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097573, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097576, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097649, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097651, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097806, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226097811, "dur": 387, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098202, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098205, "dur": 186, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098395, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098398, "dur": 56, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098457, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098460, "dur": 219, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098683, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098686, "dur": 98, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098788, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098790, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098878, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226098880, "dur": 315, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226099200, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226099202, "dur": 8003, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226107227, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226107230, "dur": 360, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226107594, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226107596, "dur": 508, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226108107, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226108109, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226108375, "dur": 11, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226108389, "dur": 626, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226109022, "dur": 527, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226109554, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226109556, "dur": 3953, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226113522, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226113526, "dur": 24715, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226138250, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226138253, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226138392, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226138395, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226138448, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226138450, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226138625, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226138626, "dur": 3690, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226142322, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226142324, "dur": 9444, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226151774, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226151777, "dur": 282, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226152065, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226152067, "dur": 985, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226153056, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226153059, "dur": 969, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154033, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154036, "dur": 259, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154300, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154303, "dur": 73, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154380, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154382, "dur": 159, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154545, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154667, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154669, "dur": 161, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154834, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226154836, "dur": 312, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226155153, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226155155, "dur": 216, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226155374, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226155376, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226155587, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226155589, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226155744, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226155746, "dur": 203, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226155955, "dur": 316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226156275, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226156278, "dur": 228, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226156509, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226156512, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226156571, "dur": 396, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226156971, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226156974, "dur": 190, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226157168, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226157171, "dur": 162, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226157337, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226157339, "dur": 230, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226157572, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226157574, "dur": 510, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226158088, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226158091, "dur": 297, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226158392, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226158394, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226158543, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226158546, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226158731, "dur": 323, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159058, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159061, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159114, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159116, "dur": 135, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159262, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159267, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159476, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159479, "dur": 170, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159653, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226159658, "dur": 554, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160217, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160219, "dur": 240, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160463, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160465, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160547, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160552, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160694, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160695, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160934, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226160936, "dur": 112, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161051, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161053, "dur": 209, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161266, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161268, "dur": 280, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161552, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161554, "dur": 138, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161696, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161698, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161772, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161774, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161914, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226161916, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226162077, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226162079, "dur": 323, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226162406, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226162409, "dur": 195, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226162608, "dur": 865, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226163478, "dur": 151, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226163633, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226163637, "dur": 220, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226163861, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226163864, "dur": 462, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226164331, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226164334, "dur": 655, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226164999, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165002, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165155, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165157, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165399, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165401, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165472, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165474, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165576, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165578, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165756, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165758, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165892, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226165894, "dur": 242, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226166140, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226166143, "dur": 362, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226166509, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226166515, "dur": 247, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226166766, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226166768, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226166833, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226166840, "dur": 310, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167154, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167156, "dur": 365, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167525, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167527, "dur": 222, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167754, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167756, "dur": 93, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167853, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167874, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167953, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226167956, "dur": 95, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168054, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168057, "dur": 116, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168177, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168179, "dur": 92, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168275, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168277, "dur": 116, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168397, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168399, "dur": 234, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168637, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168639, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168700, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168702, "dur": 285, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226168993, "dur": 364, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226169362, "dur": 1107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226170475, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226170478, "dur": 250416, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226420904, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226420908, "dur": 79, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226420993, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226420995, "dur": 75, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226421076, "dur": 69, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226421150, "dur": 70, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226421235, "dur": 39, "ph": "X", "name": "ReadAsync 4138", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226421279, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226421317, "dur": 1970, "ph": "X", "name": "ProcessMessages 4842", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226423293, "dur": 3133, "ph": "X", "name": "ReadAsync 4842", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226426433, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226426436, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226426715, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226426718, "dur": 1780, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226428505, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226428508, "dur": 1712, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226430227, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226430231, "dur": 322, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226430559, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226430561, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226430644, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226430646, "dur": 1300, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226431953, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226431956, "dur": 397, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226432357, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226432360, "dur": 1117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226433483, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226433486, "dur": 177, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226433668, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226433670, "dur": 1497, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226435171, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226435174, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226435249, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226435251, "dur": 914, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226436170, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226436172, "dur": 835, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226437012, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226437014, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226437276, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226437278, "dur": 301, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226437587, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226437769, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226437771, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226437953, "dur": 3316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226441276, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226441280, "dur": 456, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226441741, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226441743, "dur": 1316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226443064, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226443066, "dur": 1155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226444227, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226444229, "dur": 752, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226444988, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226444990, "dur": 275, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226445270, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226445272, "dur": 2260, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226447550, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226447554, "dur": 1222, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226448782, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226448785, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226448924, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226448927, "dur": 830, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226449762, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226449764, "dur": 1407, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226451175, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226451178, "dur": 441, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226451624, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226451635, "dur": 156, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226451795, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226451798, "dur": 157, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452012, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452015, "dur": 68, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452086, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452089, "dur": 99, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452191, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452193, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452474, "dur": 9, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452484, "dur": 51, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452540, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452542, "dur": 106, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452653, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452656, "dur": 88, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452748, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452753, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452862, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452864, "dur": 71, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452939, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226452941, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453017, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453019, "dur": 76, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453098, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453101, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453186, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453261, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453263, "dur": 64, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453331, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453334, "dur": 142, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453484, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453486, "dur": 122, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453612, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453615, "dur": 115, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453733, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453736, "dur": 89, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453829, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453831, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453883, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453886, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226453933, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454010, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454011, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454282, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454285, "dur": 90, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454380, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454383, "dur": 58, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454444, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454446, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454550, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454553, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454720, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454722, "dur": 169, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454895, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418226454898, "dur": 2718302, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229173219, "dur": 47, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229173270, "dur": 9474, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229182750, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229182754, "dur": 677134, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229859897, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229859901, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229859970, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229859972, "dur": 78, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229860056, "dur": 83, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229860171, "dur": 75, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229860248, "dur": 35, "ph": "X", "name": "ProcessMessages 2629", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229860284, "dur": 5016, "ph": "X", "name": "ReadAsync 2629", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229865306, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229865309, "dur": 617, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229865931, "dur": 31, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229865964, "dur": 438, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229866407, "dur": 269, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418229866679, "dur": 6172, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 70122, "tid": 9, "ts": 1748418229902573, "dur": 3185, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 70122, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 70122, "tid": 8589934592, "ts": 1748418225955349, "dur": 133011, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 70122, "tid": 8589934592, "ts": 1748418226088363, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 70122, "tid": 8589934592, "ts": 1748418226088372, "dur": 4813, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 70122, "tid": 9, "ts": 1748418229905763, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 70122, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418225817210, "dur": 4057879, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418225919728, "dur": 26912, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418229875374, "dur": 7475, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418229878616, "dur": 2570, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418229882931, "dur": 17, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 70122, "tid": 9, "ts": 1748418229905771, "dur": 15, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748418225966489, "dur": 5851, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418225972379, "dur": 33730, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418226006209, "dur": 105, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748418226006315, "dur": 273, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418226006671, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1748418226007784, "dur": 186, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3C55960BA9CB8745.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418226008026, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B66FF45CB39FB62A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418226009316, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418226013077, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748418226013134, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748418226013384, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418226027750, "dur": 360, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748418226049982, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748418226077568, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748418226006602, "dur": 80134, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418226086748, "dur": 3781797, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418229868547, "dur": 58, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418229868789, "dur": 119, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418229868970, "dur": 965, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748418226006439, "dur": 80319, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226086793, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748418226087082, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226087611, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226087829, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_9909B9D5D2D6D40E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226088181, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226088442, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226088579, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226088700, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226088820, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226089017, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226089149, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226089226, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226089278, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226089404, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226089522, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226089639, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226089747, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226089816, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226090017, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226090116, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226090346, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226090799, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226091071, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226091175, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226091402, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748418226091694, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226091873, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226092186, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226092284, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226092383, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226092641, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226092713, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748418226092947, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226093039, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226093341, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226093626, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226093753, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226093830, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226094026, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226094208, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748418226094507, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226094677, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226094795, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226094891, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748418226095122, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226095205, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226095278, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226095678, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226095856, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226096085, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748418226096314, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226096586, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226097195, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226097371, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226097597, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226097698, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226097782, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226097932, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226097996, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226098100, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226098247, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226098410, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226098500, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226098701, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226098819, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418226099052, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226099164, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226099464, "dur": 632, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Weapons/WeaponData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748418226099464, "dur": 5091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226104556, "dur": 2253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226106813, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226108557, "dur": 1944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226110502, "dur": 2203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226112723, "dur": 2182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226114905, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226116010, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226117089, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226118196, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226119432, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226120720, "dur": 644, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_4_4.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748418226120596, "dur": 1703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226122299, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226123384, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226124536, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226125687, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226126846, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226128094, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226129294, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226130462, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226131643, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226132871, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226134081, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226135375, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226136544, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226137740, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226139002, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226139164, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226139264, "dur": 1677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226140941, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226142424, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226143730, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226144880, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226146067, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226147283, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226148602, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226149796, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226150946, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226152089, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226153198, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226153742, "dur": 1131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226154874, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226155339, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226155556, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226156146, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226156245, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226157268, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226157651, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226157754, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226158058, "dur": 2818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226160877, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226161195, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226161377, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226161583, "dur": 1826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226163410, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226163581, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226163642, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226164360, "dur": 1635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226165996, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226166189, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_BC6ADFF987BC82A3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226166254, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226166341, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226166552, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226166639, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226167399, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226167533, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226168570, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226168778, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418226168917, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226169294, "dur": 254636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226423935, "dur": 3060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226426996, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226427423, "dur": 3146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226430570, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226431129, "dur": 3533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226434664, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226434801, "dur": 3295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226438097, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226438297, "dur": 3098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226441396, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226441695, "dur": 3166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226444863, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226444979, "dur": 3319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226448304, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226448425, "dur": 3400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418226451826, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226452032, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226452449, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226452752, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226452941, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226453491, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226453573, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748418226453624, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226453751, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226453922, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Common.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748418226454082, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748418226454197, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226454301, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226454399, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748418226454526, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748418226454689, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226454843, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226455284, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418226455585, "dur": 3412964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226006448, "dur": 80344, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226086804, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418226087082, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226087487, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226087959, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226088053, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226088231, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226088392, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226088679, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226089018, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226089192, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226089286, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226089398, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226089538, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226089621, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226089723, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226089927, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226090027, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226090148, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418226090528, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226090803, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_F0DEE4EE0970DD2B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226090951, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226091056, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226091715, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226091886, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226091982, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418226092175, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226092301, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226092512, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226092885, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226093084, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226093336, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226093592, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226093699, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226093979, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226094183, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226094804, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226095065, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226095163, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226095223, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226095446, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226096132, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226096490, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226096771, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226097097, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226097337, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226097442, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226097721, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226097818, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226097964, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226098103, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226098165, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226098252, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226098416, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226098800, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226098920, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226099129, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226099261, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226099407, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418226099780, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226101339, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226102821, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226104403, "dur": 2308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226106711, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226108418, "dur": 2036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226110454, "dur": 2184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226112640, "dur": 2192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226114833, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226115964, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226117029, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226118104, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226119263, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226120778, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Ports/InvalidInputWidget.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748418226120465, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226122172, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226123269, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226124363, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226125482, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226126665, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226127992, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226129109, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226130263, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226131402, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226132653, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226133847, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226135057, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226136314, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226137552, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226138804, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226140357, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226141980, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226143270, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226144499, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226145676, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226146909, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226148193, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226149394, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226150608, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226151744, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226152946, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226153646, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226153735, "dur": 3153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418226156889, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226157604, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226157700, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226157994, "dur": 1950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418226159945, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226160284, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226160559, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226160740, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418226161637, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226161858, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418226162712, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226163246, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_3971D385965D884C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226163323, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226163409, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226163877, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226164016, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226164090, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226164254, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226164590, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418226164780, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226164841, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418226165829, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226166030, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418226167114, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226167360, "dur": 1097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418226168458, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226168727, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226168801, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418226169647, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226169943, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418226170971, "dur": 79, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418226171106, "dur": 3004094, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418229179384, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748418229178029, "dur": 5049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418229184486, "dur": 387, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418229862381, "dur": 418, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418229184914, "dur": 677925, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418229867731, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748418229867719, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748418229867866, "dur": 626, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748418226006519, "dur": 80283, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226086806, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226087421, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226087950, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226088240, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_CAA42EBCF9A9A6C9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226088346, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226088645, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226088731, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD8CC2F62B9395CF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226088805, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226089037, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226089162, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226089376, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226089519, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226089594, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226089711, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226089864, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226090011, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226090129, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226090290, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226090414, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226090708, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226090842, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_0118987859331F44.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226090947, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226091071, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226091137, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226091402, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226091738, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226092079, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_FB7A597403E5FDBB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226092134, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226092216, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226092303, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226092415, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748418226092560, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226092641, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226092918, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226092984, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226093566, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748418226093617, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226093730, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226094000, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226094084, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226094231, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226094447, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226094687, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226094966, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226095078, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226095183, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226095277, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226095439, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226096095, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226096306, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226096659, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226097133, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226097382, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226097623, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226097687, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226097748, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226097854, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226097975, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226098045, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226098158, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226098351, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226098527, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226098848, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226099060, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226099141, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418226099374, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226099531, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226100823, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226102293, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226103845, "dur": 2039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226105885, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226107740, "dur": 2083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226109823, "dur": 1628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226111452, "dur": 2683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226114135, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226115524, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226116640, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226117687, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226118822, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226120743, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/Converter/BuiltInToURP2DMaterialUpgrader.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748418226120045, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226121751, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226122873, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226124015, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226125111, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226126379, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226127694, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226128895, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226130046, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226131177, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226132446, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226133654, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226134907, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226136164, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226137386, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226138613, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226140060, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226141741, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226143104, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226144266, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226145414, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226146637, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226147907, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226149127, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226150381, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226151487, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226152613, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226153568, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226153971, "dur": 1068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226155039, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226155329, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226155391, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226155935, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226155996, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226156600, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226156747, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226156809, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226156972, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226157174, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226157892, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226157989, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226158242, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226158558, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226159271, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226159640, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226159840, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226159997, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226160111, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226161212, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226161720, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226162137, "dur": 1978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226164116, "dur": 921, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226165046, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Editor.ref.dll_5A2087A6EAF2CE17.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226165297, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226165388, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226165531, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226165777, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226166421, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226166716, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226166874, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226167769, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226167904, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226168127, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226168367, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226168431, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226168490, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_CBD4E358CB8CDF43.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418226168543, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226168646, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226168747, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226168833, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226169172, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226169309, "dur": 254628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226423941, "dur": 2941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226426883, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226427168, "dur": 3590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226430759, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226430916, "dur": 3490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226434407, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226434477, "dur": 3508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226437986, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226438506, "dur": 3450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226441957, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226442052, "dur": 3908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226445960, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226446119, "dur": 3482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226449602, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226449762, "dur": 4263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418226454026, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226454231, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226454345, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226454408, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748418226454473, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226454538, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748418226454606, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226454769, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226455072, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226455347, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418226455579, "dur": 3412175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418229867774, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748418229867757, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748418229867889, "dur": 581, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748418229868474, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226006519, "dur": 80292, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226086817, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226087396, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226087852, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226088116, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_888C5CF0B798DF8B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226088167, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226088254, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226088430, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226088592, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226088704, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AB6C0CE8644C7690.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226088807, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226088962, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_57AB718CCA0A791C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226089057, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226089143, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226089215, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226089477, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226089566, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226089641, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226089746, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226089836, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226090010, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226090117, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226090300, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418226090737, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226091042, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226091145, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748418226091666, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748418226091854, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226091921, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418226092305, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226092376, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418226092682, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226092749, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226092839, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418226093374, "dur": 10625, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226104000, "dur": 2175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226106175, "dur": 1806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226107981, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226109835, "dur": 1575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226111410, "dur": 2709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226114120, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226115481, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226116568, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226117649, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226118783, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226120735, "dur": 647, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShaderGraph/Targets/UniversalSpriteUnlitSubTarget.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748418226120034, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226121743, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226122881, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226124042, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226125147, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226126320, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226127670, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226128859, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226130021, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226131160, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226132423, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226133638, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226134803, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226136023, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226137230, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226138439, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226139835, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226141460, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226142899, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226144113, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226145266, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226146427, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226147666, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226148938, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226150165, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226151287, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226152390, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226153435, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226153748, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226154377, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226154939, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226155031, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226155449, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226155519, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226156095, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226156413, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226157162, "dur": 664, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226157833, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226158066, "dur": 1367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226159434, "dur": 825, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226160270, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226160582, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226161334, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226161475, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226162450, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226162780, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226163042, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226163548, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226163669, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226164101, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226164326, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226164426, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226164857, "dur": 1364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226166222, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226166406, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226167060, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226167196, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226168097, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226168460, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226168556, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226168646, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226168727, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226168805, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418226169422, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226169899, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226169974, "dur": 253971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226423952, "dur": 2850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226426804, "dur": 647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226427459, "dur": 3939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226431399, "dur": 1154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226432567, "dur": 3390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226435958, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226436065, "dur": 3111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226439177, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226439287, "dur": 3260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226442548, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226442696, "dur": 3262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226445959, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226446131, "dur": 3450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226449583, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226449706, "dur": 3482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418226453189, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226453278, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748418226453342, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226453664, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226453738, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748418226453789, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226453877, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226454050, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748418226454133, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226454227, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226454282, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748418226454345, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226454499, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226454610, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226454690, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226455035, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226455126, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226455210, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226455290, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226455552, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418226455700, "dur": 3412819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226006577, "dur": 80255, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226086836, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226087392, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226087951, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226088205, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226088262, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_C9DCA0B70AE22DB0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226088360, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226088660, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226088772, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226088997, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226089126, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226089194, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226089289, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226089453, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226089548, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226089657, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_51CCB6F491E5150D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226089795, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226089992, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226090083, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226090135, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226090261, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226090367, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226090731, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418226091039, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226091465, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226091578, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226091855, "dur": 15287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226107143, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226107796, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226107865, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226108251, "dur": 1948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226110199, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226112032, "dur": 2440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226114472, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226115707, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226116793, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226117896, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226119068, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226120754, "dur": 632, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.State/StateExitReason.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748418226120278, "dur": 1695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226121973, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226123073, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226124178, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226125306, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226126412, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226127698, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226128844, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226129982, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226131128, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226132372, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226133545, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226134742, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226136030, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226137246, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226138475, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226139915, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226141586, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226142943, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226144169, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226145353, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226146512, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226147793, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226149007, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226150221, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226151342, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226152476, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226153499, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226153759, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226154475, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226154999, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226155099, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226155700, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226155770, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226156255, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226157076, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226157664, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226157778, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226157952, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226158162, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226159241, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226159742, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226159911, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226160048, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226160255, "dur": 2917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226163173, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226163434, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226163619, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226163702, "dur": 1793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226165496, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226166151, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226166217, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226166522, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226167417, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226167743, "dur": 1356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226169100, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226169401, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418226169629, "dur": 254289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226423928, "dur": 3069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226426998, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226427364, "dur": 3513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226430878, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226431050, "dur": 3020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226434071, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226434198, "dur": 3192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226437391, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226437717, "dur": 3394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226441112, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226441524, "dur": 3638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226445163, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226445375, "dur": 3554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226448931, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226449065, "dur": 3587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418226452653, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226452938, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226453304, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226453589, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226453685, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226453801, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226453881, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226454018, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226454110, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418226454171, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226454232, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226454345, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226454418, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418226454481, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226454560, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418226454657, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226455030, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226455377, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226455567, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418226455728, "dur": 3412811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226006577, "dur": 80245, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226086829, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226087403, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226087840, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226088079, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226088378, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226088587, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_2EC518F8CEEB6CA9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226088696, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226088828, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226088982, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226089153, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226089237, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226089290, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226089365, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226089475, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226089562, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226089649, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226089782, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226089977, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226090098, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226090487, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226090626, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226090831, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_E711E874CC38399E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226090912, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226090975, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5041E5295CD553D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226091553, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226091955, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226092121, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226092199, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226092252, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226092330, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226092615, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226092694, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226092907, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226093076, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748418226093203, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226093365, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226093562, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226093658, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226093943, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226094099, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226094294, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748418226094622, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226094815, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226094915, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226095024, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748418226095235, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226095294, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226095693, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226095837, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226095953, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226096028, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226096084, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748418226096168, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226096515, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226096640, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226097165, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226097391, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226097525, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226097652, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226097720, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226097794, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226097956, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226098065, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226098188, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226098295, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226098436, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418226098724, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226098907, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226099059, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226099126, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226099277, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226099396, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226099556, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226100803, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226102179, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226103763, "dur": 2053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226105816, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226107700, "dur": 2011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226109711, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226111313, "dur": 2742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226114056, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226115433, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226116500, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226117551, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226118680, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226120713, "dur": 642, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShapeEditor/EditorTool/PathEditorToolExtensions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748418226119948, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226121671, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226122758, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226123846, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226124947, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226126062, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226127317, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226128537, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226129703, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226130873, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226132065, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226133224, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226134390, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226135704, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226136891, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226138083, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226139382, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226141004, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226142448, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226143775, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226144943, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226146118, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226147367, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226148687, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226149867, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226151107, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226152241, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226153381, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226153731, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226153789, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226154549, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226155070, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226155457, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226155536, "dur": 1537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226157074, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226157667, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226157936, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226158050, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226158765, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226158972, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226159177, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226159383, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226160032, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226160211, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226161003, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226161336, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226162136, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226162273, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226162470, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226162529, "dur": 1191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226163721, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226163959, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226164129, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226164189, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226164386, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226164570, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226164750, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226164825, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226165001, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226165061, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226165801, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226166059, "dur": 1329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226167389, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226167560, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226168334, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226168597, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Editor.ref.dll_AB52260E0554293F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226168649, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226168711, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418226168822, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226169352, "dur": 254614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226423972, "dur": 5225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226429198, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226429280, "dur": 3775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226433057, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226433153, "dur": 3230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226436384, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226436466, "dur": 3517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226439984, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226440143, "dur": 3637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226443781, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226443851, "dur": 3234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226447086, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226447162, "dur": 3345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226450508, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418226450590, "dur": 5010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418226455682, "dur": 3412843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226006591, "dur": 80249, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226086845, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226087408, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226087902, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226087992, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37EA32C02CB3EC76.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226088065, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226088189, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226088442, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226088646, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_E69ADDE2B61683F5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226088725, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226088985, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226089139, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226089219, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226089274, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226089400, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226089522, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226089618, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226089943, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226090039, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226090161, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226090240, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418226090614, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226090788, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226091265, "dur": 16856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226108122, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226108722, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226108838, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226109016, "dur": 5013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226114030, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226114128, "dur": 24452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226138581, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226138866, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226138933, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226139045, "dur": 3838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226142884, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226142963, "dur": 9191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226152155, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226152421, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226152490, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226152746, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226153628, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226153687, "dur": 2848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226156536, "dur": 723, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226157276, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226157656, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226158010, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226158741, "dur": 894, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226159649, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226159829, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418226160026, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226160121, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226160886, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226161182, "dur": 1546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748418226162797, "dur": 203, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226421610, "dur": 566, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226163347, "dur": 258882, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748418226423920, "dur": 3313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226427234, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226427537, "dur": 3639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226431177, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226431456, "dur": 3759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226435221, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226435283, "dur": 3329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226438613, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226438720, "dur": 3506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226442227, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226442316, "dur": 3611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226445928, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226446071, "dur": 4367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226450439, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226450536, "dur": 5059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418226455596, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418226455713, "dur": 3412814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226006603, "dur": 80244, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226086848, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226087387, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226087843, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226087998, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226088217, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226088279, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226088387, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226088631, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226088712, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226088807, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226089030, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226089136, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226089209, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226089288, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226089417, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226089526, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226089649, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226089776, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226089963, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226090097, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226090218, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226090577, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226090766, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226090918, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_58CF8A58239EF61C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226091021, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226091133, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226091385, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748418226091776, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226091916, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226092055, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748418226092397, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226092521, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418226092876, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226093007, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748418226093438, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226093615, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226093710, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226094014, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226094285, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418226094881, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226095023, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226095180, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226095309, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418226095766, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226096011, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226096159, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418226096286, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226096575, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418226097004, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748418226097272, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226097449, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226097704, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748418226097765, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226097902, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226098024, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226098201, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226098307, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226098496, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226098807, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226098931, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226099113, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226099247, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226099352, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226100677, "dur": 721, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/DataManager.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748418226099507, "dur": 4063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226103571, "dur": 1812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226105384, "dur": 1999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226107383, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226109324, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748418226109408, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226109706, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226110173, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226112119, "dur": 2368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226114487, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226115784, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226116848, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226117933, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226119091, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226120772, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Units/UnitWidget.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748418226120298, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226121992, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226123082, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226124201, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226125317, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226126469, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226127747, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226128909, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226130066, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226131202, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226132471, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226133749, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226134984, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226136224, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226137460, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226138759, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226140299, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226141917, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226143232, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226144379, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226145550, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226146799, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226148094, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226149309, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226150517, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226151584, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226152777, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226153678, "dur": 884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226154562, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226155212, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226155500, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226156016, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226156396, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226157158, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226157857, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226158189, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226158332, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226158551, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226159218, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226159816, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226160651, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226160769, "dur": 1178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226161948, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226162545, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_3F93312DF16ABE8A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226162617, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226162755, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226162940, "dur": 2207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226165148, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226165369, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226166615, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226166937, "dur": 1520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226168458, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226168689, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418226168813, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226168964, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226169066, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226169157, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226169294, "dur": 254661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226423961, "dur": 3176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226427138, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226427504, "dur": 3061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226430566, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226430926, "dur": 3176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226434103, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226434268, "dur": 3668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226437937, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226438031, "dur": 3448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226441480, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226441586, "dur": 3274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226444861, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226445004, "dur": 3100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226448105, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226448246, "dur": 3554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418226451801, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226451972, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226452035, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226452469, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226452682, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226452742, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748418226452970, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748418226453031, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226453267, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748418226453447, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226453576, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226453688, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226453831, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748418226453896, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226454017, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226454270, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748418226454384, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748418226454527, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226454750, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226455281, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418226455611, "dur": 3412951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418229873095, "dur": 1721, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 70122, "tid": 9, "ts": 1748418229906459, "dur": 2369, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 70122, "tid": 9, "ts": 1748418229909001, "dur": 2325, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 70122, "tid": 9, "ts": 1748418229898094, "dur": 14416, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}