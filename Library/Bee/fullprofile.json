{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 78866, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 78866, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 78866, "tid": 12, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 78866, "tid": 12, "ts": 1748506010398389, "dur": 1839, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 78866, "tid": 12, "ts": 1748506010409956, "dur": 2187, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 78866, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 78866, "tid": 1, "ts": 1748506009836306, "dur": 10751, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 78866, "tid": 1, "ts": 1748506009847062, "dur": 55401, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 78866, "tid": 1, "ts": 1748506009902473, "dur": 77957, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 78866, "tid": 12, "ts": 1748506010412152, "dur": 1847, "ph": "X", "name": "", "args": {}}, {"pid": 78866, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009833200, "dur": 21062, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009854266, "dur": 513761, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009855942, "dur": 8070, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009864019, "dur": 1622, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009865650, "dur": 18562, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009884221, "dur": 762, "ph": "X", "name": "ProcessMessages 6325", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885006, "dur": 111, "ph": "X", "name": "ReadAsync 6325", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885122, "dur": 7, "ph": "X", "name": "ProcessMessages 8187", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885142, "dur": 97, "ph": "X", "name": "ReadAsync 8187", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885244, "dur": 12, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885258, "dur": 71, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885344, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885348, "dur": 50, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885400, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885402, "dur": 90, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885498, "dur": 1, "ph": "X", "name": "ProcessMessages 1066", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885501, "dur": 44, "ph": "X", "name": "ReadAsync 1066", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885549, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885551, "dur": 46, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885617, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885619, "dur": 62, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885684, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885687, "dur": 55, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885747, "dur": 1, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885759, "dur": 68, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885840, "dur": 1, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885842, "dur": 48, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885893, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885895, "dur": 56, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885953, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009885955, "dur": 57, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886017, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886019, "dur": 59, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886089, "dur": 1, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886091, "dur": 44, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886159, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886163, "dur": 72, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886239, "dur": 2, "ph": "X", "name": "ProcessMessages 1239", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886256, "dur": 49, "ph": "X", "name": "ReadAsync 1239", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886313, "dur": 1, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886316, "dur": 48, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886366, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886369, "dur": 62, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886435, "dur": 2, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886438, "dur": 50, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886494, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886497, "dur": 49, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886550, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886556, "dur": 108, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886667, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886679, "dur": 40, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886723, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886740, "dur": 43, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886790, "dur": 1, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886793, "dur": 43, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886841, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009886844, "dur": 380, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009887231, "dur": 5, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009887238, "dur": 39, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009887282, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009887288, "dur": 280, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009887572, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009887577, "dur": 2706, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009890290, "dur": 9, "ph": "X", "name": "ProcessMessages 5569", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009890301, "dur": 793, "ph": "X", "name": "ReadAsync 5569", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009891100, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009891106, "dur": 84, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009891195, "dur": 5, "ph": "X", "name": "ProcessMessages 3249", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009891202, "dur": 56, "ph": "X", "name": "ReadAsync 3249", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009891262, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009891265, "dur": 729, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892002, "dur": 3, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892007, "dur": 46, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892066, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892069, "dur": 91, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892164, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892166, "dur": 112, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892282, "dur": 2, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892286, "dur": 59, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892351, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892354, "dur": 100, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892458, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892464, "dur": 464, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892932, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009892935, "dur": 205, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893144, "dur": 2, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893148, "dur": 98, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893264, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893268, "dur": 55, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893328, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893330, "dur": 559, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893894, "dur": 2, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893897, "dur": 55, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893956, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009893961, "dur": 68, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009894035, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009894038, "dur": 2254, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896298, "dur": 10, "ph": "X", "name": "ProcessMessages 8183", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896310, "dur": 47, "ph": "X", "name": "ReadAsync 8183", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896372, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896382, "dur": 53, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896439, "dur": 4, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896445, "dur": 44, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896492, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896495, "dur": 63, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896562, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896564, "dur": 58, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896625, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896628, "dur": 47, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896679, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896681, "dur": 51, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896750, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896755, "dur": 42, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896803, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896807, "dur": 64, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896882, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009896884, "dur": 123, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897011, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897013, "dur": 54, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897077, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897080, "dur": 50, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897136, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897138, "dur": 39, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897190, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897194, "dur": 340, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897538, "dur": 2, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897541, "dur": 49, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897594, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897596, "dur": 49, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897648, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897651, "dur": 53, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897707, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897710, "dur": 51, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897767, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897770, "dur": 73, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897847, "dur": 2, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897850, "dur": 58, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897911, "dur": 2, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897915, "dur": 61, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897981, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009897983, "dur": 61, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898048, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898051, "dur": 213, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898269, "dur": 2, "ph": "X", "name": "ProcessMessages 1359", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898272, "dur": 544, "ph": "X", "name": "ReadAsync 1359", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898818, "dur": 2, "ph": "X", "name": "ProcessMessages 1686", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898822, "dur": 77, "ph": "X", "name": "ReadAsync 1686", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898903, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898905, "dur": 86, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898996, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009898998, "dur": 69, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899076, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899078, "dur": 71, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899155, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899157, "dur": 65, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899231, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899235, "dur": 48, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899285, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899287, "dur": 241, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899534, "dur": 1, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899537, "dur": 41, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899581, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899584, "dur": 39, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899626, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899628, "dur": 229, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899861, "dur": 2, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899872, "dur": 76, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899953, "dur": 2, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009899956, "dur": 66, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009900026, "dur": 219, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009900248, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009900251, "dur": 33, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009900287, "dur": 32, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009900322, "dur": 29, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009900354, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009900356, "dur": 595, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009900957, "dur": 2, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009900961, "dur": 45, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901011, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901013, "dur": 44, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901059, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901061, "dur": 201, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901266, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901269, "dur": 42, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901315, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901317, "dur": 47, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901369, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901371, "dur": 50, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901425, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901428, "dur": 56, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901490, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901492, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901540, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901542, "dur": 42, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901587, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901589, "dur": 51, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901644, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901647, "dur": 54, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901704, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901707, "dur": 47, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901756, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901759, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901824, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901826, "dur": 143, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901972, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009901975, "dur": 75, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902053, "dur": 2, "ph": "X", "name": "ProcessMessages 1472", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902057, "dur": 57, "ph": "X", "name": "ReadAsync 1472", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902117, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902119, "dur": 56, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902178, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902181, "dur": 48, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902232, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902234, "dur": 37, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902275, "dur": 36, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902315, "dur": 6, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902325, "dur": 43, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902371, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902373, "dur": 39, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902414, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902416, "dur": 34, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902454, "dur": 163, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902621, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902624, "dur": 35, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902661, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902664, "dur": 40, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902707, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902709, "dur": 31, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902743, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902745, "dur": 65, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902814, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902816, "dur": 33, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902852, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902854, "dur": 41, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902899, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902901, "dur": 35, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902939, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902944, "dur": 32, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009902980, "dur": 41, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903024, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903026, "dur": 52, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903081, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903083, "dur": 35, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903121, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903122, "dur": 48, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903173, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903176, "dur": 37, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903216, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903218, "dur": 42, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903263, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903265, "dur": 39, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903315, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903318, "dur": 39, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903361, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903367, "dur": 37, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903406, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903415, "dur": 44, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903463, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903466, "dur": 49, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903518, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903521, "dur": 57, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903581, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903584, "dur": 332, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903920, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903922, "dur": 41, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903966, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009903968, "dur": 34, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904006, "dur": 30, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904039, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904040, "dur": 27, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904070, "dur": 1, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904072, "dur": 125, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904205, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904207, "dur": 35, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904244, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904246, "dur": 31, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904281, "dur": 56, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904341, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904343, "dur": 42, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904390, "dur": 35, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904429, "dur": 29, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904460, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904462, "dur": 70, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904535, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904537, "dur": 135, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904676, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904678, "dur": 34, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904716, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904718, "dur": 34, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904763, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904766, "dur": 45, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904814, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904817, "dur": 120, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904940, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904942, "dur": 34, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009904980, "dur": 330, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905315, "dur": 2, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905319, "dur": 126, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905450, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905453, "dur": 49, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905506, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905508, "dur": 55, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905566, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905569, "dur": 46, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905631, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905633, "dur": 64, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905702, "dur": 2, "ph": "X", "name": "ProcessMessages 1013", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905705, "dur": 42, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905750, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905752, "dur": 83, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905839, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905841, "dur": 92, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905937, "dur": 2, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905940, "dur": 47, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905989, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009905992, "dur": 50, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906049, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906052, "dur": 95, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906150, "dur": 2, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906153, "dur": 51, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906208, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906210, "dur": 47, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906261, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906264, "dur": 34, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906300, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906302, "dur": 36, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906341, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906343, "dur": 32, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906378, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906380, "dur": 36, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906419, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906421, "dur": 45, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906470, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906473, "dur": 55, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906530, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009906532, "dur": 1852, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009908405, "dur": 5, "ph": "X", "name": "ProcessMessages 1978", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009908500, "dur": 66, "ph": "X", "name": "ReadAsync 1978", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009908569, "dur": 8, "ph": "X", "name": "ProcessMessages 7495", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009908579, "dur": 46, "ph": "X", "name": "ReadAsync 7495", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009908627, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009908629, "dur": 513, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909291, "dur": 4, "ph": "X", "name": "ProcessMessages 2378", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909298, "dur": 166, "ph": "X", "name": "ReadAsync 2378", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909468, "dur": 2, "ph": "X", "name": "ProcessMessages 1314", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909471, "dur": 83, "ph": "X", "name": "ReadAsync 1314", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909558, "dur": 2, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909561, "dur": 81, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909646, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909648, "dur": 49, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909701, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909703, "dur": 45, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909751, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909753, "dur": 46, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909803, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909806, "dur": 48, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909856, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009909859, "dur": 188, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910051, "dur": 2, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910054, "dur": 76, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910135, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910139, "dur": 293, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910440, "dur": 3, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910445, "dur": 181, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910632, "dur": 2, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910636, "dur": 81, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910723, "dur": 2, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910728, "dur": 142, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910876, "dur": 2, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009910880, "dur": 150, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009911041, "dur": 4, "ph": "X", "name": "ProcessMessages 1389", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009911048, "dur": 433, "ph": "X", "name": "ReadAsync 1389", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009911490, "dur": 3, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009911496, "dur": 2599, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914105, "dur": 7, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914113, "dur": 169, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914285, "dur": 2, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914289, "dur": 399, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914691, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914726, "dur": 49, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914778, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914781, "dur": 46, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914830, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914832, "dur": 106, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914943, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914946, "dur": 46, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914995, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009914998, "dur": 48, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009915050, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009915052, "dur": 63, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009915120, "dur": 8, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009915131, "dur": 202, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009915347, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009915349, "dur": 860, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009916226, "dur": 13, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009916242, "dur": 285, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009916532, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009916534, "dur": 633, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009917171, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009917174, "dur": 353, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009917531, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009917538, "dur": 955, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009918496, "dur": 4, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009918502, "dur": 210, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009918716, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009918717, "dur": 791, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009919513, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009919516, "dur": 322, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009919842, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009919847, "dur": 740, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009920592, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009920594, "dur": 301, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009920899, "dur": 693, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009921594, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009921595, "dur": 38, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009921642, "dur": 643, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009922289, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009922292, "dur": 2788, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009925087, "dur": 3, "ph": "X", "name": "ProcessMessages 1508", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009925092, "dur": 250, "ph": "X", "name": "ReadAsync 1508", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009925344, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009925346, "dur": 287, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009925637, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009925639, "dur": 622, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009926265, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009926268, "dur": 311, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009926584, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009926586, "dur": 1015, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009927606, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009927609, "dur": 284, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009927897, "dur": 18, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009927917, "dur": 548, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009928469, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009928471, "dur": 265, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009928740, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009928742, "dur": 541, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009929287, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009929289, "dur": 288, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009929581, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009929583, "dur": 504, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009930091, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009930096, "dur": 311, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009930412, "dur": 7, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009930422, "dur": 762, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009931190, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009931192, "dur": 78, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009931274, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009931276, "dur": 693, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009931973, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009931977, "dur": 264, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009932244, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009932246, "dur": 661, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009932910, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009932914, "dur": 47, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009932965, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009932970, "dur": 280, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009933253, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009933256, "dur": 696, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009933955, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009933957, "dur": 249, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009934211, "dur": 576, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009934798, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009934801, "dur": 312, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009935117, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009935120, "dur": 723, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009935846, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009935848, "dur": 296, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009936148, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009936149, "dur": 782, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009936935, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009936937, "dur": 1176, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009938122, "dur": 2, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009938125, "dur": 192, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009938322, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009938324, "dur": 888, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009939218, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009939220, "dur": 352, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009939577, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009939580, "dur": 715, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009940298, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009940300, "dur": 319, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009940624, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009940626, "dur": 678, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009941308, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009941311, "dur": 40, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009941355, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009941356, "dur": 340, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009941716, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009941718, "dur": 620, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009942342, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009942344, "dur": 249, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009942599, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009942602, "dur": 45, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009942650, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009942653, "dur": 47, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009942703, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009942704, "dur": 40, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009942751, "dur": 1048, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009943809, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009943812, "dur": 246, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009944063, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009944066, "dur": 603, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009944673, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009944676, "dur": 2641, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009947326, "dur": 4, "ph": "X", "name": "ProcessMessages 2051", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009947331, "dur": 436, "ph": "X", "name": "ReadAsync 2051", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009947771, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009947773, "dur": 299, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009948076, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009948078, "dur": 744, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009948826, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009948829, "dur": 304, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009949136, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009949138, "dur": 726, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009949869, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009949872, "dur": 295, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009950171, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009950173, "dur": 771, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009950997, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009951002, "dur": 277, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009951283, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009951285, "dur": 1410, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009952703, "dur": 2, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009952706, "dur": 40, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009952765, "dur": 43, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009952818, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009952823, "dur": 92, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009952920, "dur": 372, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009953296, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009953298, "dur": 33, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009953335, "dur": 303, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009953642, "dur": 688, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009954334, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009954337, "dur": 230, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009954571, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009954574, "dur": 625, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009955203, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009955205, "dur": 325, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009955534, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009955535, "dur": 558, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956097, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956099, "dur": 36, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956137, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956138, "dur": 28, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956170, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956173, "dur": 197, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956373, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956376, "dur": 562, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956948, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009956951, "dur": 310, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009957269, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009957271, "dur": 499, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009957774, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009957777, "dur": 54, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009957837, "dur": 243, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009958082, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009958084, "dur": 2231, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009960324, "dur": 3, "ph": "X", "name": "ProcessMessages 1673", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009960328, "dur": 306, "ph": "X", "name": "ReadAsync 1673", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009960636, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009960638, "dur": 323, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009960965, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009960967, "dur": 516, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009961487, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009961490, "dur": 751, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009962245, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009962247, "dur": 193, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009962445, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009962447, "dur": 150, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009962600, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009962604, "dur": 1809, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009964421, "dur": 3, "ph": "X", "name": "ProcessMessages 1469", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009964425, "dur": 165, "ph": "X", "name": "ReadAsync 1469", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009964596, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009964598, "dur": 682, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009965285, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009965287, "dur": 266, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009965557, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009965559, "dur": 627, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009966191, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009966193, "dur": 2153, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009968353, "dur": 4, "ph": "X", "name": "ProcessMessages 2583", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009968358, "dur": 112, "ph": "X", "name": "ReadAsync 2583", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009968473, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009968474, "dur": 331, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009968810, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009968812, "dur": 537, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009969354, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009969356, "dur": 249, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009969610, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009969612, "dur": 976, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009970592, "dur": 4, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009970597, "dur": 140, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009970741, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009970743, "dur": 942, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009971689, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009971692, "dur": 233, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009971928, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009971933, "dur": 36, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009971974, "dur": 35, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009972013, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009972015, "dur": 30, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009972048, "dur": 761, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009972813, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009972815, "dur": 471, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009973302, "dur": 397, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009973706, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009973757, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009973759, "dur": 102, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009973866, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009973869, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009973933, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009973935, "dur": 57, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009973999, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974000, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974035, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974038, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974106, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974109, "dur": 66, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974179, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974181, "dur": 115, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974299, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974302, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974368, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974370, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974423, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974425, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974508, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974605, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974607, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974675, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974677, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974763, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974765, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974843, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974845, "dur": 58, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974906, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974909, "dur": 48, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974961, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009974966, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975008, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975102, "dur": 28, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975133, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975225, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975227, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975346, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975348, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975476, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975479, "dur": 91, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975575, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975578, "dur": 64, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975656, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975659, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975726, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975729, "dur": 58, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975790, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975793, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975860, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975863, "dur": 83, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975950, "dur": 7, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009975959, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976045, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976047, "dur": 129, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976181, "dur": 118, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976303, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976307, "dur": 326, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976638, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976641, "dur": 153, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976799, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976802, "dur": 96, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976902, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009976905, "dur": 192, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977102, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977105, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977216, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977218, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977328, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977331, "dur": 179, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977515, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977518, "dur": 82, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977604, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977607, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977719, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977744, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977846, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977851, "dur": 104, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977968, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009977971, "dur": 93, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978069, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978072, "dur": 80, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978155, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978157, "dur": 113, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978274, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978277, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978348, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978350, "dur": 289, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978642, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978646, "dur": 86, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978735, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978738, "dur": 52, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978793, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978795, "dur": 133, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978932, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009978935, "dur": 72, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979011, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979015, "dur": 75, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979093, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979095, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979157, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979159, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979219, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979221, "dur": 77, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979303, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979424, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979427, "dur": 66, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979497, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979500, "dur": 63, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979568, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979570, "dur": 50, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979624, "dur": 5, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979630, "dur": 109, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979752, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979755, "dur": 144, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979903, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009979905, "dur": 245, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980154, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980158, "dur": 66, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980227, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980241, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980305, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980308, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980367, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980379, "dur": 97, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980480, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980483, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980582, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980585, "dur": 94, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980683, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980686, "dur": 80, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980791, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980798, "dur": 73, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980875, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980878, "dur": 57, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980940, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009980942, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981039, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981042, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981105, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981108, "dur": 127, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981238, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981241, "dur": 108, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981369, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981373, "dur": 144, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981522, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981525, "dur": 70, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981599, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981601, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009981650, "dur": 17239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009998898, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009998902, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009999087, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506009999089, "dur": 1337, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010000428, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010000430, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010000607, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010000705, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010000708, "dur": 274, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010000985, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010000987, "dur": 2159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010003150, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010003152, "dur": 26957, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010030119, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010030124, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010030187, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010030193, "dur": 198, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010030394, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010030405, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010030555, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010030557, "dur": 3240, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010033804, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010033808, "dur": 9070, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010042885, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010042888, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010043006, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010043009, "dur": 83, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010043097, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010043099, "dur": 862, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010043966, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010043968, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044054, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044056, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044151, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044154, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044286, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044290, "dur": 108, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044401, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044404, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044559, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044561, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044639, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044641, "dur": 330, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044976, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010044978, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010045052, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010045055, "dur": 555, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010045614, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010045617, "dur": 212, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010045832, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010045837, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046004, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046006, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046065, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046067, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046156, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046158, "dur": 58, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046222, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046344, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046346, "dur": 507, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046858, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046860, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046915, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010046917, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047100, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047102, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047210, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047212, "dur": 55, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047271, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047273, "dur": 70, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047391, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047394, "dur": 346, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047745, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047747, "dur": 207, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047957, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010047962, "dur": 345, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010048312, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010048314, "dur": 352, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010048669, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010048671, "dur": 138, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010048813, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010048815, "dur": 186, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010049006, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010049008, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010049115, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010049272, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010049274, "dur": 409, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010049686, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010049688, "dur": 411, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010050103, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010050106, "dur": 182, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010050294, "dur": 382, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010050679, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010050683, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010050813, "dur": 790, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010051607, "dur": 74, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010051686, "dur": 4, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010051691, "dur": 413, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010052108, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010052111, "dur": 178, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010052294, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010052296, "dur": 395, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010052695, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010052697, "dur": 260, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010052972, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010052975, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053126, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053129, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053239, "dur": 152, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053394, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053396, "dur": 191, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053592, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053594, "dur": 264, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053862, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053866, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053934, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010053939, "dur": 325, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010054276, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010054280, "dur": 393, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010054678, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010054680, "dur": 212, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010054896, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010054898, "dur": 54, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010054956, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010054959, "dur": 82, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055045, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055047, "dur": 118, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055169, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055172, "dur": 134, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055310, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055312, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055381, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055383, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055467, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055470, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055571, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055574, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055619, "dur": 156, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055785, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055787, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055996, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010055999, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010056091, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010056121, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010056123, "dur": 435, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010056564, "dur": 259657, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010316232, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010316237, "dur": 61, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010316303, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010316306, "dur": 61, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010316372, "dur": 66, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010316444, "dur": 89, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010316548, "dur": 52, "ph": "X", "name": "ReadAsync 4142", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010316604, "dur": 113, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010316720, "dur": 1490, "ph": "X", "name": "ProcessMessages 4838", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010318213, "dur": 5115, "ph": "X", "name": "ReadAsync 4838", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010323336, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010323341, "dur": 3035, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010326383, "dur": 13, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010326401, "dur": 454, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010326858, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010326861, "dur": 220, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010327086, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010327089, "dur": 545, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010327640, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010327643, "dur": 1371, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010329020, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010329023, "dur": 2427, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010331471, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010331475, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010331577, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010331580, "dur": 1309, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010332894, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010332897, "dur": 749, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010333651, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010333658, "dur": 680, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010334341, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010334344, "dur": 690, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010335038, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010335040, "dur": 537, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010335583, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010335588, "dur": 612, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010336205, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010336207, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010336332, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010336334, "dur": 2826, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010339169, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010339173, "dur": 1517, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010340699, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010340702, "dur": 1743, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010342453, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010342457, "dur": 336, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010342798, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010342800, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010342868, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010342870, "dur": 575, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010343451, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010343454, "dur": 1480, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010344942, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010344946, "dur": 1654, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010346608, "dur": 9, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010346619, "dur": 670, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010347295, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010347297, "dur": 2036, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010349341, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010349346, "dur": 473, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010349823, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010349827, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010349909, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010349912, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010349976, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010349978, "dur": 197, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350181, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350184, "dur": 87, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350276, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350279, "dur": 90, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350374, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350376, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350473, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350475, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350622, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350625, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350769, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350772, "dur": 127, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350904, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010350906, "dur": 231, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351142, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351144, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351335, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351337, "dur": 156, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351500, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351502, "dur": 163, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351670, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351672, "dur": 179, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351854, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351856, "dur": 86, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351953, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010351956, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352023, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352025, "dur": 173, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352201, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352204, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352278, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352281, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352379, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352381, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352459, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352461, "dur": 175, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352640, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352643, "dur": 71, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352718, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352720, "dur": 242, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352966, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010352968, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353114, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353116, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353218, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353221, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353296, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353298, "dur": 222, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353525, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353528, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353607, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353609, "dur": 55, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353669, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353671, "dur": 232, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353908, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010353910, "dur": 944, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010354863, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010354866, "dur": 180, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355052, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355055, "dur": 166, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355225, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355228, "dur": 103, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355335, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355338, "dur": 187, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355529, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355531, "dur": 47, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355582, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355697, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355699, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010355750, "dur": 314, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010356069, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010356071, "dur": 254, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010356328, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010356342, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010356403, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010356405, "dur": 717, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010357128, "dur": 535, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 78866, "tid": 12884901888, "ts": 1748506010357669, "dur": 10295, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 78866, "tid": 12, "ts": 1748506010414003, "dur": 5214, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 78866, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 78866, "tid": 8589934592, "ts": 1748506009829390, "dur": 151073, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 78866, "tid": 8589934592, "ts": 1748506009980466, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 78866, "tid": 8589934592, "ts": 1748506009980475, "dur": 4502, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 78866, "tid": 12, "ts": 1748506010419220, "dur": 14, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 78866, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 78866, "tid": 4294967296, "ts": 1748506009682337, "dur": 688101, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 78866, "tid": 4294967296, "ts": 1748506009791921, "dur": 28589, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 78866, "tid": 4294967296, "ts": 1748506010370790, "dur": 12697, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 78866, "tid": 4294967296, "ts": 1748506010374894, "dur": 6617, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 78866, "tid": 4294967296, "ts": 1748506010383576, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 78866, "tid": 12, "ts": 1748506010419236, "dur": 78, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748506009840699, "dur": 7051, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748506009847788, "dur": 35214, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748506009883112, "dur": 97, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748506009883210, "dur": 182, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748506009884757, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009885050, "dur": 171, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009888775, "dur": 873, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009890401, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009890494, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748506009891939, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_A298F0F0964CDA5C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009892144, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009893090, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748506009893284, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748506009893795, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_E9EA3AB45900FF1D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009894716, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_DD9C61FBB7D63C03.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009896120, "dur": 263, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009898728, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_E30EE1A78470D274.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009898924, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748506009899740, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009899982, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748506009905162, "dur": 152, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009905408, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_8E9298DC6B12ED57.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009906826, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748506009907778, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009908166, "dur": 348, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009909212, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009909320, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009909425, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748506009909984, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009910083, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748506009910236, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009911673, "dur": 134, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_AD2FFB7646FD92B4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748506009911915, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748506009912603, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748506009912902, "dur": 1273, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748506009914632, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009917123, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748506009917455, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009922198, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009937944, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748506009938013, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748506009939123, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748506009939556, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009947131, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748506009971566, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748506009883400, "dur": 89444, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748506009972859, "dur": 383655, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748506010356567, "dur": 71, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748506010356760, "dur": 96, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748506010356862, "dur": 183, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748506010357109, "dur": 1746, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748506009883313, "dur": 89562, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009972913, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748506009973333, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_9909B9D5D2D6D40E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009973524, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009973631, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1996541149DA13E0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009973749, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_97FEC325F9337F2C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009973888, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009974199, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009974368, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009974504, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009974614, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009974754, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009974864, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009974976, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009975092, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009975154, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009975323, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009975511, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009975666, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009975916, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009976141, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009976324, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009976660, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506009976740, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748506009976984, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009977191, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009977371, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009977459, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009977755, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009977853, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009978362, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009978565, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009978662, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748506009979003, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009979117, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009979193, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009979274, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009979423, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009979666, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009979736, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009980238, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009980316, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009980437, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009980573, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009980721, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009981151, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748506009981390, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009981541, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009981602, "dur": 2179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009983782, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009985110, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009986150, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009987328, "dur": 1956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009989285, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009990767, "dur": 1928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009992696, "dur": 2210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009994907, "dur": 2203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009997111, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506009998950, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010000608, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010001779, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010002892, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010004810, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.State/Units/StateGraphContainerType.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748506010003961, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010005649, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010006758, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010008022, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010009288, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010010775, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010011976, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010013154, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010014454, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010015785, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010017002, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010018279, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010019509, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010020674, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010021993, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010023590, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010025005, "dur": 1569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010026575, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010028010, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010029352, "dur": 1583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010030936, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010032270, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010033752, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010035094, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010036523, "dur": 1681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010038205, "dur": 1701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010039907, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010041226, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010042042, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010042169, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010042811, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506010043139, "dur": 1265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010044405, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010044840, "dur": 993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010045833, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010046172, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506010046344, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010047175, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010047378, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506010047609, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506010047805, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506010047996, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010049001, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010049111, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010049976, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010050103, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010051297, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010051405, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010051463, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506010051699, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506010051926, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506010052106, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748506010052330, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010052995, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010053116, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010053778, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010053858, "dur": 1678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010055537, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010055672, "dur": 262762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010318436, "dur": 3581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010322145, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010322491, "dur": 3304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010325796, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010325974, "dur": 4143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010330118, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010330226, "dur": 3975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010334256, "dur": 5340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010339597, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010339792, "dur": 4218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010344011, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010344093, "dur": 4492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010348622, "dur": 7606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010356230, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748506010356292, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748506010356363, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009883322, "dur": 89581, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009972914, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748506009973323, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009973572, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009973647, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009974012, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009974229, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD8CC2F62B9395CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009974337, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009974411, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009974504, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009974599, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009974767, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009974868, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009975332, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009975440, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009975609, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506009975679, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009975767, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748506009976040, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748506009976284, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009976344, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748506009976733, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748506009976825, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748506009977134, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009977278, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748506009977571, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009977690, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009977793, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009977934, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009978069, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009978155, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748506009978453, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009978595, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748506009978808, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748506009979201, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009979266, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009979420, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009979521, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748506009979865, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748506009980224, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009980291, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009980418, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009980560, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009980671, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009980741, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009980824, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009981200, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748506009981368, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748506009981494, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009981928, "dur": 827, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/InputManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748506009983254, "dur": 749, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/DataManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748506009981557, "dur": 4088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009985645, "dur": 1542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009987188, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009989152, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009990747, "dur": 1975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009992723, "dur": 2271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009994994, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009997139, "dur": 1822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506009998961, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010000620, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010000713, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010001821, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010002938, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010004796, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.State/NesterStateTransition.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748506010003998, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010005707, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010006777, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010008033, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010009268, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010010760, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010011958, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010013139, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010014431, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010015798, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010017008, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010018334, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010019564, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010020746, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010022129, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010023681, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010025135, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010026700, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010028165, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010029487, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010031052, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010032355, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010033870, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010035182, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010036695, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010038379, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010040040, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010041357, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010041840, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010041995, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010042171, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010042939, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010043269, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010043870, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010044277, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010044604, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010044665, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010044930, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010045013, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010045246, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010045853, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010046085, "dur": 953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010047038, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010047635, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010047825, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010048758, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010049053, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010050101, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010050214, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010050736, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010051236, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010051511, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010051723, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010051907, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010052113, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748506010052292, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010052355, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010052988, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010053134, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010054010, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010054102, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010055214, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010055530, "dur": 262897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010318435, "dur": 3270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010321707, "dur": 1566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010323281, "dur": 3563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010326845, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010327162, "dur": 4320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010331483, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010331573, "dur": 3417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010334991, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010335247, "dur": 8050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010343298, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010343382, "dur": 3725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010347109, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010347232, "dur": 8261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748506010355540, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748506010355630, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009883335, "dur": 89581, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009972923, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009973520, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009973638, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009974233, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009974335, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009974418, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009974481, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009974598, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009974669, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009974874, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009974959, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009975024, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009975335, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009975444, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009975635, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009975978, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748506009976321, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748506009976637, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506009976809, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748506009977145, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009977249, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748506009977534, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009977642, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009977725, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009977844, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009977979, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009978064, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009978311, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748506009978545, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009978621, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009978844, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748506009979144, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009979216, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009979305, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009979449, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009979794, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748506009980244, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009980336, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009980451, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009980584, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009980657, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748506009980712, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009980823, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009980977, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009981073, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748506009981143, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748506009981383, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009981472, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009981585, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009982692, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Trigger/System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748506009981684, "dur": 2539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009984223, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009985475, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009986747, "dur": 1949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009988697, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009990261, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009991616, "dur": 2169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009993785, "dur": 2163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009995949, "dur": 2191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009998141, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506009999906, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010001112, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010002273, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010003371, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010004770, "dur": 685, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Ports/ControlInputWidget.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748506010004401, "dur": 1757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010006159, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010007223, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010008480, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010009780, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010011137, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010012331, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010013535, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010014865, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010016102, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010017312, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010018681, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010019915, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010021104, "dur": 1553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010022657, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010024084, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010025481, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010027027, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010028485, "dur": 1739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010030268, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010030401, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010030513, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010031834, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010033400, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010034749, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010036052, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010037857, "dur": 1685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010039543, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010040870, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010042164, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010042809, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506010043007, "dur": 2563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010045571, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010046099, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506010046355, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010048717, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010048843, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506010048988, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010049828, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010050135, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010050975, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010051135, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010051198, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506010051547, "dur": 1659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010053249, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010053946, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010054037, "dur": 1922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010056006, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748506010056178, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010056585, "dur": 261871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010318466, "dur": 3524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010321991, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010322199, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010322413, "dur": 6278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010328692, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010328800, "dur": 4771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010333616, "dur": 3586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010337203, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010337321, "dur": 5326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010342649, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010342729, "dur": 4191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010346921, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010347022, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010347095, "dur": 6139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748506010353235, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010353349, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010353497, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010353560, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010353625, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748506010353695, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748506010353801, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010354052, "dur": 909, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010355023, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010355083, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010355205, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010355324, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010355402, "dur": 961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010356368, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748506010356436, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506009883348, "dur": 89579, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506009972933, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009973529, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506009973601, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009973751, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_A80C913DF2042397.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009974163, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009974227, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009974390, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009974498, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506009974570, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009974712, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506009974788, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009974933, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009975025, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506009975128, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009975384, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506009975538, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748506009975908, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506009976064, "dur": 23438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748506009999503, "dur": 932, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010000445, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506010000528, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010000607, "dur": 2491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506010003099, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010003157, "dur": 26112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010029271, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010029741, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010030052, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506010030119, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010030221, "dur": 3445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506010033668, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010033757, "dur": 8457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010042216, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010042794, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506010042969, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010043836, "dur": 810, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010044693, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506010045067, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010045916, "dur": 929, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010046874, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748506010047144, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010047978, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010048392, "dur": 1331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748506010049766, "dur": 417, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010316142, "dur": 575, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010050899, "dur": 265889, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748506010318394, "dur": 4664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010323061, "dur": 1166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010324240, "dur": 4121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010328363, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010328451, "dur": 4381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010332833, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010332909, "dur": 3321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010336231, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010336335, "dur": 6012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010342355, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010342422, "dur": 7129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748506010349552, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010349701, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010350096, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010350329, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010350459, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010350565, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010350677, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010350923, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010350987, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010351227, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010351375, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010351534, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010351637, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748506010351740, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010351807, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010352148, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748506010352237, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010352445, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010352605, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010352784, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010352870, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010353155, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010353346, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010353482, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748506010353571, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010353643, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010353704, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748506010353807, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010353990, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748506010354134, "dur": 779, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010354944, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748506010355027, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010355189, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010355287, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748506010355613, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009883359, "dur": 89579, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009972944, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009973554, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009973615, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_25D10CC8709418E4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009973766, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009973900, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009974225, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009974300, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_57AB718CCA0A791C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009974460, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009974610, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009974697, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009974820, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009974938, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_51CCB6F491E5150D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009975023, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009975177, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009975342, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009975466, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748506009975847, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5041E5295CD553D4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009975958, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748506009976277, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009976592, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_FB7A597403E5FDBB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506009976684, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009977050, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009977138, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748506009977333, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009977468, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009977712, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009977839, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009978017, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009978074, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009978530, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009978850, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009979187, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009979257, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009979411, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009979499, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009979641, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009980148, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009980237, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009980321, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009980420, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009980594, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009980715, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009980804, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009981001, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009981276, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748506009981383, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009981497, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009981573, "dur": 1811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009983385, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009984864, "dur": 7434, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009992298, "dur": 2184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009994483, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009996747, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506009998544, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010000295, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010001464, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010002608, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010003671, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010004791, "dur": 685, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_0_0.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748506010004726, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010006491, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010007716, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010008902, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010010353, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010011622, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010012748, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010014041, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010015312, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010016618, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010017795, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010019165, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010020304, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010021521, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010023113, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010024516, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010025985, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010027461, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010028911, "dur": 1574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010030490, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010031645, "dur": 1577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010033222, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010034534, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010035834, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010037594, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010039279, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010040715, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010041967, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010042166, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010042837, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010043037, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010043107, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010043796, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010044289, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010044529, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010044595, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010044895, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010045079, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010045160, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010045777, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010046190, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010046320, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010046383, "dur": 1369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010047757, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010048324, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010048403, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010049275, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010049368, "dur": 1262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010050630, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010051253, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010051473, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010051677, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010051882, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010052096, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748506010052317, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010053164, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010053519, "dur": 1185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010054705, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010055137, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010056113, "dur": 262335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010318450, "dur": 4598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010323089, "dur": 3636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010326726, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010327079, "dur": 4405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010331485, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010331559, "dur": 3534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010335094, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010335421, "dur": 5620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010341042, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010341102, "dur": 3427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010344531, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010344615, "dur": 4554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748506010349170, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010349318, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010349922, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010350002, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010350132, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010350260, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010350365, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748506010350435, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010350650, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010350853, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010351160, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010351282, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748506010351346, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010351645, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748506010351700, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010351798, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010351887, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010351991, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010352183, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010352363, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010352669, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010352788, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010353006, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010353240, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010353369, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010353553, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010353658, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010353757, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010353870, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010353967, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748506010354151, "dur": 819, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010355108, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010355208, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010355339, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748506010355794, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009883373, "dur": 89574, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009972952, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009973517, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009973594, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009973959, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009974222, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009974292, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B92AE864B27146B1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009974432, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009974520, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009974728, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009974885, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009974958, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009975015, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009975223, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009975364, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009975535, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009975603, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009975751, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748506009976104, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009976295, "dur": 21345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748506009997642, "dur": 1191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009998849, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506009998924, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506009999072, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010000634, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010000976, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010002167, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010003230, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010004801, "dur": 680, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Units/UnitEditor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748506010004246, "dur": 1772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010006018, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010007048, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010008323, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010009581, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010010984, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010012187, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010013321, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010014601, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010015909, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010017159, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010018528, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010019757, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010020889, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010022520, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010023938, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010025325, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010026854, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010028330, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010029662, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010031207, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010032566, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010034111, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010035451, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010037070, "dur": 1671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010038742, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010040293, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010041573, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010041951, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010042068, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010042170, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010042805, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506010043059, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010043904, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010044429, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506010044779, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506010044993, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506010045074, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010045844, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010046117, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506010046405, "dur": 2465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010048871, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010049216, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506010049453, "dur": 1260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010050713, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010050936, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506010051424, "dur": 1069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010052494, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010052607, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748506010052787, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010053598, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010053939, "dur": 1018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010054957, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010055203, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010055371, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010055459, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010055552, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010055619, "dur": 262851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010318472, "dur": 6541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010325015, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010325117, "dur": 4788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010329907, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010329975, "dur": 3610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010333586, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010333668, "dur": 3483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010337152, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010337261, "dur": 5843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010343105, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010343174, "dur": 4407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010347582, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748506010347662, "dur": 8323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748506010356095, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009883385, "dur": 89571, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009972962, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009973514, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009973635, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_710F5443A18E10C1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009973748, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37EA32C02CB3EC76.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009973993, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009974159, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009974213, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AB6C0CE8644C7690.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009974322, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009974404, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009974519, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009974622, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009974738, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009974877, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009974973, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009975038, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009975175, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009975278, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506009975477, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009975567, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009976006, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748506009976218, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009976555, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748506009976843, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748506009976935, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009977256, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009977616, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009977711, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009977820, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009978051, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009978113, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748506009978556, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009978703, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009978834, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009979202, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009979283, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009979437, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009979609, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009979675, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009980222, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009980280, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009980387, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009980543, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009980640, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748506009980701, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009980783, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009980851, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009981138, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009981254, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009981360, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748506009981613, "dur": 2548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009984162, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009985342, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009986803, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009988949, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009990468, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009992216, "dur": 2098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009994314, "dur": 2307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009996621, "dur": 1838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506009998460, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010000231, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010001394, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010002561, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010003599, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010004774, "dur": 691, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_4_4.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748506010004653, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010006446, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010007641, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010008873, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010010302, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010011545, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010012690, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010013986, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010015294, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010016551, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010017708, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010019122, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010020283, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010021497, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010023097, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010024495, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010025970, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010027406, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010028898, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010030497, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010031815, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010033372, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010034727, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010036070, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010037941, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010039681, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010040990, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010042173, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010042826, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506010043017, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010043851, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010044076, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506010044508, "dur": 1155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010045664, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010045834, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506010046054, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010046773, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010047020, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506010047222, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010047283, "dur": 3720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010051004, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010051223, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506010051574, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010052992, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010053412, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506010053607, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010054523, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010054678, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506010055145, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748506010055510, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010056052, "dur": 262414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010318469, "dur": 7934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010326404, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010326497, "dur": 4047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010330545, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010330961, "dur": 3412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010334374, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010334710, "dur": 4375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010339086, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010339344, "dur": 3474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010342877, "dur": 3683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010346562, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010346674, "dur": 8410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010355085, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010355196, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748506010355288, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010355369, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748506010356084, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009883396, "dur": 89567, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009972965, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009973539, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009973611, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009973975, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009974197, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009974287, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009974420, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009974499, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009974566, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009974716, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009974808, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009974905, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009974976, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009975137, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009975348, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009975484, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009975578, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009975683, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506009975873, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748506009976330, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748506009976575, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748506009976665, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748506009976906, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009976996, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748506009977175, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009977306, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009977391, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748506009977515, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748506009977711, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009977811, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009977893, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748506009978020, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009978094, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009978191, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748506009978518, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009978596, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009978752, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748506009979094, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009979168, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009979240, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009979402, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009979491, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748506009980053, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748506009980271, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009980364, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009980507, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009980651, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009980759, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009980855, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009980944, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009981017, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748506009981482, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009982785, "dur": 640, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Utils/ObjectPool.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748506009981547, "dur": 4465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009986012, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009987153, "dur": 2113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009989266, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009990819, "dur": 2002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009992821, "dur": 2253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009995075, "dur": 2130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009997206, "dur": 1897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506009999103, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010000623, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010001807, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010002899, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010004824, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShaderGraph/Nodes/LightTextureNode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748506010003945, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010005685, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010006746, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010008013, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010009272, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010010780, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010011967, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010013168, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010014488, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010015830, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010017031, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010018367, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010019594, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010020725, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010022166, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010023685, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010025086, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010026622, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010028119, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010029442, "dur": 1597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010031039, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010032365, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010033933, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010035282, "dur": 1499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010036781, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010038405, "dur": 1716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010040122, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010041476, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010041909, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010041994, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010042166, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010042830, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506010043172, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010043761, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010044233, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506010044549, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506010045106, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506010045376, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010046635, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010046971, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506010047204, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010048130, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010048372, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506010048577, "dur": 1687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010050265, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010050653, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748506010050866, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010050927, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010052155, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010052228, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010053217, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010053454, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010054258, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010054527, "dur": 1226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010055800, "dur": 262642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010318450, "dur": 4687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010323138, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010323245, "dur": 3801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010327047, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010327122, "dur": 5008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010332198, "dur": 3689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010335888, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010336299, "dur": 4361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010340662, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010340828, "dur": 4096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010344925, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748506010345014, "dur": 3267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010348317, "dur": 7328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748506010355770, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748506010364841, "dur": 1456, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 78866, "tid": 12, "ts": 1748506010420667, "dur": 5985, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 78866, "tid": 12, "ts": 1748506010426931, "dur": 4072, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 78866, "tid": 12, "ts": 1748506010405198, "dur": 27800, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}