{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 70593, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 70593, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 70593, "tid": 145, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 70593, "tid": 145, "ts": 1748422648929858, "dur": 1156, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 70593, "tid": 145, "ts": 1748422648948936, "dur": 2384, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 70593, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 70593, "tid": 1, "ts": 1748422642595955, "dur": 54538, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748422642650500, "dur": 533501, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748422643184017, "dur": 821715, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 70593, "tid": 145, "ts": 1748422648951327, "dur": 27, "ph": "X", "name": "", "args": {}}, {"pid": 70593, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642591986, "dur": 69602, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642661592, "dur": 6218767, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642664777, "dur": 22450, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642687237, "dur": 6143, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642693386, "dur": 19104, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642712505, "dur": 1320, "ph": "X", "name": "ProcessMessages 5269", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642713833, "dur": 145, "ph": "X", "name": "ReadAsync 5269", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642713984, "dur": 7, "ph": "X", "name": "ProcessMessages 6466", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642713993, "dur": 161, "ph": "X", "name": "ReadAsync 6466", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642714159, "dur": 3, "ph": "X", "name": "ProcessMessages 1535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642714163, "dur": 78, "ph": "X", "name": "ReadAsync 1535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642714248, "dur": 3, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642714253, "dur": 83, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642714341, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642714344, "dur": 56, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642714442, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642714447, "dur": 2354, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642716805, "dur": 8, "ph": "X", "name": "ProcessMessages 8147", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642716815, "dur": 44, "ph": "X", "name": "ReadAsync 8147", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642716862, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642716864, "dur": 33, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642716901, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642716903, "dur": 119, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717034, "dur": 2, "ph": "X", "name": "ProcessMessages 1523", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717037, "dur": 65, "ph": "X", "name": "ReadAsync 1523", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717107, "dur": 2, "ph": "X", "name": "ProcessMessages 1483", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717110, "dur": 39, "ph": "X", "name": "ReadAsync 1483", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717153, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717155, "dur": 38, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717196, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717198, "dur": 32, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717234, "dur": 49, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717287, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717290, "dur": 40, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717333, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717335, "dur": 71, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717409, "dur": 2, "ph": "X", "name": "ProcessMessages 1167", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717413, "dur": 35, "ph": "X", "name": "ReadAsync 1167", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717452, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717454, "dur": 31, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717490, "dur": 47, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717540, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717542, "dur": 31, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717601, "dur": 55, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642717683, "dur": 637, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642718324, "dur": 2, "ph": "X", "name": "ProcessMessages 1047", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642718328, "dur": 54, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642718391, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642718393, "dur": 6581, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642724981, "dur": 11, "ph": "X", "name": "ProcessMessages 8125", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642724994, "dur": 141, "ph": "X", "name": "ReadAsync 8125", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642725140, "dur": 3, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642734240, "dur": 75150, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642840152, "dur": 27084, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642867271, "dur": 2873, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642870223, "dur": 23, "ph": "X", "name": "ProcessMessages 8189", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422642950005, "dur": 76620, "ph": "X", "name": "ReadAsync 8189", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643026636, "dur": 14, "ph": "X", "name": "ProcessMessages 8175", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643026653, "dur": 162891, "ph": "X", "name": "ReadAsync 8175", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643189550, "dur": 10, "ph": "X", "name": "ProcessMessages 8152", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643189561, "dur": 93, "ph": "X", "name": "ReadAsync 8152", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643189674, "dur": 7, "ph": "X", "name": "ProcessMessages 8183", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643189683, "dur": 93, "ph": "X", "name": "ReadAsync 8183", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643189780, "dur": 1, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643189783, "dur": 60, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643189846, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643189847, "dur": 411, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190262, "dur": 2, "ph": "X", "name": "ProcessMessages 1317", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190266, "dur": 42, "ph": "X", "name": "ReadAsync 1317", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190320, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190323, "dur": 58, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190385, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190387, "dur": 51, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190441, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190443, "dur": 65, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190511, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190514, "dur": 51, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190568, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643190571, "dur": 744, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643191319, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643191322, "dur": 92, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643191426, "dur": 6, "ph": "X", "name": "ProcessMessages 4804", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643191434, "dur": 118, "ph": "X", "name": "ReadAsync 4804", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643191569, "dur": 2, "ph": "X", "name": "ProcessMessages 1130", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643191573, "dur": 62, "ph": "X", "name": "ReadAsync 1130", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643191639, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643191641, "dur": 626, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192272, "dur": 2, "ph": "X", "name": "ProcessMessages 1263", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192276, "dur": 42, "ph": "X", "name": "ReadAsync 1263", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192320, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192323, "dur": 53, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192380, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192381, "dur": 35, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192420, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192423, "dur": 44, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192470, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192472, "dur": 157, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192633, "dur": 2, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643192636, "dur": 409, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193050, "dur": 2, "ph": "X", "name": "ProcessMessages 1295", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193054, "dur": 70, "ph": "X", "name": "ReadAsync 1295", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193128, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193131, "dur": 65, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193199, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193202, "dur": 74, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193280, "dur": 2, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193283, "dur": 59, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193347, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193349, "dur": 66, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193419, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193421, "dur": 50, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193474, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193477, "dur": 45, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193526, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193528, "dur": 49, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193581, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193584, "dur": 69, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643193658, "dur": 578, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194239, "dur": 2, "ph": "X", "name": "ProcessMessages 1581", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194242, "dur": 46, "ph": "X", "name": "ReadAsync 1581", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194303, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194306, "dur": 41, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194350, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194352, "dur": 346, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194703, "dur": 2, "ph": "X", "name": "ProcessMessages 1415", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194706, "dur": 75, "ph": "X", "name": "ReadAsync 1415", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194785, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194787, "dur": 58, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194849, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194851, "dur": 67, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194946, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643194949, "dur": 76, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643195030, "dur": 2, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643195033, "dur": 727, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643195765, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643195768, "dur": 86, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643195859, "dur": 5, "ph": "X", "name": "ProcessMessages 3380", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643195865, "dur": 105, "ph": "X", "name": "ReadAsync 3380", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643195974, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643195976, "dur": 331, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196312, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196314, "dur": 84, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196401, "dur": 2, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196434, "dur": 111, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196550, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196552, "dur": 54, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196611, "dur": 2, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196614, "dur": 45, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196663, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196691, "dur": 54, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196750, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643196753, "dur": 410, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643197169, "dur": 4, "ph": "X", "name": "ProcessMessages 1752", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643197174, "dur": 53, "ph": "X", "name": "ReadAsync 1752", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643197232, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643197235, "dur": 1188, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643198429, "dur": 7, "ph": "X", "name": "ProcessMessages 6020", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643201097, "dur": 1237, "ph": "X", "name": "ReadAsync 6020", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643202338, "dur": 7, "ph": "X", "name": "ProcessMessages 8157", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643202346, "dur": 1271, "ph": "X", "name": "ReadAsync 8157", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643203622, "dur": 10, "ph": "X", "name": "ProcessMessages 8188", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643203633, "dur": 1269, "ph": "X", "name": "ReadAsync 8188", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643204908, "dur": 8, "ph": "X", "name": "ProcessMessages 8125", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643204917, "dur": 42, "ph": "X", "name": "ReadAsync 8125", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643204963, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643204965, "dur": 99, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205069, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205072, "dur": 44, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205119, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205121, "dur": 163, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205288, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205289, "dur": 60, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205352, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205354, "dur": 29, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205386, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205388, "dur": 258, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205648, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205651, "dur": 32, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205687, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205690, "dur": 69, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205762, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205764, "dur": 53, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205822, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205823, "dur": 38, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643205865, "dur": 1841, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643207709, "dur": 3, "ph": "X", "name": "ProcessMessages 3686", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643207714, "dur": 39, "ph": "X", "name": "ReadAsync 3686", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643207756, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643207758, "dur": 350, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643208112, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643208114, "dur": 293, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643208410, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643208412, "dur": 3107, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643211525, "dur": 3, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643211529, "dur": 85, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643211618, "dur": 3, "ph": "X", "name": "ProcessMessages 1541", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643211622, "dur": 933, "ph": "X", "name": "ReadAsync 1541", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643212560, "dur": 2, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643212563, "dur": 2829, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643215397, "dur": 2, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643215400, "dur": 831, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643216246, "dur": 2, "ph": "X", "name": "ProcessMessages 1066", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643216249, "dur": 48, "ph": "X", "name": "ReadAsync 1066", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643216301, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643216303, "dur": 329, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643216634, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643216636, "dur": 584, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643217223, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643217225, "dur": 658, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643217886, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643217888, "dur": 47, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643217938, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643217940, "dur": 726, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643218669, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643218671, "dur": 622, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643219297, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643219299, "dur": 1369, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643220671, "dur": 1, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643220673, "dur": 91, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643220767, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643220769, "dur": 298, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643221072, "dur": 3752, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643224831, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643224835, "dur": 407, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643225247, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643225250, "dur": 5515, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643230799, "dur": 3, "ph": "X", "name": "ProcessMessages 2256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643230804, "dur": 772, "ph": "X", "name": "ReadAsync 2256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643231581, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643231583, "dur": 39, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643231626, "dur": 1089, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643232719, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643232722, "dur": 2553, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643235280, "dur": 3, "ph": "X", "name": "ProcessMessages 1781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643235284, "dur": 1283, "ph": "X", "name": "ReadAsync 1781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643236573, "dur": 2, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643236576, "dur": 622, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643237816, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643237819, "dur": 679, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643238503, "dur": 2, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643238505, "dur": 34, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643238542, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643238544, "dur": 2508, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643241056, "dur": 3, "ph": "X", "name": "ProcessMessages 2751", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643241060, "dur": 887, "ph": "X", "name": "ReadAsync 2751", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643241950, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643241952, "dur": 2388, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643244345, "dur": 2, "ph": "X", "name": "ProcessMessages 1331", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643244348, "dur": 119, "ph": "X", "name": "ReadAsync 1331", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643244471, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643244473, "dur": 1140, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643245617, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643245620, "dur": 584, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643246208, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643246210, "dur": 1257, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643247488, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643247490, "dur": 2459, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643249967, "dur": 2, "ph": "X", "name": "ProcessMessages 1276", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643249970, "dur": 419, "ph": "X", "name": "ReadAsync 1276", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643250393, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643250395, "dur": 259, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643250673, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643250675, "dur": 1370, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643252059, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643252061, "dur": 1603, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643253668, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643253670, "dur": 322, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643253995, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643253997, "dur": 826, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643254827, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643254829, "dur": 334, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643255168, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643255170, "dur": 1895, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643257072, "dur": 2, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643257076, "dur": 1266, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643258346, "dur": 2, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643258349, "dur": 603, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643258956, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643258958, "dur": 1529, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643260491, "dur": 2, "ph": "X", "name": "ProcessMessages 1604", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643260494, "dur": 1583, "ph": "X", "name": "ReadAsync 1604", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643262081, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643262083, "dur": 283, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643262370, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643262372, "dur": 2356, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643264733, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643264735, "dur": 70, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643264809, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643264811, "dur": 425, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643265239, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643265241, "dur": 767, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643270623, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643270627, "dur": 55, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643270702, "dur": 2, "ph": "X", "name": "ProcessMessages 1634", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643270704, "dur": 629, "ph": "X", "name": "ReadAsync 1634", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643271338, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643271342, "dur": 867, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643272211, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643272214, "dur": 1276, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643273493, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643325269, "dur": 124, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643325399, "dur": 11, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643325412, "dur": 70, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643330035, "dur": 16, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643330058, "dur": 171, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643330234, "dur": 651, "ph": "X", "name": "ProcessMessages 2822", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643330890, "dur": 81, "ph": "X", "name": "ReadAsync 2822", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643330976, "dur": 4, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643330982, "dur": 90, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331076, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331078, "dur": 132, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331215, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331217, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331466, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331469, "dur": 191, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331665, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331668, "dur": 69, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331742, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643331744, "dur": 2343, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643334094, "dur": 9, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643334105, "dur": 1187, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643335315, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643335320, "dur": 345, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643335669, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643335672, "dur": 184, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643335863, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643335866, "dur": 263, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643336170, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643336174, "dur": 1688, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643337869, "dur": 4, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643337874, "dur": 72, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643337954, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643337957, "dur": 191, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643338153, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643338155, "dur": 594, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643338755, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643338758, "dur": 59, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643338832, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643338835, "dur": 2126, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643342245, "dur": 6, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643342257, "dur": 321, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643342584, "dur": 8, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643342594, "dur": 65, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643342663, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643342665, "dur": 267, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643342946, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643342949, "dur": 80, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643343034, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643343037, "dur": 558, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643343600, "dur": 5, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643343606, "dur": 587, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643344199, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643344202, "dur": 202, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643344407, "dur": 4, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643344413, "dur": 180, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643344598, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643344601, "dur": 841, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643345456, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643345459, "dur": 11778, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643357245, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643357249, "dur": 236, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643357490, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643357492, "dur": 699, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643358195, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643358197, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643358467, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643358470, "dur": 243, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643358717, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643358719, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643358996, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643358998, "dur": 2471, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643361476, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643361479, "dur": 166515, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643614187, "dur": 2978, "ph": "X", "name": "ProcessMessages 3000", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643617272, "dur": 319655, "ph": "X", "name": "ReadAsync 3000", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643936949, "dur": 5, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643936957, "dur": 159, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643937123, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643937126, "dur": 1216, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643938351, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643938354, "dur": 82, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643938444, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643938446, "dur": 84, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643938536, "dur": 1, "ph": "X", "name": "ProcessMessages 4138", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643965177, "dur": 159, "ph": "X", "name": "ReadAsync 4138", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643965343, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643965347, "dur": 70, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422643965422, "dur": 52616, "ph": "X", "name": "ProcessMessages 4842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644018049, "dur": 2102, "ph": "X", "name": "ReadAsync 4842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644020158, "dur": 7, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644020167, "dur": 12269, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644032448, "dur": 13, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644032467, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644032556, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644032558, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644032672, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644032675, "dur": 8411, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644041097, "dur": 18, "ph": "X", "name": "ProcessMessages 1808", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422644041117, "dur": 4387957, "ph": "X", "name": "ReadAsync 1808", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648429122, "dur": 74, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648429198, "dur": 5153, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648434363, "dur": 9, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648434374, "dur": 425575, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648859960, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648859964, "dur": 85, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648860068, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648860071, "dur": 566, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648860644, "dur": 72, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648860723, "dur": 67, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648860793, "dur": 59, "ph": "X", "name": "ProcessMessages 2629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648860874, "dur": 5015, "ph": "X", "name": "ReadAsync 2629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648865897, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648865903, "dur": 705, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648866614, "dur": 55, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648866673, "dur": 60, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648866738, "dur": 28, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648866768, "dur": 1066, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648867852, "dur": 596, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748422648868454, "dur": 11543, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 70593, "tid": 145, "ts": 1748422648951356, "dur": 5235, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 70593, "tid": 8589934592, "ts": 1748422642551332, "dur": 1462326, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748422644013663, "dur": 21, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748422644013686, "dur": 33218, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 70593, "tid": 145, "ts": 1748422648956594, "dur": 31, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 70593, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 70593, "tid": 4294967296, "ts": 1748422642420969, "dur": 6466763, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748422642453330, "dur": 77186, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748422648889719, "dur": 30534, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748422648906702, "dur": 7581, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748422648920987, "dur": 38, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 70593, "tid": 145, "ts": 1748422648956627, "dur": 25, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748422642626476, "dur": 109, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422642626930, "dur": 19945, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422642647756, "dur": 63100, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422642711118, "dur": 184, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748422642711303, "dur": 517, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422642712146, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1748422642712979, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_A80C913DF2042397.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642713240, "dur": 429, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642713846, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642714039, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3C55960BA9CB8745.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642714169, "dur": 254, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_EBB973DC47E5C128.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642714816, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B92AE864B27146B1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642714920, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642715008, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642715709, "dur": 1727, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642718739, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748422642722775, "dur": 2830, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642727553, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748422642727650, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748422642728725, "dur": 6360, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748422642736285, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642737211, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748422642737354, "dur": 131677, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642869045, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748422642939604, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642940760, "dur": 2821, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748422642953270, "dur": 455, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_1DF7BBC55D029A75.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422642953817, "dur": 351, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748422642954202, "dur": 511, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748422642954747, "dur": 72608, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748422643027937, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748422643028027, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748422643028612, "dur": 7139, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748422643037423, "dur": 153, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422643040949, "dur": 149355, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748422643191950, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748422643193943, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748422643196858, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748422643196987, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748422643199718, "dur": 2079, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748422643202957, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748422643204748, "dur": 800, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748422643287468, "dur": 38566, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748422642711962, "dur": 616494, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422643328471, "dur": 5538988, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422648867479, "dur": 139, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422648867673, "dur": 69, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422648867819, "dur": 485, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422648868334, "dur": 71, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422648868508, "dur": 1533, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748422642711954, "dur": 616587, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643328556, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643329215, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643329507, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643329825, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643329956, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643330052, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643330350, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643330421, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643330495, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643330802, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643330888, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643331070, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643331189, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643331350, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643331451, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643331571, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643331640, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643331838, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643331947, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643332150, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643332260, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643332357, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643332519, "dur": 1018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643333538, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643333613, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_FB7A597403E5FDBB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643333825, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643333929, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643334017, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643334635, "dur": 2564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643337267, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748422643337361, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643337469, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643337652, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643337988, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643338271, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643338689, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643338761, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643339116, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643339413, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643339692, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643339819, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748422643340505, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748422643340630, "dur": 674, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643341330, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643341624, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643342184, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643342239, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643342307, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643342437, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643342990, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643343063, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643343239, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643343674, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643343750, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643343917, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643344005, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748422643344056, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643344163, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643344267, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643344386, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643344526, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643344626, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748422643344874, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643344962, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643345187, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643345286, "dur": 2496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643347783, "dur": 1901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643349685, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643351388, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643352783, "dur": 1799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643354583, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643355775, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643356934, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643358085, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643359297, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643359411, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643360713, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643361932, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643363143, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643365363, "dur": 869, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Plugin/BoltFlowConfiguration.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748422643364914, "dur": 2377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643367292, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643369013, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643370137, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643371434, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643373107, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643374516, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643376135, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643377328, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643378475, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643379580, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643380712, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643381832, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643383011, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643384309, "dur": 1986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643386296, "dur": 2052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643388348, "dur": 2070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643390419, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643392124, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643393891, "dur": 1654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643395545, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643397082, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643398206, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643399306, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643400408, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643401819, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643403199, "dur": 1242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643404495, "dur": 3247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748422643407742, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643407982, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643408172, "dur": 1423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748422643409596, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643409804, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_CB2B1FA2A63FF8D9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643409892, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643409960, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748422643410181, "dur": 1255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748422643411437, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643411604, "dur": 2225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748422643413892, "dur": 781, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643867329, "dur": 98690, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643415883, "dur": 550251, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748422643970852, "dur": 17700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748422643988554, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643988726, "dur": 9709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748422643998437, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422643999059, "dur": 14705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748422644013766, "dur": 600, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644014380, "dur": 6470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748422644020851, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644021270, "dur": 4104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748422644025376, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644025681, "dur": 3253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748422644028935, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644029004, "dur": 4073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748422644033086, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644033336, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644033564, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748422644033621, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644033814, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748422644033910, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644034014, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748422644034073, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644034171, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748422644034225, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644034326, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644034415, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748422644034472, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644034567, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644034714, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748422644034774, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644034940, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644035036, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644035116, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748422644035182, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644035467, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644035639, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748422644035696, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644035770, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644035851, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748422644036392, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644036610, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748422644036720, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644036814, "dur": 10202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644047019, "dur": 9114, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748422644056134, "dur": 4811391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422642711952, "dur": 616563, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643328537, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748422643329500, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_9909B9D5D2D6D40E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643329955, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643330042, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643330525, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AB6C0CE8644C7690.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643330599, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643330677, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643330785, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643330873, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643331030, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643331100, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643331214, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643331385, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643331500, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643331648, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643331800, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643331881, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643332020, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643332195, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748422643332840, "dur": 25638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643358479, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643358745, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643358797, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643358911, "dur": 2907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643361819, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643361908, "dur": 26109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643388018, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643388445, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643388829, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643389007, "dur": 4147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643393155, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643393274, "dur": 9400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643402675, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643403008, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643403086, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643403192, "dur": 1257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643404506, "dur": 2560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643407067, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643407343, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643407572, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643408458, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643408606, "dur": 1734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643410341, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643410508, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643410596, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643410828, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643411816, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643412066, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643412386, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643412571, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643412660, "dur": 1575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643414235, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643414573, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643415258, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643415531, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643416347, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643416701, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_DD9C61FBB7D63C03.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643416897, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643416959, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_8E9298DC6B12ED57.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643417014, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643417153, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643417624, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643417712, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643418334, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643418455, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643418653, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643418996, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643419057, "dur": 1437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643420495, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643420784, "dur": 1242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643422026, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643422202, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643422460, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643422592, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643422713, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_CBD4E358CB8CDF43.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643422767, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643422841, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748422643423301, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643423423, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643423597, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643423802, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643423941, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643424030, "dur": 546956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643970994, "dur": 11416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643982412, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643982957, "dur": 7687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643990645, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643990947, "dur": 7516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643998464, "dur": 717, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422643999182, "dur": 658, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422643999844, "dur": 9459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422644009305, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422644009400, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422644009495, "dur": 4840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422644014336, "dur": 804, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422644015155, "dur": 6423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422644021579, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422644021688, "dur": 6488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422644028178, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422644028315, "dur": 8543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748422644036859, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422644037034, "dur": 4829384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748422648866444, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748422648866428, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748422648866589, "dur": 826, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748422642712019, "dur": 616574, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643328608, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643329222, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643329624, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643329873, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643329995, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D0749362471C98A9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643330157, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643330466, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643330704, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643330821, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643330981, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643331119, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643331268, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643331436, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643331624, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643331998, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643332166, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643332379, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643332437, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748422643333345, "dur": 24166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643357513, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643357753, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643357827, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643357929, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643359227, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643359286, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643360619, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643361845, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643363019, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643365306, "dur": 859, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/XFlowGraph.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748422643364671, "dur": 2288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643366960, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643368739, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643369992, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643371170, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643372966, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643374440, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643376056, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643377240, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643378430, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643379535, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643380685, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643381793, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643382952, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643384243, "dur": 1993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643386236, "dur": 2102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643388338, "dur": 2039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643390378, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643392105, "dur": 1858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643393964, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643395611, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643397121, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643398289, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643399415, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643400465, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643401772, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643403119, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643404385, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643404670, "dur": 2985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643407655, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643407920, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643408073, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643408161, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643408572, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643408626, "dur": 1358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643409985, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643410269, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643410329, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643410630, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643410693, "dur": 1336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643412030, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643412284, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643412539, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643412629, "dur": 2423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643415059, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643415247, "dur": 1753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643417001, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643417205, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643417408, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_E9EA3AB45900FF1D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643417586, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643417955, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643418385, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643418564, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643418743, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643418835, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643419156, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643420022, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643420238, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643421546, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643421625, "dur": 1080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643422705, "dur": 694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643423404, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Editor.ref.dll_FD249531A54E376F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748422643423517, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643423578, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748422643423634, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643423800, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643423937, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643424029, "dur": 546849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643970888, "dur": 6890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643977779, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643978023, "dur": 7054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643985079, "dur": 2017, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643987098, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643987314, "dur": 8716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748422643996032, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422643996297, "dur": 8869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748422644005168, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644005398, "dur": 8783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748422644014182, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644014473, "dur": 5752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748422644020227, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644020750, "dur": 5576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748422644026328, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644026695, "dur": 6287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748422644032983, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644033165, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644033342, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748422644033400, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644033586, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644033726, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644033817, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748422644033915, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644034033, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644034099, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748422644034156, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644034259, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644034381, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644034565, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644034668, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644034831, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644035011, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644035077, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748422644035141, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644035543, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644035687, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644035787, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644036316, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644036480, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644036709, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644036807, "dur": 956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748422644037808, "dur": 4829674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422642712008, "dur": 616551, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643328572, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643329171, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643329549, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_25D10CC8709418E4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643329845, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643329914, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_25D10CC8709418E4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643330003, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643330250, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643330435, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643330550, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD8CC2F62B9395CF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643330623, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643330715, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643330806, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643330915, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643331034, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643331163, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643331317, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643331422, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643331573, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643331629, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643331787, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643331866, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643332039, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643332183, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643332244, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643332331, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643332423, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643332628, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643332789, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643332911, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643332984, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748422643333222, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643333460, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748422643334146, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748422643334209, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748422643334387, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748422643334448, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643334541, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643335714, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748422643336099, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643336181, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748422643336502, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643336658, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748422643337331, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748422643337562, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643337689, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643338182, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643338390, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643338593, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748422643338748, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643338859, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643338977, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643339313, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643339486, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643339706, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748422643340513, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643340602, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643341335, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643341618, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643342164, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643342250, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643342372, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643343259, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643343609, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643343685, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643343756, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643343909, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748422643344071, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643344175, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643344281, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643344407, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643344543, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643344714, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748422643344872, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643344971, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643345137, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643345278, "dur": 2675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643347953, "dur": 1964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643349918, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643351647, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643353016, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643354587, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643355786, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643356981, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643358168, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643359362, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643360686, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643361905, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643363102, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643365319, "dur": 832, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_4_10.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748422643364784, "dur": 2329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643367113, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643368942, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643370083, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643371053, "dur": 1777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643372831, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643374308, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643375965, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643377175, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643378341, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643379474, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643380629, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643381751, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643382905, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643384184, "dur": 2011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643386196, "dur": 2032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643388228, "dur": 2094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643390322, "dur": 1736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643392064, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643393813, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643395493, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643397037, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643398169, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643399283, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643400359, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643401729, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643403090, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643404270, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643404799, "dur": 1133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643405933, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643406163, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643406287, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643406965, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643407112, "dur": 1711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643408824, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643409038, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643409096, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643409365, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643409492, "dur": 3350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643412843, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643413100, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643413163, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643413837, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643414163, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643414520, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643414602, "dur": 685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643415288, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643415562, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643415631, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643416317, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643416586, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Path.Editor.ref.dll_91802D938EA41213.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643416700, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643416792, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_3F93312DF16ABE8A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643416947, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643417031, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643417096, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643417539, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643417607, "dur": 2023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643419636, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643419813, "dur": 1333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643421147, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643421350, "dur": 1274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643422625, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643422797, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643422878, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643423247, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643423319, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748422643423835, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643424545, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643424643, "dur": 546249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643970911, "dur": 6094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643977007, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643977535, "dur": 5479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643983016, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643983136, "dur": 6507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748422643989645, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422643989738, "dur": 14756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748422644004495, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644004602, "dur": 5638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748422644010241, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644010699, "dur": 4885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748422644015585, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644015666, "dur": 6465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748422644022132, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644022289, "dur": 6373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748422644028664, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644028746, "dur": 3650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748422644032397, "dur": 662, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644033258, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644033469, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644033609, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748422644033677, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644033842, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644034028, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644034093, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748422644034159, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644034259, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644034356, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644034494, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644034598, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644034721, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644034832, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748422644034896, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644035038, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644035110, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748422644035170, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644035242, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644035570, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644035766, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644035843, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748422644036380, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644036599, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644036734, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644037027, "dur": 19114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748422644056144, "dur": 4811429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422642711941, "dur": 616541, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643328529, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748422643329171, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643329242, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748422643329524, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643329929, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_97FEC325F9337F2C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643330248, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_97FEC325F9337F2C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643330310, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3C55960BA9CB8745.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643330463, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643330526, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643330640, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643330736, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643330825, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643330976, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643331109, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643331200, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643331342, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643331444, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643331585, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643331672, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643332018, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643332175, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643332501, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643332742, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748422643333102, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643333352, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748422643333538, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643333605, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748422643334239, "dur": 2209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643336449, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643336642, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643337481, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643337681, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643338102, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643338301, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643338487, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643338650, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643338834, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643338968, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643339314, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643339422, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643339666, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643339869, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643340568, "dur": 663, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643341252, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643341370, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643341730, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643342228, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643342337, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643343132, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643343512, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748422643343718, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643343781, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643343849, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643343989, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748422643344085, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643344187, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643344266, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643344360, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643344483, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643344871, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643345025, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748422643345224, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643345285, "dur": 2510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643347796, "dur": 1965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643349762, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643351602, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643352910, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643354662, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643355843, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643356978, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643358104, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643359329, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643360674, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643361873, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643363089, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643365326, "dur": 868, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Ports/ControlOutputWidget.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748422643364769, "dur": 2283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643367052, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643368766, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643369967, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643370896, "dur": 1888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643372784, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643374268, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643375946, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643377107, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643378296, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643379434, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643380590, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643381700, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643382881, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643384161, "dur": 1945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643386106, "dur": 2064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643388171, "dur": 2167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643390339, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643392087, "dur": 1730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643393817, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643395506, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643397052, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643398243, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643399349, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643400443, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643401691, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643403143, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643404375, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643404611, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643405336, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643405617, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643405675, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643406471, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643406593, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643406757, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643406828, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643407268, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643407640, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643408261, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643408495, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643409878, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643410058, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_E30EE1A78470D274.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643410204, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643410264, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643410478, "dur": 2027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643412506, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643412797, "dur": 3007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643415805, "dur": 682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643416496, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643416736, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643416809, "dur": 1905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643418714, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643418922, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748422643419147, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643420186, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643420448, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643421460, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643421963, "dur": 1903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643423867, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643424134, "dur": 546782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643970935, "dur": 6274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643977210, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643977426, "dur": 4327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643981756, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643982283, "dur": 9498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748422643991783, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422643991920, "dur": 11870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748422644003798, "dur": 774, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644004607, "dur": 6354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748422644010962, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644011102, "dur": 4719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748422644015822, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644015879, "dur": 4701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748422644020581, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644020966, "dur": 6227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748422644027194, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644027340, "dur": 6510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748422644033851, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644034029, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644034116, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644034191, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644034277, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644034411, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644034537, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644034724, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644034853, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748422644034914, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644035036, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644035144, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644035248, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644035497, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644035562, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644035622, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748422644035739, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644035836, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644036474, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748422644036561, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644036773, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644036990, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748422644037067, "dur": 4830491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422642712012, "dur": 616563, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643328588, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643329169, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643329571, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643329975, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_888C5CF0B798DF8B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643330267, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643330319, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643330489, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_E69ADDE2B61683F5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643330586, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643330650, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643330758, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643330868, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643331018, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643331147, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643331307, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643331418, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643331571, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643331660, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643331851, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643331996, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643332189, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643332265, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643332370, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643332584, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643332672, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643332748, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748422643333147, "dur": 1419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643334597, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748422643335694, "dur": 2438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643338133, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643338338, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643338832, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643338935, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643339283, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643339413, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643339681, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643339916, "dur": 1470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643341387, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643341747, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643342274, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643342411, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643342997, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643343684, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643343762, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643343949, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748422643344108, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643344211, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643344312, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643344444, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643344570, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643344677, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643344895, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643345034, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643345119, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643345222, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748422643345406, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643345853, "dur": 2131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643347984, "dur": 2035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643350019, "dur": 1665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643351684, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643353129, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643354796, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643356025, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643357185, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643358369, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643359737, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643360967, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643362227, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643363421, "dur": 1805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643365314, "dur": 845, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Framework/Control/SelectOnIntegerDescriptor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748422643365227, "dur": 2216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643367443, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643369212, "dur": 1728, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@14.1.0/Editor/Drawing/Manipulators/ElementResizer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748422643369212, "dur": 3561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643372774, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643374236, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643375838, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643377074, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643378244, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643379384, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643380543, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643381626, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643382812, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643384088, "dur": 1888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643385977, "dur": 2092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643388069, "dur": 2199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643390268, "dur": 1677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643391946, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643393664, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643395391, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643396951, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643398111, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643399235, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643400349, "dur": 1412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643401761, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643403111, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643404351, "dur": 1840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643406245, "dur": 2899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643409145, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643409363, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643409762, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643409852, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643411058, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643411348, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643411496, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643412094, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643412432, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643412555, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643412905, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643413992, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643414491, "dur": 2141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643416633, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643417081, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643417161, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643418031, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643418190, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643418469, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643418667, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643418984, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643419207, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643419293, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643419987, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643420208, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643421579, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643421809, "dur": 2491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643424301, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643424417, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748422643424608, "dur": 546245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643970874, "dur": 6815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643977691, "dur": 727, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643978431, "dur": 14367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643992800, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643992934, "dur": 3506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748422643996441, "dur": 2199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422643998670, "dur": 12733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748422644011404, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422644011687, "dur": 6586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748422644018275, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422644018468, "dur": 5276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748422644023746, "dur": 1016, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422644024776, "dur": 6212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748422644030996, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422644031105, "dur": 6567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748422644037674, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748422644037790, "dur": 4829710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422642712022, "dur": 616616, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643328649, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643329169, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643329516, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643329888, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A49EDD7A339F9A85.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643330226, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643330293, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643330414, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643330521, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643330630, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_57AB718CCA0A791C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643330739, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643330836, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643331008, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643331082, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643331188, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643331380, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643331516, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643331624, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643331752, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643331877, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643331978, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643332174, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643332380, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643332511, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643332808, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748422643333081, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643333253, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643333583, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748422643334122, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643334214, "dur": 2349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643336564, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643336656, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643337679, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643337926, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643338260, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643338689, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643338780, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748422643339109, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643339349, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643339574, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643339639, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643339825, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643340039, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748422643340292, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643340652, "dur": 744, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643341405, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643341704, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643341866, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643342225, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643342320, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643342435, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643342970, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643343434, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643343614, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643343793, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643343856, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643343943, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643344008, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643344091, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643344192, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643344463, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643344615, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748422643344854, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643344954, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643345157, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643345299, "dur": 2612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643347912, "dur": 2212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643350125, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643351717, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643353140, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643354787, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643355994, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643357134, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643358307, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643359636, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643360880, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643362126, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643363266, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643365297, "dur": 882, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Framework/Nesting/GraphOutputWidget.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748422643365082, "dur": 2310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643367393, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643369493, "dur": 1504, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@14.1.0/Editor/Drawing/Views/ContextView.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748422643369208, "dur": 3237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643372446, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643373886, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643375404, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643376739, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643377962, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643379126, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643380271, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643381407, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643382510, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643383755, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643385487, "dur": 2015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643387503, "dur": 2213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643389750, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643389926, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643390013, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643391823, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643393632, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643395406, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643396921, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643398068, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643399180, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643400256, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643400643, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643401823, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643403240, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643404457, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643404525, "dur": 4034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748422643408559, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643409077, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643409481, "dur": 3863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748422643413345, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643413643, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643414300, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643414561, "dur": 1746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748422643416308, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643416605, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643416712, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643416925, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643417015, "dur": 1880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748422643418896, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643419260, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643419352, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643419501, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643419568, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748422643420631, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643420932, "dur": 1599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748422643422532, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643422742, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748422643422824, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643423293, "dur": 1280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748422643424574, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643424710, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748422643425898, "dur": 107, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422643426039, "dur": 5003137, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748422648431035, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748422648430718, "dur": 3181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748422648434734, "dur": 285, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422648860569, "dur": 947, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748422648435050, "dur": 426483, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748422648866425, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748422648866388, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748422648866654, "dur": 655, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748422648867314, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422642712022, "dur": 616591, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643328635, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643329561, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1996541149DA13E0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643329920, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643329999, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643330289, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643330468, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_72608D0D80FBEA70.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643330543, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643330618, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B92AE864B27146B1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643330724, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643330849, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643331006, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643331133, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643331298, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643331396, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643331864, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643332020, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748422643332245, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643332609, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643333093, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643333795, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643333946, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643334341, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643334398, "dur": 1256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643335698, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748422643336049, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643336147, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643336332, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643336422, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643336596, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643337545, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643337743, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643338069, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643338224, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643338424, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643339034, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643339170, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643339228, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643339293, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643339437, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643339710, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643339817, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748422643340516, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643340630, "dur": 792, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643341453, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643341876, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643342255, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643342385, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643343154, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643343563, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643343632, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643343716, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643343838, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643343971, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643344116, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643344251, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643344339, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643344472, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643344823, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643344909, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748422643345166, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643345302, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643345775, "dur": 2194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643347969, "dur": 2091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643350060, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643351697, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643353077, "dur": 1700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643354777, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643355951, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643357108, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643358298, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643359674, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643360866, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643362104, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643363283, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643365292, "dur": 882, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Framework/Variables/UnifiedVariableUnitDescriptor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748422643365024, "dur": 2325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643367349, "dur": 1872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643369221, "dur": 1780, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@14.1.0/Editor/Drawing/Inspector/PropertyDrawers/Texture2DPropertyDrawer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748422643369221, "dur": 3508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643372729, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643374208, "dur": 1605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643375814, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643377012, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643378162, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643379281, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643380460, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643381562, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643382705, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643383975, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643385740, "dur": 2010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643387751, "dur": 2210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643389962, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643391772, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643393370, "dur": 1888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643395258, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643396807, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643397939, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643399068, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643400125, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643400590, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643401657, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643403074, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643404264, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643404637, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643404744, "dur": 2474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748422643407219, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643407497, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643407665, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643407786, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748422643408857, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643409152, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643409213, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643409442, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643409503, "dur": 1283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748422643410787, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643410953, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643411126, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643411184, "dur": 6075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748422643417260, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643417686, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_BF69964832659484.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643418021, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643418101, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643418314, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643418380, "dur": 2135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748422643420515, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643420771, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Editor.ref.dll_7BE565C70AE586E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643420904, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643421244, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643421315, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748422643422406, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643422698, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643422766, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748422643422846, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643423488, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748422643423704, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643423839, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643424212, "dur": 546745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643970981, "dur": 15841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748422643986825, "dur": 1018, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643987858, "dur": 7227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748422643995087, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422643995468, "dur": 4954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748422644000424, "dur": 1481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422644001930, "dur": 11904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748422644013836, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422644014130, "dur": 7139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748422644021270, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422644021376, "dur": 7583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748422644028965, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422644029033, "dur": 7825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748422644036859, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422644036974, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748422644037049, "dur": 4830604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748422648876486, "dur": 2924, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 70593, "tid": 145, "ts": 1748422648967726, "dur": 15255, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 70593, "tid": 145, "ts": 1748422648983113, "dur": 7896, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 70593, "tid": 145, "ts": 1748422648939578, "dur": 60261, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}