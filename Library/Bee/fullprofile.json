{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 70122, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 70122, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 70122, "tid": 68, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 70122, "tid": 68, "ts": 1748418890568684, "dur": 1431, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 70122, "tid": 68, "ts": 1748418890578632, "dur": 1680, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 70122, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 70122, "tid": 1, "ts": 1748418889074254, "dur": 23885, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70122, "tid": 1, "ts": 1748418889098143, "dur": 130129, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70122, "tid": 1, "ts": 1748418889228283, "dur": 174866, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 70122, "tid": 68, "ts": 1748418890580329, "dur": 299, "ph": "X", "name": "", "args": {}}, {"pid": 70122, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889071446, "dur": 29423, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889100874, "dur": 1424480, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889102399, "dur": 9216, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889111634, "dur": 2472, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889114109, "dur": 9977, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889124092, "dur": 824, "ph": "X", "name": "ProcessMessages 8118", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889124921, "dur": 93, "ph": "X", "name": "ReadAsync 8118", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889125031, "dur": 14, "ph": "X", "name": "ProcessMessages 8127", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889125050, "dur": 34827, "ph": "X", "name": "ReadAsync 8127", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889159883, "dur": 7, "ph": "X", "name": "ProcessMessages 8118", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889159892, "dur": 2369, "ph": "X", "name": "ReadAsync 8118", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889162278, "dur": 8, "ph": "X", "name": "ProcessMessages 8181", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889162313, "dur": 64, "ph": "X", "name": "ReadAsync 8181", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889162391, "dur": 2, "ph": "X", "name": "ProcessMessages 1439", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889162394, "dur": 4268, "ph": "X", "name": "ReadAsync 1439", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236122, "dur": 4, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236128, "dur": 130, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236263, "dur": 9, "ph": "X", "name": "ProcessMessages 8190", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236303, "dur": 92, "ph": "X", "name": "ReadAsync 8190", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236399, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236434, "dur": 70, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236507, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236509, "dur": 63, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236576, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236577, "dur": 316, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236898, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236900, "dur": 44, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236949, "dur": 9, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889236968, "dur": 76, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237059, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237061, "dur": 346, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237409, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237412, "dur": 35, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237451, "dur": 117, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237571, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237592, "dur": 34, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237629, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237631, "dur": 30, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237699, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237701, "dur": 53, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237757, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237760, "dur": 57, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889237822, "dur": 353, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238176, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238178, "dur": 43, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238224, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238227, "dur": 40, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238284, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238287, "dur": 92, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238383, "dur": 2, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238396, "dur": 37, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238437, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238439, "dur": 36, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238478, "dur": 17, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238499, "dur": 100, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238603, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238605, "dur": 266, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238875, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238878, "dur": 49, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238945, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238947, "dur": 47, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238996, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889238998, "dur": 45, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239046, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239048, "dur": 34, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239085, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239087, "dur": 54, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239145, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239147, "dur": 48, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239198, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239200, "dur": 52, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239255, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239256, "dur": 49, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239308, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239310, "dur": 54, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239366, "dur": 17, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239386, "dur": 51, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239439, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239441, "dur": 69, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239513, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239515, "dur": 32, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239550, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239552, "dur": 86, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239644, "dur": 347, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239995, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889239998, "dur": 54, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240055, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240058, "dur": 50, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240112, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240115, "dur": 87, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240206, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240209, "dur": 50, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240262, "dur": 7, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240272, "dur": 74, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240350, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240352, "dur": 55, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240412, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240414, "dur": 55, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240506, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240508, "dur": 58, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240571, "dur": 2, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240594, "dur": 57, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240655, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240657, "dur": 72, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240732, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240735, "dur": 52, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240791, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889240805, "dur": 333, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241152, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241154, "dur": 40, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241219, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241221, "dur": 62, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241303, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241306, "dur": 53, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241362, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241364, "dur": 56, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241423, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241426, "dur": 40, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241477, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241479, "dur": 80, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241563, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241566, "dur": 65, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241633, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241634, "dur": 61, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241699, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241702, "dur": 52, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241758, "dur": 40, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241800, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241802, "dur": 38, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241842, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241844, "dur": 32, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241889, "dur": 49, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241940, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241942, "dur": 32, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241976, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889241979, "dur": 135, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242118, "dur": 1, "ph": "X", "name": "ProcessMessages 1004", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242121, "dur": 39, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242163, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242165, "dur": 39, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242207, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242216, "dur": 45, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242294, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242296, "dur": 265, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242564, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242566, "dur": 34, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242603, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242605, "dur": 32, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242639, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242641, "dur": 33, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242678, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889242679, "dur": 523, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243206, "dur": 3, "ph": "X", "name": "ProcessMessages 4336", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243219, "dur": 441, "ph": "X", "name": "ReadAsync 4336", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243663, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243665, "dur": 38, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243708, "dur": 32, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243743, "dur": 55, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243802, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243804, "dur": 39, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243846, "dur": 55, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243905, "dur": 33, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243941, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889243944, "dur": 564, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889244511, "dur": 4, "ph": "X", "name": "ProcessMessages 5155", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889244516, "dur": 31, "ph": "X", "name": "ReadAsync 5155", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889244550, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889244552, "dur": 46, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889244601, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889244603, "dur": 545, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245152, "dur": 5, "ph": "X", "name": "ProcessMessages 4116", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245158, "dur": 37, "ph": "X", "name": "ReadAsync 4116", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245208, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245209, "dur": 42, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245254, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245256, "dur": 34, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245293, "dur": 30, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245326, "dur": 50, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245381, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245383, "dur": 48, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245434, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245436, "dur": 67, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889245508, "dur": 516, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889246027, "dur": 4, "ph": "X", "name": "ProcessMessages 5154", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889246032, "dur": 34, "ph": "X", "name": "ReadAsync 5154", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889246071, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889246073, "dur": 32, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889246109, "dur": 1145, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889247258, "dur": 6, "ph": "X", "name": "ProcessMessages 8177", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889247265, "dur": 31, "ph": "X", "name": "ReadAsync 8177", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889247300, "dur": 46, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889247358, "dur": 30, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889247391, "dur": 55, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889247449, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889247451, "dur": 521, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889247976, "dur": 4, "ph": "X", "name": "ProcessMessages 5335", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889247981, "dur": 3560, "ph": "X", "name": "ReadAsync 5335", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889251548, "dur": 9, "ph": "X", "name": "ProcessMessages 7512", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889253128, "dur": 59, "ph": "X", "name": "ReadAsync 7512", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889253190, "dur": 6, "ph": "X", "name": "ProcessMessages 8157", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889253197, "dur": 42, "ph": "X", "name": "ReadAsync 8157", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889253249, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889253253, "dur": 562, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889253818, "dur": 6, "ph": "X", "name": "ProcessMessages 6532", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889253826, "dur": 470, "ph": "X", "name": "ReadAsync 6532", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254314, "dur": 6, "ph": "X", "name": "ProcessMessages 4638", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254322, "dur": 197, "ph": "X", "name": "ReadAsync 4638", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254522, "dur": 3, "ph": "X", "name": "ProcessMessages 2284", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254527, "dur": 41, "ph": "X", "name": "ReadAsync 2284", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254571, "dur": 1, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254574, "dur": 36, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254614, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254616, "dur": 102, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254733, "dur": 1, "ph": "X", "name": "ProcessMessages 1307", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254735, "dur": 58, "ph": "X", "name": "ReadAsync 1307", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254796, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254798, "dur": 57, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254866, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254880, "dur": 37, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254920, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254922, "dur": 30, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254955, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254957, "dur": 31, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254991, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889254993, "dur": 32, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255045, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255047, "dur": 34, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255086, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255135, "dur": 48, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255186, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255189, "dur": 47, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255240, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255242, "dur": 32, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255279, "dur": 61, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255343, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255345, "dur": 48, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255396, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255398, "dur": 62, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255474, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889255476, "dur": 557, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889256045, "dur": 2, "ph": "X", "name": "ProcessMessages 1821", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889256048, "dur": 36, "ph": "X", "name": "ReadAsync 1821", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889256102, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889256104, "dur": 1578, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889257687, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889257691, "dur": 280, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889257986, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889257988, "dur": 392, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889258383, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889258386, "dur": 46, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889258461, "dur": 818, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889259283, "dur": 2, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889259286, "dur": 1481, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889260771, "dur": 2, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889260774, "dur": 71, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889260849, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889260851, "dur": 479, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889261334, "dur": 2, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889261337, "dur": 940, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889262281, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889262283, "dur": 939, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889263225, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889263236, "dur": 260, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889263527, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889263529, "dur": 1003, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889264537, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889264539, "dur": 94, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889264637, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889264639, "dur": 1863, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889266505, "dur": 18, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889266525, "dur": 612, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889267141, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889267143, "dur": 617, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889267791, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889267794, "dur": 273, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889268070, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889268072, "dur": 871, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889268947, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889268949, "dur": 260, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889269213, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889269214, "dur": 920, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889270138, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889270141, "dur": 235, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889270409, "dur": 863, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889271276, "dur": 1, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889271279, "dur": 41, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889271324, "dur": 1249, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889272577, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889272588, "dur": 331, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889272923, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889272926, "dur": 78, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889273008, "dur": 644, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889273654, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889273655, "dur": 34, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889273693, "dur": 998, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889274693, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889274696, "dur": 265, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889274964, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889274966, "dur": 1340, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889276310, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889276312, "dur": 616, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889276932, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889276934, "dur": 46, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889276984, "dur": 1043, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889278030, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889278032, "dur": 33, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889278069, "dur": 985, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889279084, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889279086, "dur": 250, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889279345, "dur": 9, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889279354, "dur": 386, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889279743, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889279745, "dur": 456, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889280206, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889280208, "dur": 688, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889280899, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889280901, "dur": 198, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889281102, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889281104, "dur": 990, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889282098, "dur": 2, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889282101, "dur": 67, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889282171, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889282173, "dur": 883, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889283061, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889283063, "dur": 48, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889283113, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889283116, "dur": 52, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889283171, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889283173, "dur": 35, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889283229, "dur": 49, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889283281, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889283283, "dur": 1508, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889284795, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889284798, "dur": 53, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889284855, "dur": 3108, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889287968, "dur": 3, "ph": "X", "name": "ProcessMessages 2541", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889287972, "dur": 508, "ph": "X", "name": "ReadAsync 2541", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889288484, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889288487, "dur": 33, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889288524, "dur": 1044, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889289572, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889289574, "dur": 251, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889289840, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889289842, "dur": 1005, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889290851, "dur": 2, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889290854, "dur": 1433, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889292293, "dur": 2, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889292296, "dur": 47, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889292347, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889292349, "dur": 800, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889293153, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889293156, "dur": 391, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889293551, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889293553, "dur": 599, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889294156, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889294158, "dur": 448, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889294636, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889294639, "dur": 678, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889295320, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889295321, "dur": 92, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889295419, "dur": 822, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889296245, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889296247, "dur": 722, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889296972, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889296974, "dur": 285, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889297264, "dur": 1020, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889298300, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889298302, "dur": 37, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889298342, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889298345, "dur": 31, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889298379, "dur": 1122, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889299504, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889299506, "dur": 226, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889299750, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889299752, "dur": 516, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889300272, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889300274, "dur": 37, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889300314, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889300315, "dur": 1040, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889301358, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889301360, "dur": 51, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889301415, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889301417, "dur": 1119, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889302539, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889302541, "dur": 153, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889302703, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889302705, "dur": 1467, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889304175, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889304178, "dur": 1021, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889305201, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889305203, "dur": 35, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889305243, "dur": 1142, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889306388, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889306391, "dur": 144, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889306538, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889306540, "dur": 1009, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889307552, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889307554, "dur": 300, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889307856, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889307858, "dur": 712, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889308593, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889308596, "dur": 63, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889308663, "dur": 833, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889309499, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889309501, "dur": 35, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889309551, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889309553, "dur": 753, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310310, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310313, "dur": 35, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310352, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310354, "dur": 38, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310396, "dur": 20, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310417, "dur": 39, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310459, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310461, "dur": 40, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310503, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889310505, "dur": 904, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889311413, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889311415, "dur": 47, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889311466, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889311468, "dur": 924, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889312396, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889312398, "dur": 273, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889312676, "dur": 629, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889313307, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889313310, "dur": 37, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889313351, "dur": 650, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889314009, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889314011, "dur": 37, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889314051, "dur": 855, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889314925, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889314928, "dur": 59, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889314990, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889314993, "dur": 65, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889315061, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889315063, "dur": 61, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889315127, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889315129, "dur": 1393, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889316551, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889316553, "dur": 938, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889317552, "dur": 420, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889317976, "dur": 56, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889318035, "dur": 5, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889318042, "dur": 188, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889318234, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889318237, "dur": 1283, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889319526, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889319528, "dur": 108, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322016, "dur": 23, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322045, "dur": 139, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322224, "dur": 11, "ph": "X", "name": "ProcessMessages 1248", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322249, "dur": 117, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322371, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322374, "dur": 91, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322469, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322472, "dur": 233, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322709, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322712, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322778, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889322780, "dur": 1105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889323895, "dur": 5, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889323902, "dur": 187, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324092, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324095, "dur": 107, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324205, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324208, "dur": 252, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324464, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324467, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324567, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324569, "dur": 319, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324892, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889324912, "dur": 195, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889325129, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889325134, "dur": 85, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889325222, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889325243, "dur": 75, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889325321, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889333991, "dur": 164, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889334160, "dur": 46, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889334211, "dur": 9735, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889343954, "dur": 9, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889343968, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889344032, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889344034, "dur": 483, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889344537, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889344540, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889344608, "dur": 2889, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889347518, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889347521, "dur": 34596, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889382126, "dur": 857, "ph": "X", "name": "ProcessMessages 1700", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889382988, "dur": 85, "ph": "X", "name": "ReadAsync 1700", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889383094, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889383099, "dur": 378, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889383480, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889383483, "dur": 226, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889383715, "dur": 1366, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889385087, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889385090, "dur": 745, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889385853, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889385856, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889385954, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889385956, "dur": 352, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889386313, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889386327, "dur": 371, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889386702, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889386704, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889386866, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889386868, "dur": 277, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889387149, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889387151, "dur": 865, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889388022, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889388025, "dur": 846, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889388876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889388878, "dur": 643, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889389526, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889389529, "dur": 556, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889390089, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889390092, "dur": 2005, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889392119, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889392123, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889392189, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889392192, "dur": 555, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889392757, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889392759, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889392946, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889392948, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393003, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393005, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393145, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393148, "dur": 168, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393320, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393323, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393393, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393395, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393473, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393476, "dur": 160, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393640, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393642, "dur": 121, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393768, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393770, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393828, "dur": 67, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393900, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889393999, "dur": 341, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889394344, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889394346, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889394536, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889394538, "dur": 275, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889394818, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889394820, "dur": 145, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889394968, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889394971, "dur": 337, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889395326, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889395329, "dur": 160, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889395494, "dur": 224, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889395722, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889395724, "dur": 215, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889395943, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889395945, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889396095, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889396097, "dur": 397, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889396498, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889396500, "dur": 133, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889396638, "dur": 591, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889397233, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889397236, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889397348, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889397350, "dur": 626, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889397980, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889397983, "dur": 247578, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889645581, "dur": 14, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889645598, "dur": 76, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889645679, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889645682, "dur": 67, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889645753, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889645756, "dur": 51, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889645812, "dur": 72, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889646154, "dur": 69, "ph": "X", "name": "ReadAsync 4138", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889646228, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889646230, "dur": 54, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889646288, "dur": 3083, "ph": "X", "name": "ProcessMessages 4842", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889649379, "dur": 2398, "ph": "X", "name": "ReadAsync 4842", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889651784, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889651788, "dur": 910, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889652704, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889652708, "dur": 769, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889653483, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889653486, "dur": 1364, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889654855, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889654858, "dur": 801, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889655664, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889655667, "dur": 371, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889656044, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889656046, "dur": 297, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889656349, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889656351, "dur": 3027, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889659396, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889659400, "dur": 167, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889659571, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889659573, "dur": 380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889659957, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889659960, "dur": 557, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889660522, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889660524, "dur": 396, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889660923, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889660925, "dur": 1080, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889662010, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889662012, "dur": 568, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889662646, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889662650, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889662708, "dur": 1394, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889664107, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889664109, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889664331, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889664334, "dur": 1710, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889666051, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889666055, "dur": 1721, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889667783, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889667786, "dur": 1001, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889668792, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889668795, "dur": 225, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889669024, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889669026, "dur": 317, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889669347, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889669349, "dur": 1810, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889671164, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889671167, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889671334, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889671337, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889671541, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889671543, "dur": 963, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889672511, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889672513, "dur": 1693, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889674213, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889674216, "dur": 412, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889674633, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889674636, "dur": 375, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889675016, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889675019, "dur": 928, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889675952, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889675955, "dur": 64, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676023, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676025, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676116, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676118, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676201, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676204, "dur": 201, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676409, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676412, "dur": 62, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676478, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676482, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676560, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676563, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676614, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676616, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676726, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676729, "dur": 136, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676869, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889676872, "dur": 155, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677031, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677034, "dur": 104, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677143, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677146, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677239, "dur": 66, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677310, "dur": 138, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677453, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677456, "dur": 61, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677530, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677533, "dur": 103, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677640, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677643, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677727, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677729, "dur": 178, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677911, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889677914, "dur": 125, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678043, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678057, "dur": 76, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678137, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678140, "dur": 61, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678205, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678207, "dur": 119, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678330, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678332, "dur": 64, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678413, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678415, "dur": 64, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678482, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678485, "dur": 49, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889678539, "dur": 955, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889679498, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889679500, "dur": 288, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889679790, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889679793, "dur": 129, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889679924, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418889679926, "dur": 513491, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890193427, "dur": 18, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890193447, "dur": 3726, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890197195, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890197200, "dur": 310361, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890507572, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890507576, "dur": 87, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890507668, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890507671, "dur": 53, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890507729, "dur": 56, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890507792, "dur": 65, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890507860, "dur": 22, "ph": "X", "name": "ProcessMessages 2629", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890507884, "dur": 10276, "ph": "X", "name": "ReadAsync 2629", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890518168, "dur": 677, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 70122, "tid": 12884901888, "ts": 1748418890518852, "dur": 6422, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 70122, "tid": 68, "ts": 1748418890580630, "dur": 6341, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 70122, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 70122, "tid": 8589934592, "ts": 1748418889065621, "dur": 337556, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 70122, "tid": 8589934592, "ts": 1748418889403181, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 70122, "tid": 8589934592, "ts": 1748418889403203, "dur": 5045, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 70122, "tid": 68, "ts": 1748418890586974, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 70122, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418888954955, "dur": 1573531, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418888979664, "dur": 73280, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418890528828, "dur": 10838, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418890532164, "dur": 5295, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 70122, "tid": 4294967296, "ts": 1748418890539754, "dur": 18, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 70122, "tid": 68, "ts": 1748418890586984, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748418889087949, "dur": 6650, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418889094638, "dur": 28680, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418889123411, "dur": 97, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748418889123509, "dur": 187, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418889124420, "dur": 480, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418889125799, "dur": 224, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418889126401, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418889126796, "dur": 34090, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418889161490, "dur": 1756, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418889165029, "dur": 72213, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418889241326, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418889241500, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748418889241555, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748418889247794, "dur": 452, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418889249643, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748418889249931, "dur": 2617, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748418889253347, "dur": 822, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748418889293114, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748418889293172, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748418889123705, "dur": 193742, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418889317459, "dur": 1200300, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418890517939, "dur": 109, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748418890518104, "dur": 1053, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748418889123615, "dur": 193854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889317508, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748418889317814, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_9909B9D5D2D6D40E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889318011, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_9909B9D5D2D6D40E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889318070, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889318247, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889318670, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_E69ADDE2B61683F5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889318844, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889318980, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889319119, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889319242, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889319373, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889319533, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889319624, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748418889320270, "dur": 5190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889325461, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889325927, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889326031, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889326564, "dur": 17620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889344185, "dur": 583, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889344790, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889344876, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889344967, "dur": 3320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889348289, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889348360, "dur": 9946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889358307, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889358562, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889358637, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889358724, "dur": 1184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889359935, "dur": 3331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889363267, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889364125, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889365246, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889367898, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889368239, "dur": 1118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889369390, "dur": 3909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889373300, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889373637, "dur": 2474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889376112, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889376271, "dur": 2280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889378552, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889378675, "dur": 1755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889380431, "dur": 740, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889381331, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889381745, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889381853, "dur": 14325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889396179, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889396255, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748418889396401, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889396912, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889396985, "dur": 252226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889649213, "dur": 3196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889652416, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889652514, "dur": 4179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889656694, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889656824, "dur": 4541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889661419, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889663494, "dur": 3193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889666688, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889666785, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889666901, "dur": 3104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889670006, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889670124, "dur": 3347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889673517, "dur": 3331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748418889676849, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889677284, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889677398, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889677464, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748418889677515, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889677779, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889677861, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889678031, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889678186, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889678362, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889678460, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889678544, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748418889678606, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889678786, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748418889678863, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889678968, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889679068, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748418889679183, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889679352, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748418889679455, "dur": 1196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748418889680684, "dur": 837066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889123622, "dur": 193883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889317520, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418889317810, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889318078, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889318325, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889318389, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_C9DCA0B70AE22DB0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889318474, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889318701, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889318978, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889319044, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889319177, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889319244, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889319361, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889319573, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418889319823, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_0118987859331F44.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889320122, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418889320347, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889320941, "dur": 711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889321710, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418889321884, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418889322037, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889322095, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889322427, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418889322629, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748418889322806, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889323243, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748418889323523, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889323580, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889323878, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889324665, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889324775, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889325074, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889325129, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889325883, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889325998, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889326062, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889326150, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889326228, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889326312, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748418889326405, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889326891, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889327082, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889327191, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748418889327418, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889327500, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889328884, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889329932, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889331014, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889332341, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889333546, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889335050, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889336576, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889337975, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889339168, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889341151, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ShaderGUI/ShadingModels/SimpleLitGUI.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748418889340320, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889342104, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889343407, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889344649, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889346045, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889347342, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889348632, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889349782, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889350971, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889352141, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889353453, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889354761, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889356093, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889357425, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889358827, "dur": 949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889359802, "dur": 16493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889376296, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889376837, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_4EC1FE8B3807FE28.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889376926, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889377619, "dur": 5062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889382682, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889382845, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748418889383287, "dur": 2521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889385809, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889386020, "dur": 2710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889388731, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889388875, "dur": 4665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889393541, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889393978, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889394318, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889394382, "dur": 1753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889396136, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889396228, "dur": 252888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889649123, "dur": 3930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889653054, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889653157, "dur": 3436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889656595, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889656722, "dur": 3304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889660028, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889660128, "dur": 2593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889662722, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889662809, "dur": 2437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889665280, "dur": 3276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889668593, "dur": 3361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889671955, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889672072, "dur": 3505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889675578, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889675652, "dur": 4701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748418889680425, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748418889680851, "dur": 836833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889123634, "dur": 193884, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889317523, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889318010, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889318235, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_888C5CF0B798DF8B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889318309, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889318364, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889318715, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889318838, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889318946, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889319003, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889319120, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889319201, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889319556, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889319800, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_F0DEE4EE0970DD2B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889320098, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748418889320355, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889320573, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748418889320783, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_FB7A597403E5FDBB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889320944, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889321678, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889321740, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889322047, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889322728, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889323170, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748418889323333, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889323405, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889323472, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889323727, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889324678, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889324756, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889324823, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889325162, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889325426, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889325818, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889326009, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889326079, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889326161, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889326242, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889326345, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889326921, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889327145, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748418889327421, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889327480, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889328818, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889329863, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889330874, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889332228, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889333253, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889334763, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889336217, "dur": 1499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889337717, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889338974, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889341120, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Editor/SkinningModule/IMGUI/RectSelectionTool.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748418889340101, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889341833, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889343198, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889344420, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889345758, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889347090, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889348380, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889349573, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889350731, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889351912, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889353220, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889354512, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889355753, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889357218, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889358598, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889359642, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889359899, "dur": 1190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889361090, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889361588, "dur": 1538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889363177, "dur": 1638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889364858, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889365563, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889366367, "dur": 3010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889369378, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889369804, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889370434, "dur": 1998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889372433, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889372638, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889373407, "dur": 4540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889377948, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889378375, "dur": 2066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889380442, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889380598, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889380659, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_E9EA3AB45900FF1D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889380848, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889381006, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_DD9C61FBB7D63C03.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889381142, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_D292A1F7EB1601C3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889381254, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889381333, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889381738, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889381824, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889382518, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889382962, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889383360, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889383809, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748418889384632, "dur": 2413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889387046, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889387175, "dur": 2403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889389579, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889389721, "dur": 3078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889392804, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889393059, "dur": 2195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889395255, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889395654, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889395853, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889395940, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889396178, "dur": 252945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889649128, "dur": 5129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889654259, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889654341, "dur": 3665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889658007, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889658096, "dur": 2198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889660296, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748418889660377, "dur": 8505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889668917, "dur": 3268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889672218, "dur": 3315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889675598, "dur": 5085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748418889680764, "dur": 836937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889123651, "dur": 193881, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889317537, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889318001, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889318054, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1996541149DA13E0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889318240, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889318313, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E782B6141ECD6A34.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889318408, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889318652, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889318840, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889318897, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889318986, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889319079, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889319208, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889319342, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889319407, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889319586, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889319893, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889320033, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889320401, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889320577, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748418889320889, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889321126, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889321743, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748418889322050, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889322957, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889323358, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889323418, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889323685, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889324475, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889324534, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889324672, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889324744, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889324903, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889325150, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889325731, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889325787, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889325876, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889325948, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889326018, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889326095, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889326172, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889326455, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889326536, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889326926, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748418889328119, "dur": 705, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/DataManager.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748418889327256, "dur": 3660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889330917, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889332235, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889333339, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889334866, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889336332, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889337832, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889339043, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889341103, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Editor/SkinningModule/BaseTool.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748418889340172, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889341937, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889343284, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889344505, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889345882, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889347199, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889348524, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889349707, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889350878, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889352060, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889353372, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889354635, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889355945, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889357281, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889358629, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889359695, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889359995, "dur": 4560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889364555, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889364757, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889365639, "dur": 3151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889368790, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889369201, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889369955, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889370690, "dur": 7316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889378006, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889378463, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889379049, "dur": 7540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889386590, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889386723, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_BF69964832659484.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889386777, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889386842, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889387738, "dur": 4715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889392454, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889392943, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Editor.ref.dll_7BE565C70AE586E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889393076, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889393809, "dur": 1809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889395619, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889395782, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.Editor.ref.dll_B6E543B7064A5CCA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748418889395867, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889396173, "dur": 23490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889419664, "dur": 229462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889649132, "dur": 3291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889652424, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889652528, "dur": 3619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889656148, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889656275, "dur": 3851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889660127, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889660331, "dur": 6276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889666644, "dur": 3543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889670239, "dur": 3093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889673333, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889673403, "dur": 3731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748418889677135, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889677377, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889677717, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889677878, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889678073, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748418889678452, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889678615, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889678762, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889678923, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889679143, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889679208, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889679318, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889679462, "dur": 1300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748418889680763, "dur": 836916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889123665, "dur": 193876, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889317546, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889318018, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889318214, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_97FEC325F9337F2C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889318309, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889318369, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_CAA42EBCF9A9A6C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889318492, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889318644, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_72608D0D80FBEA70.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889318825, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889318886, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889318958, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889319062, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889319137, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889319246, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889319329, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889319393, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889319639, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889319773, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889320047, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889320200, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748418889320364, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889320863, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889321026, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889321221, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748418889321515, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889321716, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889322167, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889322256, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889322710, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889322961, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889323126, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748418889323413, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889323566, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889323635, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889324070, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889324157, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889324483, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889324549, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889324659, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889324760, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889325463, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889325807, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889325896, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889326041, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889326136, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889326193, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889326267, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889326548, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748418889327246, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Weapons/WeaponData.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748418889328484, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Utils/ObjectPool.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748418889327246, "dur": 4759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889332005, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889333199, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889334657, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889336118, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889337680, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889338893, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889340040, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889341156, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/RendererFeatures/ScreenSpaceShadowsEditor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748418889341156, "dur": 1969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889343126, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889344355, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889345715, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889347009, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889348332, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889349569, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889350713, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889351884, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889353228, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889354543, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889355789, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889357241, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889358604, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889359677, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889359886, "dur": 1676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418889361562, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889361952, "dur": 1418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889363420, "dur": 1619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889365039, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889365107, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889365809, "dur": 3030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418889368840, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889369208, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889370100, "dur": 2124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418889372225, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889372366, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889373374, "dur": 2921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418889376295, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889376681, "dur": 922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889377639, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889378057, "dur": 15615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418889393673, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889394078, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889394659, "dur": 1921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418889396635, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889396834, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418889397441, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889397545, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418889397865, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889398129, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748418889398268, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418889398818, "dur": 54, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418889398895, "dur": 794871, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748418890194913, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418890194881, "dur": 2095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418890197752, "dur": 278, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418890508403, "dur": 395, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748418890198060, "dur": 310755, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748418890514194, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418890514180, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418890514352, "dur": 3173, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748418890517530, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889123676, "dur": 193874, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889317557, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889317998, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889318092, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A49EDD7A339F9A85.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889318311, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889318378, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889318583, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_565918C7359E0F61.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889318832, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889319137, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889319228, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889319416, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889319787, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418889320448, "dur": 4776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889325225, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889325829, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418889326259, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889326456, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418889326798, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418889327021, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748418889327266, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889328515, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889329462, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889330485, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889331721, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889332877, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889334164, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889335698, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889337286, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889338588, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889339688, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889341136, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ShaderGUI/ShaderGraphUnlitGUI.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748418889340792, "dur": 1893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889342685, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889343987, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889345321, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889345431, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889345513, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889346744, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889348127, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889349358, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889350499, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889351662, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889352959, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889354321, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889355496, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889356923, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889358299, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889359504, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889359882, "dur": 2818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889362701, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889363164, "dur": 1591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889364795, "dur": 2632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889367427, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889367920, "dur": 1353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889369309, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889370087, "dur": 1993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889372080, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889372473, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Editor.ref.dll_8237F4420D536756.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889372577, "dur": 5022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889377600, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889377717, "dur": 2775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889380493, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889381074, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Editor.ref.dll_5A2087A6EAF2CE17.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889381178, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889381325, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Runtime.ref.dll_23B941DDE597517C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889381428, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889382070, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889382483, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889382539, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889383167, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889383596, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889384338, "dur": 2962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889387301, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889387553, "dur": 2619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889390184, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889390346, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889392824, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889392986, "dur": 2128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889395115, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889395229, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Editor.ref.dll_3C7C2928F291E90C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748418889395291, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889395437, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889395494, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889395632, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889395753, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889395843, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889396172, "dur": 12772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889408946, "dur": 10712, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889419659, "dur": 229473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889649140, "dur": 3558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889652699, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889652781, "dur": 3383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889656165, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889656326, "dur": 5474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889661836, "dur": 3190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889665027, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889665214, "dur": 3373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889668622, "dur": 3225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889671904, "dur": 3191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889675096, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748418889675203, "dur": 5395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748418889680665, "dur": 837104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889123689, "dur": 193871, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889317567, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889318038, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_25D10CC8709418E4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889318267, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889318610, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889318855, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889318934, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889318995, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889319096, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889319194, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889319566, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889319675, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889319947, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889320127, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748418889320413, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889320723, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748418889320927, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748418889321102, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748418889321312, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748418889321537, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748418889321727, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889322018, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889322370, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889323340, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889324038, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889324208, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889324521, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889324586, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889324661, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889324768, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889325248, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889325768, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748418889326041, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889326130, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889326217, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889326280, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889326784, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889327210, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748418889327411, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889327468, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889328876, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889329918, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889330996, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889332333, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889333508, "dur": 1516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889335024, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889336564, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889337970, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889339120, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889341128, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/VFXGraph/VFXAbstractParticleURPLitOutput.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748418889340242, "dur": 1791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889342033, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889343330, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889344543, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889345958, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889347268, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889348574, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889349759, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889350915, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889352081, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889353410, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889354733, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889356044, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889357418, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889358818, "dur": 958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889359816, "dur": 4865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889364692, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889365043, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889365787, "dur": 4844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889370632, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889371191, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748418889371723, "dur": 2639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889374363, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889374743, "dur": 3985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748418889378729, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889378903, "dur": 604, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889646159, "dur": 1055, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889379809, "dur": 267491, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748418889649111, "dur": 4639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889653803, "dur": 3164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889656968, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889657061, "dur": 4079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889661141, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889661234, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889663609, "dur": 2133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889665795, "dur": 3194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889668990, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889669066, "dur": 3263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889672330, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889672398, "dur": 3571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889675970, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748418889676041, "dur": 4726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748418889680810, "dur": 836921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889123701, "dur": 193873, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889317576, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889318028, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889318242, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D0749362471C98A9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889318592, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_2EC518F8CEEB6CA9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889318815, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889318874, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889318951, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889319045, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889319153, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889319239, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889319300, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889319389, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889319608, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889319830, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889319916, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889320603, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748418889320876, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889321130, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889322252, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889322489, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889323323, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889323567, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889323660, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889324031, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889324125, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889324215, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748418889324445, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889324628, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889324693, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889324793, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889325118, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889325371, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889325797, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889325892, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889325949, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889326022, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889326111, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889326168, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889326251, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889326305, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748418889326421, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889326502, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889326843, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889327006, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748418889327274, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889328597, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889329554, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889330578, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889331883, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889333032, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889334360, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889335823, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889337404, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889338666, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889339789, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889341144, "dur": 661, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/ShaderGraph/Targets/UniversalDecalSubTarget.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748418889340911, "dur": 1925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889342836, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889344160, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889345467, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889346700, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889348091, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889349299, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889350442, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889351641, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889352915, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889354305, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889355470, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889356837, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889358212, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889359427, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889359846, "dur": 9151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889368998, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889369439, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889370282, "dur": 1944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889372227, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889372607, "dur": 2864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889375472, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889375629, "dur": 4333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889379962, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889380257, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889380859, "dur": 2383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889383243, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889383427, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889383942, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889384609, "dur": 3045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889387655, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889388024, "dur": 2792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889390817, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889390955, "dur": 2525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889393481, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889393618, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889394544, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889394776, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889395188, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889395439, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889395654, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889395730, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889395856, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889396151, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889396640, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748418889396797, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889397445, "dur": 251666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889649120, "dur": 2992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889652113, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889652242, "dur": 3360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889655670, "dur": 5072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889660800, "dur": 2957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889663811, "dur": 2848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889666660, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889666802, "dur": 3047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889669850, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889669908, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889673370, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889673449, "dur": 3214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748418889676794, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889676954, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889677179, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748418889677241, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889677543, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889677792, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748418889677901, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748418889678031, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889678285, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889678427, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889678500, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889678655, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889678741, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889679204, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889679352, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889679440, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418889680445, "dur": 833840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748418890514304, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748418890514288, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748418890514504, "dur": 3154, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748418890523457, "dur": 2132, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 70122, "tid": 68, "ts": 1748418890588462, "dur": 3121, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 70122, "tid": 68, "ts": 1748418890592023, "dur": 3170, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 70122, "tid": 68, "ts": 1748418890575887, "dur": 20475, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}