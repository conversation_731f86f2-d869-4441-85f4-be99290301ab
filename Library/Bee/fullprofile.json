{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 70593, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 70593, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 70593, "tid": 44, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 70593, "tid": 44, "ts": 1748419863103743, "dur": 2953, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 70593, "tid": 44, "ts": 1748419863123850, "dur": 2001, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 70593, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 70593, "tid": 1, "ts": 1748419856152291, "dur": 26051, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748419856178348, "dur": 85334, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 70593, "tid": 1, "ts": 1748419856263692, "dur": 123551, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 70593, "tid": 44, "ts": 1748419863125863, "dur": 45, "ph": "X", "name": "", "args": {}}, {"pid": 70593, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856148875, "dur": 89013, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856237893, "dur": 6832013, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856239260, "dur": 26461, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856265728, "dur": 4342, "ph": "X", "name": "ProcessMessages 8174", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856270078, "dur": 132, "ph": "X", "name": "ReadAsync 8174", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856270216, "dur": 10, "ph": "X", "name": "ProcessMessages 8127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856270228, "dur": 68, "ph": "X", "name": "ReadAsync 8127", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856270300, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856270302, "dur": 4528, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856274835, "dur": 7, "ph": "X", "name": "ProcessMessages 8123", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856274843, "dur": 95, "ph": "X", "name": "ReadAsync 8123", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856274942, "dur": 2, "ph": "X", "name": "ProcessMessages 1373", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856274945, "dur": 69, "ph": "X", "name": "ReadAsync 1373", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275017, "dur": 1, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275020, "dur": 77, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275101, "dur": 2, "ph": "X", "name": "ProcessMessages 1118", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275104, "dur": 68, "ph": "X", "name": "ReadAsync 1118", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275176, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275179, "dur": 67, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275255, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275256, "dur": 68, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275328, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275331, "dur": 55, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275390, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856275392, "dur": 641, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856276037, "dur": 3, "ph": "X", "name": "ProcessMessages 4526", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856276041, "dur": 1756, "ph": "X", "name": "ReadAsync 4526", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856277803, "dur": 16, "ph": "X", "name": "ProcessMessages 2186", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856277820, "dur": 89, "ph": "X", "name": "ReadAsync 2186", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856277912, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856277914, "dur": 93, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278012, "dur": 2, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278015, "dur": 109, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278127, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278130, "dur": 74, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278206, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278209, "dur": 79, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278292, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278295, "dur": 84, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278383, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278385, "dur": 225, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278613, "dur": 2, "ph": "X", "name": "ProcessMessages 1276", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278631, "dur": 171, "ph": "X", "name": "ReadAsync 1276", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278835, "dur": 2, "ph": "X", "name": "ProcessMessages 1091", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278838, "dur": 42, "ph": "X", "name": "ReadAsync 1091", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278884, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278886, "dur": 47, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278935, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278937, "dur": 34, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278974, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856278976, "dur": 101, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279093, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279096, "dur": 75, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279175, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279178, "dur": 78, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279259, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279261, "dur": 94, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279358, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279361, "dur": 66, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279432, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856279435, "dur": 769, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856280208, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856280210, "dur": 108, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856280324, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856280327, "dur": 1720, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282051, "dur": 2, "ph": "X", "name": "ProcessMessages 1581", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282054, "dur": 165, "ph": "X", "name": "ReadAsync 1581", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282223, "dur": 1, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282226, "dur": 64, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282292, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282294, "dur": 63, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282361, "dur": 1, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282364, "dur": 61, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282430, "dur": 96, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282545, "dur": 1, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282548, "dur": 42, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282592, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282594, "dur": 32, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282631, "dur": 80, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282714, "dur": 19, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282737, "dur": 134, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282875, "dur": 9, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856282887, "dur": 2481, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285370, "dur": 6, "ph": "X", "name": "ProcessMessages 8181", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285378, "dur": 56, "ph": "X", "name": "ReadAsync 8181", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285438, "dur": 35, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285478, "dur": 76, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285557, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285560, "dur": 43, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285607, "dur": 92, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285702, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285705, "dur": 71, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285779, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285781, "dur": 47, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285831, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285834, "dur": 66, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285922, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285924, "dur": 59, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285986, "dur": 2, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856285989, "dur": 90, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286082, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286085, "dur": 85, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286173, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286176, "dur": 76, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286256, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286258, "dur": 55, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286353, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286357, "dur": 439, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286882, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286885, "dur": 93, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286982, "dur": 2, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856286985, "dur": 56, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287045, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287047, "dur": 61, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287127, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287129, "dur": 58, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287191, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287193, "dur": 160, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287357, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287360, "dur": 79, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287443, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287479, "dur": 58, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287540, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287543, "dur": 221, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287768, "dur": 2, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287771, "dur": 63, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287838, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287841, "dur": 147, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287992, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856287995, "dur": 51, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288050, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288052, "dur": 74, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288129, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288132, "dur": 67, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288219, "dur": 2, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288222, "dur": 54, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288279, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288282, "dur": 111, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288397, "dur": 2, "ph": "X", "name": "ProcessMessages 1407", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856288400, "dur": 3548, "ph": "X", "name": "ReadAsync 1407", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856291955, "dur": 3, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856291959, "dur": 161, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292124, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292127, "dur": 56, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292187, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292190, "dur": 59, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292252, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292255, "dur": 117, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292377, "dur": 2, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292380, "dur": 111, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292496, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292516, "dur": 66, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292586, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292589, "dur": 67, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292660, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292662, "dur": 47, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292712, "dur": 41, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292756, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292771, "dur": 36, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292811, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292814, "dur": 76, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292893, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856292896, "dur": 739, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856293638, "dur": 6, "ph": "X", "name": "ProcessMessages 8165", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856293646, "dur": 544, "ph": "X", "name": "ReadAsync 8165", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294194, "dur": 5, "ph": "X", "name": "ProcessMessages 4612", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294201, "dur": 78, "ph": "X", "name": "ReadAsync 4612", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294284, "dur": 1, "ph": "X", "name": "ProcessMessages 1162", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294287, "dur": 73, "ph": "X", "name": "ReadAsync 1162", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294375, "dur": 2, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294379, "dur": 134, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294517, "dur": 1, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294520, "dur": 43, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294566, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294568, "dur": 40, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294611, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294613, "dur": 174, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294792, "dur": 2, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294795, "dur": 44, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294842, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294844, "dur": 35, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294883, "dur": 36, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856294940, "dur": 79, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295022, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295025, "dur": 38, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295068, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295070, "dur": 39, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295112, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295113, "dur": 79, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295195, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295198, "dur": 78, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295279, "dur": 1, "ph": "X", "name": "ProcessMessages 1199", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295281, "dur": 36, "ph": "X", "name": "ReadAsync 1199", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295320, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295323, "dur": 63, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295389, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295391, "dur": 51, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295446, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295448, "dur": 57, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295510, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295513, "dur": 82, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295609, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295612, "dur": 73, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295690, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295692, "dur": 58, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295755, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856295757, "dur": 375, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856296137, "dur": 2, "ph": "X", "name": "ProcessMessages 1937", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856296141, "dur": 765, "ph": "X", "name": "ReadAsync 1937", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856296911, "dur": 6, "ph": "X", "name": "ProcessMessages 6273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856296918, "dur": 67, "ph": "X", "name": "ReadAsync 6273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856296989, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856296992, "dur": 626, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856297640, "dur": 7, "ph": "X", "name": "ProcessMessages 5139", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856297879, "dur": 45, "ph": "X", "name": "ReadAsync 5139", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856297928, "dur": 4, "ph": "X", "name": "ProcessMessages 3268", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856297933, "dur": 34, "ph": "X", "name": "ReadAsync 3268", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856297985, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856297987, "dur": 29, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298020, "dur": 558, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298582, "dur": 6, "ph": "X", "name": "ProcessMessages 5666", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298590, "dur": 39, "ph": "X", "name": "ReadAsync 5666", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298632, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298634, "dur": 92, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298728, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298731, "dur": 34, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298768, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298770, "dur": 37, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298811, "dur": 35, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298851, "dur": 44, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298898, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298900, "dur": 46, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298951, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856298953, "dur": 45, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299001, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299003, "dur": 32, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299039, "dur": 33, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299076, "dur": 66, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299145, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299147, "dur": 31, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299182, "dur": 32, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299217, "dur": 34, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299255, "dur": 36, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299295, "dur": 64, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299360, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299363, "dur": 30, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299396, "dur": 72, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299471, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299473, "dur": 27, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299504, "dur": 80, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299586, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299588, "dur": 29, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299633, "dur": 29, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299665, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299666, "dur": 31, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299701, "dur": 27, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299731, "dur": 64, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299799, "dur": 69, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299870, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299872, "dur": 27, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299903, "dur": 38, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856299943, "dur": 65, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300011, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300012, "dur": 30, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300047, "dur": 39, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300090, "dur": 39, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300132, "dur": 29, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300176, "dur": 30, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300209, "dur": 28, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300242, "dur": 29, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300274, "dur": 55, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300334, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300336, "dur": 47, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300385, "dur": 1, "ph": "X", "name": "ProcessMessages 121", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300387, "dur": 54, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300446, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300449, "dur": 102, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300554, "dur": 2, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300557, "dur": 127, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300689, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300692, "dur": 72, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300767, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300770, "dur": 51, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300824, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300827, "dur": 51, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300882, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300884, "dur": 50, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300937, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856300939, "dur": 71, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856301014, "dur": 55, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856301073, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856301075, "dur": 64, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856301143, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856301146, "dur": 48, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856301213, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856301216, "dur": 50, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856301270, "dur": 174, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856301449, "dur": 1199, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856302653, "dur": 2, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856302655, "dur": 1456, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856304118, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856304121, "dur": 351, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856304476, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856304478, "dur": 505, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856304987, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856304990, "dur": 37, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856305030, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856305033, "dur": 966, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856306003, "dur": 2, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856306007, "dur": 1094, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856307131, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856307135, "dur": 222, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856307362, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856307364, "dur": 1157, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856308526, "dur": 2, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856308529, "dur": 843, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856309392, "dur": 2, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856309395, "dur": 2516, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856311918, "dur": 23, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856311946, "dur": 817, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856313459, "dur": 2, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856313463, "dur": 142, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856313626, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856313629, "dur": 386, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856314033, "dur": 18, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856314052, "dur": 280, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856314336, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856314338, "dur": 765, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856315108, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856315111, "dur": 425, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856315541, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856315544, "dur": 721, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856316270, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856316273, "dur": 1359, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856317638, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856317641, "dur": 4509, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856322158, "dur": 3, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856322162, "dur": 684, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856322853, "dur": 17, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856322873, "dur": 74, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856322949, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856322952, "dur": 83, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856323040, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856323042, "dur": 1058, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856324104, "dur": 31, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856324171, "dur": 1074, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856325259, "dur": 2, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856325263, "dur": 62, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856325329, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856325331, "dur": 1029, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856326365, "dur": 2, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856326368, "dur": 1119, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856327507, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856327509, "dur": 315, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856327827, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856327829, "dur": 873, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856329266, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856329269, "dur": 624, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856329923, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856329925, "dur": 111, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856330060, "dur": 487, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856330565, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856330567, "dur": 602, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856331173, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856331175, "dur": 308, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856331486, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856331488, "dur": 484, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856331976, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856331978, "dur": 1798, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856333780, "dur": 56, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856333838, "dur": 302, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856334144, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856334146, "dur": 645, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856334796, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856335373, "dur": 65, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856335473, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856335475, "dur": 1107, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856336587, "dur": 2, "ph": "X", "name": "ProcessMessages 1027", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856336590, "dur": 48, "ph": "X", "name": "ReadAsync 1027", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856336641, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856336642, "dur": 85, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856336731, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856336734, "dur": 75, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856336811, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856336832, "dur": 1883, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856338721, "dur": 3, "ph": "X", "name": "ProcessMessages 1565", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856338724, "dur": 188, "ph": "X", "name": "ReadAsync 1565", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856338917, "dur": 2, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856338920, "dur": 716, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856339639, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856339642, "dur": 31, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856339678, "dur": 901, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856340614, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856340617, "dur": 68, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856340691, "dur": 833, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856341527, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856341529, "dur": 287, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856341819, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856341820, "dur": 628, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856342452, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856342454, "dur": 634, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856343097, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856343100, "dur": 1238, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856344342, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856344344, "dur": 362, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856344710, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856344712, "dur": 375, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856345091, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856345093, "dur": 614, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856345712, "dur": 2, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856345715, "dur": 54, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856345773, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856345794, "dur": 954, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856346751, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856346753, "dur": 485, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856347242, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856347245, "dur": 284, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856347533, "dur": 406, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856347943, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856347945, "dur": 45, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856347993, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856347994, "dur": 37, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856348034, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856348036, "dur": 739, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856348778, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856348780, "dur": 324, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856349124, "dur": 26, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856349152, "dur": 574, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856349744, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856349746, "dur": 41, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856349791, "dur": 667, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856350484, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856350487, "dur": 67, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856350558, "dur": 3644, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856354208, "dur": 4706, "ph": "X", "name": "ProcessMessages 3229", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856358968, "dur": 100, "ph": "X", "name": "ReadAsync 3229", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359081, "dur": 18, "ph": "X", "name": "ProcessMessages 2745", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359102, "dur": 76, "ph": "X", "name": "ReadAsync 2745", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359180, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359183, "dur": 98, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359293, "dur": 2, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359297, "dur": 107, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359426, "dur": 1, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359429, "dur": 71, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359502, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856359505, "dur": 1055, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856360570, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856360572, "dur": 73, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856360649, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856360651, "dur": 1328, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856362018, "dur": 3, "ph": "X", "name": "ProcessMessages 1610", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856362023, "dur": 771, "ph": "X", "name": "ReadAsync 1610", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856362798, "dur": 8, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856362842, "dur": 1895, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856365299, "dur": 622, "ph": "X", "name": "ProcessMessages 2544", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856365927, "dur": 341, "ph": "X", "name": "ReadAsync 2544", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856366275, "dur": 8, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856366297, "dur": 1391, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856367696, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856367700, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856367871, "dur": 4, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856367876, "dur": 392, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856368274, "dur": 16, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856368418, "dur": 203, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856368626, "dur": 646, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856369279, "dur": 1380, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856370667, "dur": 6, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856370717, "dur": 148, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856370870, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856370874, "dur": 80, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856370959, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856370962, "dur": 567, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856371535, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856371539, "dur": 307, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856371852, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856371855, "dur": 149, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372010, "dur": 151, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372166, "dur": 106, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372276, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372303, "dur": 76, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372413, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372416, "dur": 80, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372500, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372503, "dur": 189, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372696, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372699, "dur": 75, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372779, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372781, "dur": 110, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372895, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856372898, "dur": 98, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373000, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373002, "dur": 187, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373193, "dur": 12, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373210, "dur": 112, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373326, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373330, "dur": 71, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373404, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373407, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373484, "dur": 8, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373496, "dur": 77, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373578, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373580, "dur": 73, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373657, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373659, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373713, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373715, "dur": 273, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373992, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856373996, "dur": 79, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374078, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374080, "dur": 79, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374164, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374180, "dur": 93, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374277, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374280, "dur": 59, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374343, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374345, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374411, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374414, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374475, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374478, "dur": 140, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374621, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374624, "dur": 71, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374712, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374714, "dur": 113, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374838, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374841, "dur": 103, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374949, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856374951, "dur": 78, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375034, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375036, "dur": 76, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375131, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375133, "dur": 112, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375249, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375252, "dur": 208, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375465, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375467, "dur": 370, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375844, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375847, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375948, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856375950, "dur": 156, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376110, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376113, "dur": 98, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376215, "dur": 13, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376232, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376338, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376341, "dur": 136, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376480, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376483, "dur": 271, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376758, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856376760, "dur": 1285, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856378053, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856378056, "dur": 12329, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856390393, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856390410, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856390623, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856390626, "dur": 2069, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856392702, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856392706, "dur": 157, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856392867, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856392870, "dur": 4360, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856397237, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856397240, "dur": 27169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856424418, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856424421, "dur": 4931, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856429393, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856429440, "dur": 14537, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856443986, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856443990, "dur": 1879, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856445877, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856445881, "dur": 754, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856446642, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856446645, "dur": 5267, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856451920, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856451923, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856452099, "dur": 4, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856452105, "dur": 781, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856452893, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856452897, "dur": 89, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856452991, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856452994, "dur": 1767, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856454767, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856454770, "dur": 190, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856454966, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856454970, "dur": 2041, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856457020, "dur": 83, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856457107, "dur": 719, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856457831, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856457833, "dur": 110, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856458033, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856458038, "dur": 215, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856458256, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856458258, "dur": 839, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856459100, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856459102, "dur": 2774, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856461883, "dur": 5, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856461889, "dur": 228, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856462122, "dur": 902, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856463303, "dur": 172, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856463480, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856463492, "dur": 446, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856463943, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856463947, "dur": 1253, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856465206, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856465208, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856465381, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856465453, "dur": 556, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856466014, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856466017, "dur": 549, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856466569, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856466571, "dur": 1215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856471112, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856471119, "dur": 144, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856471282, "dur": 23, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856471320, "dur": 63, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856471387, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856471390, "dur": 859, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856472258, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856472262, "dur": 306714, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856778986, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856778990, "dur": 74, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856779070, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856779073, "dur": 62, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856779141, "dur": 75, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856779236, "dur": 72, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856779452, "dur": 135, "ph": "X", "name": "ReadAsync 4138", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856779592, "dur": 62, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856779659, "dur": 3088, "ph": "X", "name": "ProcessMessages 4842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856782754, "dur": 5348, "ph": "X", "name": "ReadAsync 4842", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856788111, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856788116, "dur": 1573, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856789695, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856789698, "dur": 584, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856790287, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856790289, "dur": 529, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856790823, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856790826, "dur": 2927, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856793775, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856793779, "dur": 343, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856794129, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856794131, "dur": 2074, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856796214, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856796226, "dur": 2780, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856799015, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856799019, "dur": 4923, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856803951, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856803955, "dur": 417, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856804378, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856804381, "dur": 3692, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856808082, "dur": 7, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856808091, "dur": 3175, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856811277, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856811281, "dur": 1348, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856812635, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856812654, "dur": 226, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856812883, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856812886, "dur": 1510, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856814403, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856814406, "dur": 168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856814620, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856814623, "dur": 11025, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856825659, "dur": 6, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856825671, "dur": 138, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856825816, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856825819, "dur": 889, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856826713, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856826719, "dur": 143, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856826866, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856826869, "dur": 278, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827154, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827158, "dur": 97, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827259, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827262, "dur": 382, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827648, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827661, "dur": 148, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827815, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827818, "dur": 76, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827898, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856827901, "dur": 211, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856828117, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856828123, "dur": 343, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856828474, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856828483, "dur": 136, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856828624, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856828626, "dur": 212, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856828842, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856828844, "dur": 164, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829016, "dur": 5, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829023, "dur": 241, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829272, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829279, "dur": 199, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829485, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829487, "dur": 187, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829684, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829691, "dur": 216, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829912, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856829915, "dur": 147, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856830192, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856830195, "dur": 570, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856830776, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856830790, "dur": 1104, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856831995, "dur": 53, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856832053, "dur": 1038, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856833097, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856833100, "dur": 131, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419856833237, "dur": 4641784, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419861475034, "dur": 176, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419861475217, "dur": 6972, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419861482196, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419861482202, "dur": 1561147, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043359, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043364, "dur": 97, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043469, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043496, "dur": 110, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043612, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043615, "dur": 81, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043702, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043704, "dur": 79, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043788, "dur": 26, "ph": "X", "name": "ProcessMessages 2629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863043816, "dur": 10412, "ph": "X", "name": "ReadAsync 2629", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863054238, "dur": 5, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863054245, "dur": 976, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863055229, "dur": 60, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863055292, "dur": 754, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863056054, "dur": 619, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 70593, "tid": 12884901888, "ts": 1748419863056680, "dur": 13109, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 70593, "tid": 44, "ts": 1748419863125912, "dur": 6274, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 70593, "tid": 8589934592, "ts": 1748419856143819, "dur": 243464, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748419856387288, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 70593, "tid": 8589934592, "ts": 1748419856387298, "dur": 6235, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 70593, "tid": 44, "ts": 1748419863132189, "dur": 23, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 70593, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419856049114, "dur": 7026044, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419856064116, "dur": 70893, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419863075670, "dur": 22502, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419863087576, "dur": 6726, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 70593, "tid": 4294967296, "ts": 1748419863098336, "dur": 26, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 70593, "tid": 44, "ts": 1748419863132214, "dur": 1988, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748419856208572, "dur": 6777, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419856215389, "dur": 34086, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419856249582, "dur": 121, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748419856249703, "dur": 192, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419856250710, "dur": 14351, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_811EC2CD9E0B859F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856265457, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856265784, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856265839, "dur": 4987, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856271278, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856271508, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856271757, "dur": 3670, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856276051, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856276111, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856276410, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419856278205, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_58CF8A58239EF61C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856278325, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748419856278808, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419856279287, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856280023, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856280112, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419856282414, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C5B5631EE4DDD44F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856283240, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_FB7A597403E5FDBB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856283399, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856284948, "dur": 1001, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419856287894, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748419856289053, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419856292935, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748419856293239, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Editor.ref.dll_8237F4420D536756.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856294175, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856296210, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419856296561, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419856301125, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419856307563, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419856313289, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748419856315467, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419856321896, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419856326680, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748419856335397, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748419856352628, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748419856249905, "dur": 114953, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419856364870, "dur": 6691205, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419863056077, "dur": 144, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419863056461, "dur": 85, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419863056618, "dur": 2184, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748419856249814, "dur": 115068, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856364919, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748419856365309, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_695CAE35FCE15689.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856365609, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_02D5D9FA54EC16C9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856365715, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_C9DCA0B70AE22DB0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856365916, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E01BA708133EFFD8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856366270, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856366487, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_57AB718CCA0A791C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856366667, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856366771, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856366930, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856367205, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_C572D659EF0D75FE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856367347, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C9222BF87218E07B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856367898, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856368037, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C55FB41B3BE345A6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856368173, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856368278, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_9C8827F217EB9A8E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856368522, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856368690, "dur": 1256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748419856369960, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748419856370129, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748419856370372, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856370600, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419856370880, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856371011, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_FB7A597403E5FDBB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856371182, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856371294, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419856371618, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856371693, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748419856372019, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856372188, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856372321, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856372421, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419856372479, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856372544, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856372660, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419856372782, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856373230, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856373312, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856373389, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419856373442, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856373527, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419856373854, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748419856373913, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856373995, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856374112, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856374212, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856374328, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856374438, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419856374756, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856374883, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856375012, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856375110, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856375248, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856375365, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856375479, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856375581, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856375706, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856375915, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856376345, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856376644, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856376808, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856376991, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856377236, "dur": 1904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856379141, "dur": 1640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856380782, "dur": 1866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856382648, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856386274, "dur": 636, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Runner/Microsoft.AspNetCore.HttpOverrides.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419856384530, "dur": 2381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856386911, "dur": 2701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856389612, "dur": 2149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856391761, "dur": 1858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856393620, "dur": 8270, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856401891, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856403766, "dur": 805, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/FreeformPathPresets.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419856403258, "dur": 2080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856405339, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856406489, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856407720, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856408989, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856410548, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856411796, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856413284, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856414755, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856416604, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856418119, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856419726, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856421402, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856422901, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856424435, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856424510, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856426008, "dur": 2027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856428036, "dur": 2201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856430238, "dur": 2269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856432508, "dur": 1794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856434302, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856436057, "dur": 1906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856437964, "dur": 2333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856440297, "dur": 1785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856442082, "dur": 1998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856444080, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856445594, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856446613, "dur": 3545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856450163, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856450344, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856450508, "dur": 3676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856454185, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856454432, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856454506, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856454754, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856454922, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856455668, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856456217, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856456594, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856457280, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856457681, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Runtime.ref.dll_DEAB70CB54ED1324.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856458003, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856458197, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856460591, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856461094, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Editor.ref.dll_5A2087A6EAF2CE17.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856461214, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856461435, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856461814, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856462070, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856462302, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856462638, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856462700, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419856463630, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856463708, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856465341, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856465558, "dur": 2305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856467920, "dur": 2739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856470660, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856471013, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856471379, "dur": 311052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856782439, "dur": 8826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856791266, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856791336, "dur": 4097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856795471, "dur": 9900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856805428, "dur": 6299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856811729, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856811966, "dur": 3430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856815397, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856815844, "dur": 3631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856819480, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419856819830, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856820145, "dur": 5515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856825694, "dur": 7818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419856833669, "dur": 6222428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856249819, "dur": 115089, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856364919, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748419856365458, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A49EDD7A339F9A85.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856365604, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_BDE55B526F99CFBD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856365683, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856365820, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856365878, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_AF28A3E31D6399E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856365946, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_72608D0D80FBEA70.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856366290, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856366514, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0DCCD87795E03C42.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856366669, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856366777, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6E4BCB18FBDC6C97.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856366969, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_44A3657E0D5133B5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856367443, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856367969, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856368148, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856368222, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_06EBD843CA969E56.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856368286, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6AFECB9047521D1C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856368519, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856368643, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DA21C63E26AB31DF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856368777, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856368866, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856369026, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419856370025, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748419856370348, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856370492, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419856370817, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856370926, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856371070, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856371182, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856371355, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748419856371561, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748419856371848, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748419856371926, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856372003, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856372172, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856372278, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856372401, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419856372689, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856372804, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856372975, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419856373269, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856373369, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419856373446, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856373573, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419856373946, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856374029, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856374146, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856374244, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856374338, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419856374392, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856374527, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856374669, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419856374746, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856374824, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856374918, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856375028, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856375159, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856375256, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856375388, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856375497, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856375608, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856375773, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856375987, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856376538, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856376643, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856376808, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856376879, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856377240, "dur": 881, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/SceneManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419856378973, "dur": 674, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Core/DataManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419856377001, "dur": 5399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856382400, "dur": 1897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856384298, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856386184, "dur": 2991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856389180, "dur": 2285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856391465, "dur": 1984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856393449, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856395303, "dur": 2233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856397537, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856399139, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856400605, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856402215, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856403759, "dur": 797, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_4_3.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419856403526, "dur": 2080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856405606, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856406754, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856407994, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856409288, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856410769, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856412033, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856413473, "dur": 1577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856415051, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856416984, "dur": 1543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856418527, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856420210, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856421874, "dur": 1501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856423375, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856424775, "dur": 1603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856426379, "dur": 2099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856428478, "dur": 2014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856430493, "dur": 2347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856432840, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856434673, "dur": 1849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856436526, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856438459, "dur": 2301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856440761, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856442563, "dur": 2100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856444665, "dur": 935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856445601, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856445671, "dur": 3615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856449287, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856449725, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856450759, "dur": 2857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856453617, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856454125, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856454393, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856455783, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856456099, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_1DF7BBC55D029A75.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856456192, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856456577, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856457461, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856457705, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856457819, "dur": 1702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856459522, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856459761, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856459974, "dur": 3054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856463029, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856463200, "dur": 2728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856465929, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856466087, "dur": 958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856467046, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856467441, "dur": 1705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856469147, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856469937, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856470064, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Psdimporter.Editor.ref.dll_47CE2970C8879D28.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856470162, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856470305, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419856470516, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856471558, "dur": 310941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856782502, "dur": 7651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856790154, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856790326, "dur": 3820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856794147, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856794375, "dur": 4260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856798637, "dur": 981, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856799633, "dur": 5761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856805395, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856805475, "dur": 3124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856808600, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856808760, "dur": 4650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856813417, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856813484, "dur": 5051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856818536, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856818701, "dur": 8281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419856826983, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856827136, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419856827207, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856827331, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856827420, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748419856827471, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856827644, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856827764, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856827880, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856827998, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856828123, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856828252, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856828407, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856828469, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856828695, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856828780, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856828846, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419856828912, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856828999, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419856829067, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856829246, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419856829371, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856829525, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856829712, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856829774, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748419856829917, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856830095, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856830201, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748419856830387, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856830479, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856830568, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856830825, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748419856830888, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856831284, "dur": 2331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419856833646, "dur": 6222456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856249834, "dur": 115085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856364926, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856365332, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_5E5DD76230405390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856365452, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_566A8230A4757E41.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856365822, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3C55960BA9CB8745.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856365885, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856365982, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D3397F2308FCA23D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856366309, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856366550, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AE9EB63A7D0EC42E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856366708, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856366800, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4CE0620618E29952.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856366961, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CB9F951B7F6C1B8D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856367422, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856367918, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_C5ED7F2D0850708B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856368098, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856368257, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_098A885D15C50AF5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856368552, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_694A7936D4FF139B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856368723, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856368820, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419856370008, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856370584, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856370696, "dur": 21354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856392051, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856392677, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856392737, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856392838, "dur": 4573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856397412, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856397502, "dur": 26363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856423866, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856424237, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856424315, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856424417, "dur": 4452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856428870, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856428958, "dur": 14603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856443563, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856444025, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856444114, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856444270, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856445559, "dur": 3509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856449069, "dur": 816, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856449898, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856450006, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856450655, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856450737, "dur": 2130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856452868, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856453274, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_E30EE1A78470D274.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856453395, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856453567, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856453780, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856454065, "dur": 1225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856455290, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856455392, "dur": 5856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856461249, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856461545, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856461940, "dur": 3814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856465755, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856466207, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419856466395, "dur": 1715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856468111, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856468596, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856470888, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856471384, "dur": 311078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856782464, "dur": 3298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856785764, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856786005, "dur": 3939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856789946, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856790201, "dur": 4413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856794615, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856794742, "dur": 5965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856800746, "dur": 5804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856806552, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856806662, "dur": 7670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856814367, "dur": 4794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856819162, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856819522, "dur": 4690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856824213, "dur": 805, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419856825019, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856825193, "dur": 8381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419856833658, "dur": 6222360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856249846, "dur": 115085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856364937, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BCFAD3B56B3994AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856365367, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D970682A82B6594B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856365514, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37EA32C02CB3EC76.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856365813, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D56D3910CFAEA861.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856365963, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_E69ADDE2B61683F5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856366141, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856366383, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B92AE864B27146B1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856366622, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856366760, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_F0E1D61C670FB050.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856366915, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856367054, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_DBBCB0624138746C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856367763, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856367884, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6726DD74EEA0E240.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856368069, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856368198, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_51CCB6F491E5150D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856368424, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856368581, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FA76BD7B9240C406.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856368752, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856368863, "dur": 1118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419856369996, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748419856370219, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856370436, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856370556, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856370621, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419856370814, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856370913, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856371034, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856371215, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856371406, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419856371873, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856371954, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856372107, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856372224, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856372348, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856372479, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856372571, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856372697, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856372819, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419856373367, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856373513, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856373767, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748419856373872, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748419856373978, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856374073, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419856374450, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856374591, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856374697, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856374796, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856374895, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856375022, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856375121, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856375228, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856375352, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856375467, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856375577, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856375700, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856375874, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419856375952, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856376348, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419856378150, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856378421, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856380169, "dur": 1915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856382085, "dur": 1948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856384034, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856386265, "dur": 632, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Runner/Microsoft.AspNetCore.Connections.Abstractions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419856385883, "dur": 3110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856388994, "dur": 2298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856391292, "dur": 1889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856393182, "dur": 1889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856395072, "dur": 2298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856397370, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856398978, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856400448, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856402141, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856403725, "dur": 794, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.State/FlowStateTransition.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419856403434, "dur": 2104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856405538, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856406714, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856407968, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856409259, "dur": 1499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856410759, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856412040, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856413455, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856414995, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856416788, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856418390, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856420003, "dur": 1631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856421635, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856423163, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856424652, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856426193, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856428339, "dur": 1988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856430327, "dur": 2258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856432586, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856434453, "dur": 1822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856436275, "dur": 1821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856438097, "dur": 2343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856440440, "dur": 1821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856442262, "dur": 1992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856444274, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856445573, "dur": 8021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856453595, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856454089, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856454321, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856455236, "dur": 634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856455904, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419856456311, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856457135, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856457666, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856457810, "dur": 3280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748419856461091, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856461259, "dur": 952, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856779340, "dur": 854, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856462573, "dur": 317679, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748419856782426, "dur": 8597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856791024, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856791095, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856791303, "dur": 4518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856795822, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856795887, "dur": 8996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856804885, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856805020, "dur": 4476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856809497, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856809623, "dur": 6162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856815786, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856815953, "dur": 5157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856821111, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856821216, "dur": 7690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419856828907, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856829122, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856829183, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748419856829279, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856829463, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856829545, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419856829638, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856829887, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856829947, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856830005, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748419856830174, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856830353, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748419856830420, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856830543, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748419856830608, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856830686, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856830785, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856830911, "dur": 2622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419856833563, "dur": 6222433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856249860, "dur": 115083, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856364992, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_E60CB16AD82AB3A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856365444, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D75DA07EBCD9E7E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856365892, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B66FF45CB39FB62A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856366017, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856366305, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EE9561D4D14CF77B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856366567, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856366650, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_2ABBB7F8F8E50753.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856366783, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856366894, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D8A74C26D737DACC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856367174, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856367487, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FF45E5DA5403F41D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856368066, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856368187, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0384474257303014.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856368364, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856368538, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E6481EF78247372F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856368693, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856368786, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856368962, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856369209, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419856370088, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856370212, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748419856370469, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856370634, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748419856370915, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856371041, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856371232, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419856371627, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419856371966, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856372114, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856372227, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748419856372523, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856372647, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748419856372707, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419856373255, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856373444, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856373610, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419856373967, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856374088, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419856374158, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856374250, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856374328, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419856374394, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856374568, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856374679, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856374812, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856374935, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856375042, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856375169, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856375278, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856375397, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419856375452, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856375542, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856375633, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856375780, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856375922, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419856375981, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856376442, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856376713, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856376829, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856376939, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856377190, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856378864, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856380543, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856382421, "dur": 1901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856384323, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856386237, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Runner/Grpc.Core.Api.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419856386152, "dur": 2646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856388799, "dur": 2450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856391249, "dur": 1904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856393154, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856394936, "dur": 2340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856397277, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856398865, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856400274, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856401996, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856403746, "dur": 798, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.State/StateGraph.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419856403275, "dur": 2131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856405406, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856406558, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856407817, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856409026, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856410597, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856411828, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856413261, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856414668, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856416555, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856418070, "dur": 1704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856419775, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856421440, "dur": 1477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856422917, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856424465, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856424540, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856426065, "dur": 2052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856428118, "dur": 2062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856430181, "dur": 2211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856432393, "dur": 1857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856434251, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856435992, "dur": 1889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856437882, "dur": 2327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856440211, "dur": 1787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856441999, "dur": 2039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856444039, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856445470, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856445807, "dur": 3402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856449210, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856449556, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856450100, "dur": 1751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856451852, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856452234, "dur": 1246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856453481, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856453581, "dur": 5573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856459155, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856459607, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856459700, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856460196, "dur": 3987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856464184, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856464402, "dur": 1181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856465620, "dur": 1892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856467512, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856467729, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419856467956, "dur": 2422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856470379, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856470983, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856471208, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856471378, "dur": 311071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856782457, "dur": 2960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856785420, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856785568, "dur": 4701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856790270, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856790421, "dur": 4719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856795141, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856795246, "dur": 5541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856800788, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856800996, "dur": 6932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856807929, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856808127, "dur": 5058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856813186, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856813268, "dur": 3274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856816543, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856816673, "dur": 8950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419856825625, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856825863, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856826130, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856826232, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856826326, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856826390, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856826479, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856827106, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856827287, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419856827355, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856827491, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856827541, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419856827608, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856828053, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856828311, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856828379, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856828443, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856828570, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856828692, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856828803, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856828932, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856829062, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856829144, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856829317, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856829435, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856829614, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856829721, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856829830, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856829929, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856830116, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856830171, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856830254, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856830330, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856830457, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856830512, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856830596, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856830647, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856830801, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856830865, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856831479, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419856831609, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419856831683, "dur": 6222883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419863054597, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419863054570, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419863054804, "dur": 1045, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419863055855, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856249876, "dur": 115122, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856365003, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4E60E71D29DF5CF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856365348, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_24C2E717FF39AE8E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856365752, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0FCBDDCA92BB6E2A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856366098, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856366339, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD8CC2F62B9395CF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856366590, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856366694, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_6D11E34B66675FA2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856366851, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856367134, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_94987F7885BC0C0A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856367842, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856368004, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_4ED40EB55A1A5774.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856368153, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856368273, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_E51247239B34C7FA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856368514, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856368617, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_01A1682AC3FD20EE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856368763, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856368873, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856369062, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_E711E874CC38399E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856369140, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5041E5295CD553D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856369235, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856370312, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856370440, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856370893, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856371019, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856371203, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856371568, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856371639, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856371945, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856372070, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856372338, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856372439, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856372570, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856372654, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856372755, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748419856373146, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856373208, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856373279, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856373521, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856373790, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748419856373872, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748419856373947, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856374070, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856374207, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856374277, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856374417, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856374635, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856374753, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856374818, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856374882, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856374990, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856375089, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856375215, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856375338, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856375454, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856375551, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856375661, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856375899, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856376260, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419856376341, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856376625, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856376805, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856376992, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856377226, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856379087, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856380674, "dur": 1873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856382547, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856384395, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856386243, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.ide.visualstudio@2.0.22/Editor/SimpleJSON.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419856386243, "dur": 3195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856389439, "dur": 2187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856391627, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856393542, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856395451, "dur": 2429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856397885, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856399243, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856400689, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856402284, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856403721, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_0_0.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419856403647, "dur": 2054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856405701, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856406886, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856408135, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856409448, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856410917, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856412167, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856413665, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856415268, "dur": 1838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856417106, "dur": 1533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856418640, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856420279, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856421963, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856423425, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856424889, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856426603, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856428676, "dur": 1888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856430565, "dur": 2428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856432993, "dur": 1831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856434825, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856436541, "dur": 1997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856438539, "dur": 2237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856440776, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856442556, "dur": 2054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856444616, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856445527, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856445598, "dur": 3012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856448611, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856449123, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856449250, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856450155, "dur": 3081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856453237, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856453650, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856453829, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856453885, "dur": 1324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856455210, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856455780, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856455952, "dur": 1241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856457193, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856457296, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856458348, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856458483, "dur": 2202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856460686, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856461164, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856461557, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856461788, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856461850, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856462146, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856462315, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856462651, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856462740, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856463322, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856465630, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856465819, "dur": 1105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856466925, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856467193, "dur": 1586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856468780, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856469270, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_CBF917C274624EE2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856469453, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856469676, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856469904, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419856470269, "dur": 1118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856471388, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856471483, "dur": 310956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856782445, "dur": 9671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856792117, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856792197, "dur": 4505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856796703, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856796865, "dur": 6860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856803727, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856803838, "dur": 3892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856807774, "dur": 5554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856813370, "dur": 6716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856820088, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856820236, "dur": 11006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419856831243, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856831424, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856831515, "dur": 2118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419856833681, "dur": 6222346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856249888, "dur": 115123, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856365017, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A095384AAA891558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856365774, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1D599B1BE51D0E3D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856365873, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_EBB973DC47E5C128.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856365969, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856366076, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AB6C0CE8644C7690.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856366343, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856366566, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3BE300C145E6CD05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856366713, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856366808, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_18E117194F700B02.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856366935, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856367228, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_36BAC8769ABF4647.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856367854, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856368030, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_062344F6065DC349.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856368174, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856368302, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A3456C0678C8BF8A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856368523, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856368681, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_00585CD2BC036044.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856368778, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856369033, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_F0DEE4EE0970DD2B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856369134, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_58CF8A58239EF61C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856369241, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748419856369900, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856370141, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856370302, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856370359, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856370450, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856370858, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856370967, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748419856371311, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856371367, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856371745, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748419856371956, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856372033, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856372098, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856372200, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856372356, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856372494, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856372618, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856372721, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856372866, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748419856373210, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748419856373436, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856373544, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856373902, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856373966, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856374049, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856374199, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856374302, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856374392, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856374741, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856374872, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856374984, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856375078, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856375197, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856375329, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856375440, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856375549, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856375638, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856375815, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856375965, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856376019, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856376488, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856376597, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856376784, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856376924, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419856376994, "dur": 1163, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Weapons/WeaponData.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419856378841, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Assets/Scripts/Utils/ObjectPool.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419856376994, "dur": 5834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856382828, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856386236, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Runner/Microsoft.AspNetCore.DataProtection.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419856384703, "dur": 2695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856387399, "dur": 2515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856389915, "dur": 2223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856392138, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856393970, "dur": 2037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856396008, "dur": 2210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856398219, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856399528, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856401203, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856403733, "dur": 799, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@14.1.0/Editor/2D/ShaderGraph/Targets/SpriteSubTargetUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419856402722, "dur": 2126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856404849, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856406057, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856407275, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856408514, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856409950, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856411243, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856412666, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856414062, "dur": 1590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856415653, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856417445, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856418985, "dur": 1691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856420677, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856422328, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856423805, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856425189, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856426902, "dur": 2409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856429312, "dur": 1907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856431220, "dur": 2261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856433482, "dur": 1813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856435295, "dur": 1872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856437168, "dur": 2098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856439271, "dur": 2093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856441364, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856443168, "dur": 1930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856445099, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856445691, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419856446569, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856447051, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856447512, "dur": 1602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856449115, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856449207, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856449842, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856449924, "dur": 1068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856451032, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856451117, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856451290, "dur": 2740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419856454031, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856454340, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856454647, "dur": 1225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419856455873, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856456364, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856456434, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856456802, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419856457456, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856457710, "dur": 6665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419856464376, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856464570, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856464652, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856464910, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856465081, "dur": 1422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419856466503, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856466638, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419856467553, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856467736, "dur": 3849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419856471661, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419856471861, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419856472617, "dur": 97, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419856472751, "dur": 5002770, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419861477838, "dur": 298, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419861477785, "dur": 3050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419861482120, "dur": 562, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419863043842, "dur": 604, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419861482737, "dur": 1561731, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419863054574, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419863054538, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419863054822, "dur": 1100, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419856249900, "dur": 115120, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856365022, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_745F5267FA46D4F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856365389, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_463E4C8A1250AF13.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856365836, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_2EC518F8CEEB6CA9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856365924, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_2EC518F8CEEB6CA9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856365995, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EBEC3F4C28775516.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856366325, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856366536, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_26090D0E01C0AD21.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856366702, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856366792, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856366947, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856367264, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_84881A4F783B22C4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856367334, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EC7717AF26C5E26D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856367864, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856368017, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6DE2D788C488B3AA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856368186, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856368317, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2635E125E5E1564B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856368537, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856368682, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_A63AF7640A6D88E2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856368804, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856369019, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419856370098, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856370770, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856370864, "dur": 19114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856389986, "dur": 830, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856390836, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856390924, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856391029, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856393044, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856393143, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748419856393203, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856393310, "dur": 1704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856395015, "dur": 2320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856397335, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856398959, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856400456, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856402151, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856403776, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Ports/InvalidOutputWidget.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419856403485, "dur": 2080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856405566, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856406687, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856407915, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856409189, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856410690, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856411950, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856413369, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856414856, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856416727, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856418379, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856420038, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856421697, "dur": 1553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856423250, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856424734, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856426310, "dur": 2074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856428384, "dur": 2031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856430416, "dur": 2252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856432668, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856434504, "dur": 1905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856436410, "dur": 1917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856438327, "dur": 2333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856440661, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856442501, "dur": 2075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856444577, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856445759, "dur": 5407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856451167, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856451806, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856451901, "dur": 1437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856453339, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856453454, "dur": 2953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856456408, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856456558, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856457233, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856458339, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856458554, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856459653, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856460044, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_3971D385965D884C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856460099, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856460166, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856460268, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856460623, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419856461272, "dur": 6191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856467464, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856467697, "dur": 3467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856471165, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856471288, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856471436, "dur": 310990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856782435, "dur": 6001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856788438, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856788728, "dur": 5045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856793795, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856794183, "dur": 5927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856800151, "dur": 3975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856804127, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856804449, "dur": 4590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856809040, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856809320, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856809405, "dur": 5513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856814919, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856815012, "dur": 3989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856819002, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856819194, "dur": 4120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856823316, "dur": 1569, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419856824900, "dur": 8562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419856833541, "dur": 6222419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419863066260, "dur": 2794, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 70593, "tid": 44, "ts": 1748419863137342, "dur": 6668, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 70593, "tid": 44, "ts": 1748419863144240, "dur": 6072, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 70593, "tid": 44, "ts": 1748419863116448, "dur": 40662, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}