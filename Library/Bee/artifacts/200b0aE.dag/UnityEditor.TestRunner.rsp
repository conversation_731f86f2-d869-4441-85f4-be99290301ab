-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.ref.dll"
-define:UNITY_2022_3_55
-define:UNITY_2022_3
-define:UNITY_2022
-define:TUANJIE_1_5_2
-define:TUANJIE_1_5
-define:TUANJIE_1
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:TUANJIE_2022_3_OR_NEWER
-define:TUANJIE_1_0_OR_NEWER
-define:TUANJIE_1_1_OR_NEWER
-define:TUANJIE_1_2_OR_NEWER
-define:TUANJIE_1_3_OR_NEWER
-define:TUANJIE_1_4_OR_NEWER
-define:TUANJIE_1_5_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_OSX
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:MINIGAME_SUBPLATFORM_WEIXIN
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEditor.Graphs.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.HMISimulatorModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AndroidAppViewModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AutoStreamingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.InfinityModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.OcclusionCullingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.OpenHarmonyJSModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TextureManagerModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/netstandard.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.AppContext.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Buffers.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Collections.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ComponentModel.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Console.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Data.Common.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Globalization.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.Pipes.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Linq.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Memory.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.Ping.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.Requests.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.Security.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.Sockets.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ObjectModel.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Reflection.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Resources.Reader.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Resources.Writer.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Security.Claims.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Security.Principal.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Security.SecureString.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Text.Encoding.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Threading.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Threading.Thread.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Threading.Timer.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.ValueTuple.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Xml.XPath.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Microsoft.CSharp.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/mscorlib.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.ComponentModel.Composition.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Core.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Data.DataSetExtensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Data.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Drawing.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.IO.Compression.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Net.Http.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Numerics.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Numerics.Vectors.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Runtime.Serialization.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Transactions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Xml.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.Mdb.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.Pdb.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.Rocks.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.ref.dll"
-analyzer:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/Analytics/AnalyticsReporter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/Analytics/AnalyticsTestCallback.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/Analytics/RunFinishedData.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/Analytics/TestTreeData.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/CallbacksDelegator.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/CallbacksDelegatorListener.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/CallbacksHolder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ExecutionSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/Filter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ICallbacks.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ICallbacksDelegator.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ICallbacksHolder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/IErrorCallbacks.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ITestAdaptor.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ITestAdaptorFactory.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ITestResultAdaptor.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ITestRunnerApi.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ITestRunSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/ITestTreeRebuildCallbacks.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/RunState.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/TestAdaptor.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/TestAdaptorFactory.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/TestMode.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/TestResultAdaptor.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/TestRunnerApi.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/Api/TestStatus.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/AssemblyInfo.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineParser/CommandLineOption.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineParser/CommandLineOptionSet.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineParser/ICommandLineOption.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/Executer.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/ExecutionSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/ExitCallbacks.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/ExitCallbacksDataHolder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/ISettingsBuilder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/LogSavingCallbacks.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/LogWriter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/ResultsSavingCallbacks.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/ResultsWriter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/RunData.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/RunSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/SettingsBuilder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/SetupException.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/CommandLineTest/TestStarter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/AssetsDatabaseHelper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/GuiHelper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/IAssetsDatabaseHelper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/IGuiHelper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestListBuilder/RenderingOptions.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestListBuilder/ResultSummarizer.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestListBuilder/TestFilterSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestListBuilder/TestTreeViewBuilder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestListGuiHelper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestListTreeView/Icons.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestListTreeView/TestListTreeViewDataSource.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestListTreeView/TestListTreeViewGUI.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestListTreeView/TestTreeViewItem.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestRunnerResult.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/TestRunnerUIFilter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/UITestRunnerFilter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/Views/EditModeTestListGUI.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/Views/PlayModeTestListGUI.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/GUI/Views/TestListGUIBase.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/NUnitExtension/Attributes/AssetPipelineIgnore.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/NUnitExtension/Attributes/ITestPlayerBuildModifier.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/NUnitExtension/Attributes/TestPlayerBuildModifierAttribute.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/NUnitExtension/TestRunnerStateSerializer.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/RequireApiProfileAttribute.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/RequirePlatformSupportAttribute.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestBuildAssemblyFilter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/AttributeFinderBase.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/DelayedCallback.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/EditModeLauncher.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/EditModeLauncherContextSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/AndroidPlatformSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/ApplePlatformSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/HMIAndroidPlatformSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/IPlatformSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/LuminPlatformSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/PlatformSpecificSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/StadiaPlatformSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/SwitchPlatformSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/UwpPlatformSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlatformSetup/XboxOnePlatformSetup.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlayerLauncher.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlayerLauncherBuildOptions.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlayerLauncherContextSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlayerLauncherTestRunSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PlaymodeLauncher.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PostbuildCleanupAttributeFinder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/PrebuildSetupAttributeFinder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/RemotePlayerLogController.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/RemotePlayerTestController.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/RemoteTestResultReciever.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/RuntimeTestLauncherBase.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestLaunchers/TestLauncherBase.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestResultSerializer.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/BuildActionTaskBase.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/BuildTestTreeTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/CleanupVerificationTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/FileCleanupVerifierTaskBase.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/LegacyEditModeRunTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/LegacyPlayerRunTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/LegacyPlayModeRunTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/PerformUndoTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/PrebuildSetupTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/RegisterFilesForCleanupVerificationTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/SaveModiedSceneTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/SaveUndoIndexTask.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/Tasks/TestTaskBase.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/TestJobData.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/TestJobDataHolder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/TestJobRunner.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRun/TestRunCanceledException.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Callbacks/EditModeRunnerCallback.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Callbacks/RerunCallback.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Callbacks/RerunCallbackData.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Callbacks/RerunCallbackInitializer.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Callbacks/TestRunnerCallback.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Callbacks/WindowResultUpdater.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Callbacks/WindowResultUpdaterDataHolder.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/EditModePCHelper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/EditModeRunner.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/EditmodeWorkItemFactory.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/EditorEnumeratorTestWorkItem.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/EnumeratorStepHelper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Messages/EnterPlayMode.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Messages/ExitPlayMode.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Messages/RecompileScripts.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Messages/WaitForDomainReload.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/CachingTestListProvider.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/EditorAssembliesProxy.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/EditorAssemblyWrapper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/EditorCompilationInterfaceProxy.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/EditorLoadedTestAssemblyProvider.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/IEditorAssembliesProxy.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/IEditorCompilationInterfaceProxy.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/IEditorLoadedTestAssemblyProvider.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/ITestListCache.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/ITestListCacheData.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/ITestListProvider.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/TestListCache.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/TestListCacheData.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/TestListJob.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunner/Utils/TestListProvider.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunnerWindow.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestRunnerWindowSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestSettings/ITestSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestSettings/ITestSettingsDeserializer.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestSettings/TestSettings.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/TestSettings/TestSettingsDeserializer.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/ITestRunnerApiMapper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/IUtpLogger.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/IUtpMessageReporter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/Message.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/TestFinishedMessage.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/TestPlanMessage.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/TestRunnerApiMapper.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/TestStartedMessage.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/TestState.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/UnityTestProtocolListener.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/UnityTestProtocolStarter.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/UtpDebuglogger.cs"
"Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/UnityTestProtocol/UtpMessageReporter.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"