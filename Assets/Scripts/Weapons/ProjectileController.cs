using UnityEngine;

namespace RoguelikeGame.Weapons
{
    /// <summary>
    /// 投射物控制器，处理投射物的移动、碰撞和生命周期
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D))]
    [RequireComponent(typeof(Collider2D))]
    public class ProjectileController : MonoBehaviour
    {
        [Header("投射物设置")]
        public float lifeTime = 5f;
        public bool destroyOnHit = true;
        public int maxPierceCount = 0;
        public LayerMask obstacleLayerMask = 1 << 9; // 障碍物层

        // 投射物属性
        private WeaponController weaponController;
        private float damage;
        private float maxRange;
        private Vector3 startPosition;
        private int currentPierceCount;

        // 组件引用
        private Rigidbody2D rb;
        private Collider2D col;
        private TrailRenderer trail;
        private SpriteRenderer spriteRenderer;

        // 已击中的目标列表（防止重复伤害）
        private System.Collections.Generic.HashSet<GameObject> hitTargets;

        private void Awake()
        {
            rb = GetComponent<Rigidbody2D>();
            col = GetComponent<Collider2D>();
            trail = GetComponent<TrailRenderer>();
            spriteRenderer = GetComponent<SpriteRenderer>();

            // 如果没有SpriteRenderer，创建一个
            if (spriteRenderer == null)
            {
                spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
                // 创建一个简单的方形精灵
                spriteRenderer.sprite = CreateSimpleSprite();
                spriteRenderer.color = Color.yellow; // 投射物使用黄色
            }

            hitTargets = new System.Collections.Generic.HashSet<GameObject>();
        }

        private void Start()
        {
            // 设置自动销毁
            Destroy(gameObject, lifeTime);
        }

        private void Update()
        {
            CheckRange();
        }

        public void Initialize(WeaponController weapon, float range, float projectileDamage)
        {
            weaponController = weapon;
            maxRange = range;
            damage = projectileDamage;
            startPosition = transform.position;
        }

        private void CheckRange()
        {
            // 检查是否超出最大射程
            if (Vector3.Distance(startPosition, transform.position) >= maxRange)
            {
                DestroyProjectile();
            }
        }

        private void OnTriggerEnter2D(Collider2D other)
        {
            // 检查是否击中障碍物
            if (IsInLayerMask(other.gameObject.layer, obstacleLayerMask))
            {
                HitObstacle(other);
                return;
            }

            // 检查是否击中敌人（通过名称检查，避免标签问题）
            if (other.name.Contains("Enemy") || other.GetComponent<RoguelikeGame.Enemies.EnemyController>() != null)
            {
                HitEnemy(other.gameObject);
            }
        }

        private void HitEnemy(GameObject enemy)
        {
            // 防止重复伤害同一个敌人
            if (hitTargets.Contains(enemy))
                return;

            hitTargets.Add(enemy);

            // 计算攻击方向
            Vector2 attackDirection = rb.velocity.normalized;

            // 通过武器控制器造成伤害
            if (weaponController != null)
            {
                weaponController.DamageEnemy(enemy, attackDirection);
            }

            // 检查穿透
            if (maxPierceCount > 0 && currentPierceCount < maxPierceCount)
            {
                currentPierceCount++;
            }
            else if (destroyOnHit)
            {
                DestroyProjectile();
            }
        }

        private void HitObstacle(Collider2D obstacle)
        {
            // 击中障碍物，销毁投射物
            DestroyProjectile();
        }

        private void DestroyProjectile()
        {
            // 停止移动
            rb.velocity = Vector2.zero;

            // 禁用碰撞器防止重复触发
            col.enabled = false;

            // 如果有拖尾效果，等待拖尾消失
            if (trail != null && trail.time > 0)
            {
                trail.emitting = false;
                Destroy(gameObject, trail.time);
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private bool IsInLayerMask(int layer, LayerMask layerMask)
        {
            return (layerMask.value & (1 << layer)) != 0;
        }

        #region 特殊投射物效果

        /// <summary>
        /// 设置投射物为追踪类型
        /// </summary>
        public void SetHoming(Transform target, float homingStrength = 5f)
        {
            if (target != null)
            {
                StartCoroutine(HomingBehavior(target, homingStrength));
            }
        }

        private System.Collections.IEnumerator HomingBehavior(Transform target, float strength)
        {
            while (target != null && gameObject != null)
            {
                Vector2 direction = (target.position - transform.position).normalized;
                rb.velocity = Vector2.Lerp(rb.velocity, direction * rb.velocity.magnitude, strength * Time.deltaTime);

                // 更新旋转
                float angle = Mathf.Atan2(rb.velocity.y, rb.velocity.x) * Mathf.Rad2Deg;
                transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);

                yield return null;
            }
        }

        /// <summary>
        /// 设置投射物为弹跳类型
        /// </summary>
        public void SetBouncing(int maxBounces = 3)
        {
            maxPierceCount = maxBounces;
            destroyOnHit = false;
        }

        /// <summary>
        /// 设置投射物为爆炸类型
        /// </summary>
        public void SetExploding(float explosionRadius = 2f, GameObject explosionEffect = null)
        {
            StartCoroutine(ExplodingBehavior(explosionRadius, explosionEffect));
        }

        private System.Collections.IEnumerator ExplodingBehavior(float radius, GameObject effect)
        {
            yield return new WaitUntil(() => hitTargets.Count > 0 ||
                                            Vector3.Distance(startPosition, transform.position) >= maxRange);

            // 创建爆炸效果
            if (effect != null)
            {
                Instantiate(effect, transform.position, Quaternion.identity);
            }

            // 对范围内的敌人造成伤害
            Collider2D[] enemies = Physics2D.OverlapCircleAll(transform.position, radius); // 检查所有层
            foreach (var enemy in enemies)
            {
                // 通过名称或组件检查是否为敌人，避免标签问题
                if ((enemy.name.Contains("Enemy") || enemy.GetComponent<RoguelikeGame.Enemies.EnemyController>() != null)
                    && !hitTargets.Contains(enemy.gameObject))
                {
                    Vector2 direction = (enemy.transform.position - transform.position).normalized;
                    if (weaponController != null)
                    {
                        weaponController.DamageEnemy(enemy.gameObject, direction);
                    }
                }
            }

            DestroyProjectile();
        }

        #endregion

        #region 视觉效果

        /// <summary>
        /// 添加拖尾效果
        /// </summary>
        public void AddTrailEffect(Material trailMaterial, float trailTime = 0.5f, float trailWidth = 0.1f)
        {
            if (trail == null)
            {
                trail = gameObject.AddComponent<TrailRenderer>();
            }

            trail.material = trailMaterial;
            trail.time = trailTime;
            trail.startWidth = trailWidth;
            trail.endWidth = 0f;
            trail.autodestruct = false;
        }

        /// <summary>
        /// 设置投射物旋转
        /// </summary>
        public void SetRotation(float rotationSpeed = 360f)
        {
            StartCoroutine(RotationBehavior(rotationSpeed));
        }

        private System.Collections.IEnumerator RotationBehavior(float speed)
        {
            while (gameObject != null)
            {
                transform.Rotate(0, 0, speed * Time.deltaTime);
                yield return null;
            }
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 创建简单的方形精灵
        /// </summary>
        private Sprite CreateSimpleSprite()
        {
            // 创建一个1x1的纹理
            Texture2D texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, Color.white);
            texture.Apply();

            // 创建精灵
            return Sprite.Create(texture, new Rect(0, 0, 1, 1), new Vector2(0.5f, 0.5f), 100f);
        }

        #endregion
    }
}
