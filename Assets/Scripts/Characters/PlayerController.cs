using UnityEngine;
using RoguelikeGame.Core;
using RoguelikeGame.Weapons;

namespace RoguelikeGame.Characters
{
    /// <summary>
    /// 玩家控制器，处理玩家移动、攻击和技能使用
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D))]
    [RequireComponent(typeof(Collider2D))]
    public class PlayerController : MonoBehaviour
    {
        [Header("角色数据")]
        public CharacterData characterData;

        [Header("移动设置")]
        public float acceleration = 10f;
        public float deceleration = 10f;

        [Header("调试设置")]
        public bool debugMode = false;

        // 组件引用
        private Rigidbody2D rb;
        private Animator animator;
        private SpriteRenderer spriteRenderer;

        // 角色属性
        public float CurrentHealth { get; private set; }
        public float MaxHealth { get; private set; }
        public float AttackDamage { get; private set; }
        public float AttackSpeed { get; private set; }
        public float MoveSpeed { get; private set; }
        public float Defense { get; private set; }
        public float CriticalChance { get; private set; }
        public float CriticalMultiplier { get; private set; }

        // 状态
        public bool IsAlive { get; private set; } = true;
        public bool CanMove { get; private set; } = true;
        public bool CanAttack { get; private set; } = true;

        // 移动相关
        private Vector2 moveInput;
        private Vector2 currentVelocity;
        private Vector2 lastMoveDirection = Vector2.down;

        // 武器系统
        private WeaponController currentWeapon;

        // 技能系统
        private float activeSkillCooldownTimer;

        // 事件系统
        public System.Action<float, float> OnHealthChanged;
        public System.Action OnPlayerDeath;
        public System.Action<Vector2> OnPlayerMoved;

        private void Awake()
        {
            Debug.Log("PlayerController.Awake() 被调用");
            InitializeComponents();
        }

        private void Start()
        {
            Debug.Log("PlayerController.Start() 被调用");
            InitializeCharacter();
        }

        private void Update()
        {
            if (!IsAlive)
            {
                return;
            }

            // 检查HandleInput前的moveInput状态
            Vector2 moveInputBefore = moveInput;

            HandleInput();

            // 检查HandleInput后的moveInput状态
            Vector2 moveInputAfter = moveInput;

            // 只在有输入变化时显示详细调试
            if (moveInputBefore != Vector2.zero || moveInputAfter != Vector2.zero)
            {
                Debug.Log($"=== Update调试 ===");
                Debug.Log($"HandleInput前: {moveInputBefore}");
                Debug.Log($"HandleInput后: {moveInputAfter}");

                if (moveInputBefore != moveInputAfter)
                {
                    Debug.Log($"HandleInput改变了moveInput: {moveInputBefore} -> {moveInputAfter}");
                }
            }

            UpdateSkillCooldowns();
            UpdateAnimations();

            // 临时测试：手动调用移动处理
            if (Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.D))
            {
                Debug.Log("手动调用移动处理（因为FixedUpdate不工作）");
                HandleMovement();

                // 额外测试：直接移动Transform（绕过物理系统）
                Vector3 directMovement = new Vector3(moveInput.x, moveInput.y, 0) * MoveSpeed * Time.deltaTime;
                Vector3 oldPos = transform.position;

                Debug.Log($"准备移动Transform:");
                Debug.Log($"  当前位置: {oldPos}");
                Debug.Log($"  moveInput: {moveInput}");
                Debug.Log($"  MoveSpeed: {MoveSpeed}");
                Debug.Log($"  Time.deltaTime: {Time.deltaTime}");
                Debug.Log($"  计算的移动量: {directMovement}");

                transform.position += directMovement;

                Vector3 newPos = transform.position;
                Debug.Log($"Transform移动结果:");
                Debug.Log($"  设置后位置: {newPos}");
                Debug.Log($"  实际移动距离: {Vector3.Distance(oldPos, newPos)}");

                if (Vector3.Distance(oldPos, newPos) < 0.001f)
                {
                    Debug.LogError("警告：Transform.position没有改变！可能被其他代码重置了！");
                }
            }
        }

        private void FixedUpdate()
        {
            // 强制调试输出 - 总是显示，不管任何条件
            Debug.Log($"PlayerController.FixedUpdate 被调用！Frame: {Time.fixedTime:F2}, IsAlive: {IsAlive}, CanMove: {CanMove}, moveInput: {moveInput}, enabled: {enabled}");

            if (!IsAlive || !CanMove)
            {
                Debug.Log($"PlayerController.FixedUpdate - 跳过移动处理，IsAlive: {IsAlive}, CanMove: {CanMove}");
                return;
            }

            HandleMovement();
        }

        private void InitializeComponents()
        {
            Debug.Log("PlayerController.InitializeComponents() 开始");

            rb = GetComponent<Rigidbody2D>();
            if (rb == null)
            {
                Debug.LogError("PlayerController需要Rigidbody2D组件！正在创建...");
                rb = gameObject.AddComponent<Rigidbody2D>();
            }

            Debug.Log($"找到/创建Rigidbody2D组件: {rb}");

            animator = GetComponent<Animator>();
            spriteRenderer = GetComponent<SpriteRenderer>();

            // 如果没有SpriteRenderer，创建一个
            if (spriteRenderer == null)
            {
                spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
                // 创建一个简单的方形精灵
                spriteRenderer.sprite = CreateSimpleSprite();
                spriteRenderer.color = Color.green; // 玩家使用绿色
            }

            // 设置Rigidbody2D
            rb.bodyType = RigidbodyType2D.Dynamic; // 确保是动态的
            rb.gravityScale = 0f;
            rb.drag = 0f;
            rb.angularDrag = 0f;
            rb.freezeRotation = true;
            rb.constraints = RigidbodyConstraints2D.FreezeRotation; // 只冻结旋转，不冻结位置

            Debug.Log($"Rigidbody2D详细配置:");
            Debug.Log($"  bodyType: {rb.bodyType}");
            Debug.Log($"  gravityScale: {rb.gravityScale}");
            Debug.Log($"  drag: {rb.drag}");
            Debug.Log($"  constraints: {rb.constraints}");
            Debug.Log($"  isKinematic: {rb.isKinematic}");
            Debug.Log($"  simulated: {rb.simulated}");
            Debug.Log("PlayerController.InitializeComponents() 完成");
        }

        private void InitializeCharacter()
        {
            if (characterData == null)
            {
                Debug.LogError("角色数据未设置！");
                return;
            }

            // 初始化属性
            MaxHealth = characterData.baseHealth;
            CurrentHealth = MaxHealth;
            AttackDamage = characterData.baseAttackDamage;
            AttackSpeed = characterData.baseAttackSpeed;
            MoveSpeed = characterData.baseMoveSpeed;
            Defense = characterData.baseDefense;
            CriticalChance = characterData.baseCriticalChance;
            CriticalMultiplier = characterData.baseCriticalMultiplier;

            // 设置动画控制器
            if (animator != null && characterData.animatorController != null)
            {
                animator.runtimeAnimatorController = characterData.animatorController;
            }

            // 初始化武器
            InitializeWeapon();

            // 通知UI更新
            OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);

            if (debugMode)
            {
                Debug.Log($"角色初始化完成: {characterData.characterName}");
            }
        }

        private void InitializeWeapon()
        {
            // 查找武器控制器
            currentWeapon = GetComponentInChildren<WeaponController>();
            if (currentWeapon == null)
            {
                // 如果没有武器，创建默认武器
                GameObject weaponObj = new GameObject("DefaultWeapon");
                weaponObj.transform.SetParent(transform);
                weaponObj.transform.localPosition = Vector3.zero;
                currentWeapon = weaponObj.AddComponent<WeaponController>();
            }

            // 设置武器拥有者
            currentWeapon.SetOwner(this);
        }

        private void HandleInput()
        {
            if (InputManager.Instance != null)
            {
                // 使用输入管理器获取输入
                Vector2 inputFromManager = InputManager.Instance.GetMovementDirection();
                moveInput = inputFromManager;

                // 只在有输入时显示调试
                if (inputFromManager != Vector2.zero)
                {
                    Debug.Log($"PlayerController.HandleInput - InputManager输入: {inputFromManager}, 设置moveInput: {moveInput}");
                }

                // 记录最后的移动方向
                if (moveInput != Vector2.zero)
                {
                    lastMoveDirection = moveInput;
                }

                // 主动技能输入
                if (InputManager.Instance.SkillPressed && CanUseActiveSkill())
                {
                    UseActiveSkill();
                }
            }
            else
            {
                // 备用输入处理
                moveInput.x = Input.GetAxisRaw("Horizontal");
                moveInput.y = Input.GetAxisRaw("Vertical");
                moveInput = moveInput.normalized;

                // 调试输出
                if (debugMode && moveInput != Vector2.zero)
                {
                    Debug.Log($"PlayerController.HandleInput - 备用输入: {moveInput}");
                }

                // 记录最后的移动方向
                if (moveInput != Vector2.zero)
                {
                    lastMoveDirection = moveInput;
                }

                // 主动技能输入
                if (Input.GetKeyDown(KeyCode.Space) && CanUseActiveSkill())
                {
                    UseActiveSkill();
                }
            }
        }

        private void HandleMovement()
        {
            Vector2 targetVelocity = moveInput * MoveSpeed;
            Vector3 oldPosition = transform.position;

            // 强制调试输出 - 总是显示
            Debug.Log($"PlayerController移动处理 - moveInput: {moveInput}, MoveSpeed: {MoveSpeed}, targetVelocity: {targetVelocity}");


            // 平滑移动
            if (moveInput != Vector2.zero)
            {
                currentVelocity = Vector2.MoveTowards(currentVelocity, targetVelocity, acceleration * Time.fixedDeltaTime);
            }
            else
            {
                currentVelocity = Vector2.MoveTowards(currentVelocity, Vector2.zero, deceleration * Time.fixedDeltaTime);
            }

            rb.velocity = currentVelocity;

            // 强制调试输出 - 显示速度设置
            Debug.Log($"PlayerController设置速度 - currentVelocity: {currentVelocity}, rb.velocity: {rb.velocity}");

            // 位置变化调试
            Vector3 newPosition = transform.position;
            if (Vector3.Distance(oldPosition, newPosition) > 0.001f)
            {
                Debug.Log($"玩家位置变化！从 {oldPosition} 到 {newPosition}, 距离: {Vector3.Distance(oldPosition, newPosition):F3}");
            }

            // 触发移动事件
            if (currentVelocity.magnitude > 0.1f)
            {
                OnPlayerMoved?.Invoke(currentVelocity.normalized);
            }
        }

        private void UpdateSkillCooldowns()
        {
            Vector2 moveInputBefore = moveInput;

            if (activeSkillCooldownTimer > 0)
            {
                activeSkillCooldownTimer -= Time.deltaTime;
            }

            Vector2 moveInputAfter = moveInput;

            if (moveInputBefore != moveInputAfter)
            {
                Debug.Log($"UpdateSkillCooldowns改变了moveInput: {moveInputBefore} -> {moveInputAfter}");
            }
        }

        private void UpdateAnimations()
        {
            if (animator == null) return;

            Vector2 moveInputBefore = moveInput;

            // 设置移动动画参数
            animator.SetFloat("MoveX", lastMoveDirection.x);
            animator.SetFloat("MoveY", lastMoveDirection.y);
            animator.SetBool("IsMoving", moveInput.magnitude > 0.1f);
            animator.SetFloat("MoveSpeed", currentVelocity.magnitude);

            Vector2 moveInputAfter = moveInput;

            if (moveInputBefore != moveInputAfter)
            {
                Debug.Log($"UpdateAnimations改变了moveInput: {moveInputBefore} -> {moveInputAfter}");
            }
        }

        #region 战斗系统

        public void TakeDamage(float damage, Vector2 knockbackForce = default)
        {
            if (!IsAlive) return;

            // 计算实际伤害（考虑防御力）
            float actualDamage = Mathf.Max(1f, damage - Defense);
            CurrentHealth -= actualDamage;
            CurrentHealth = Mathf.Max(0f, CurrentHealth);

            // 播放受击音效
            AudioManager.Instance?.PlayPlayerHit();

            // 击退效果
            if (knockbackForce != Vector2.zero)
            {
                rb.AddForce(knockbackForce, ForceMode2D.Impulse);
            }

            // 受击动画
            if (animator != null)
            {
                animator.SetTrigger("Hit");
            }

            // 通知UI更新
            OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);

            // 检查死亡
            if (CurrentHealth <= 0)
            {
                Die();
            }

            if (debugMode)
            {
                Debug.Log($"玩家受到 {actualDamage} 点伤害，剩余血量: {CurrentHealth}");
            }
        }

        public void Heal(float amount)
        {
            if (!IsAlive) return;

            CurrentHealth += amount;
            CurrentHealth = Mathf.Min(MaxHealth, CurrentHealth);

            OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);

            if (debugMode)
            {
                Debug.Log($"玩家恢复 {amount} 点血量，当前血量: {CurrentHealth}");
            }
        }

        private void Die()
        {
            IsAlive = false;
            CanMove = false;
            CanAttack = false;

            // 停止移动
            rb.velocity = Vector2.zero;

            // 播放死亡动画
            if (animator != null)
            {
                animator.SetTrigger("Death");
            }

            // 禁用武器
            if (currentWeapon != null)
            {
                currentWeapon.SetActive(false);
            }

            // 触发死亡事件
            OnPlayerDeath?.Invoke();

            // 通知游戏管理器
            GameManager.Instance?.GameOver();

            if (debugMode)
            {
                Debug.Log("玩家死亡");
            }
        }

        #endregion

        #region 技能系统

        private bool CanUseActiveSkill()
        {
            return IsAlive &&
                   characterData.activeSkill != null &&
                   activeSkillCooldownTimer <= 0;
        }

        private void UseActiveSkill()
        {
            if (!CanUseActiveSkill()) return;

            // 设置冷却时间
            activeSkillCooldownTimer = characterData.activeSkill.cooldown;

            // 执行技能效果
            ExecuteSkillEffects(characterData.activeSkill);

            // 播放技能动画
            if (animator != null)
            {
                animator.SetTrigger("UseSkill");
            }

            if (debugMode)
            {
                Debug.Log($"使用主动技能: {characterData.activeSkill.skillName}");
            }
        }

        private void ExecuteSkillEffects(CharacterSkill skill)
        {
            foreach (var effect in skill.effects)
            {
                // 检查触发条件和概率
                if (ShouldTriggerEffect(effect))
                {
                    ApplySkillEffect(effect);
                }
            }
        }

        private bool ShouldTriggerEffect(SkillEffect effect)
        {
            // 检查触发概率
            if (Random.Range(0f, 1f) > effect.triggerChance)
                return false;

            // 检查触发条件
            switch (effect.triggerCondition)
            {
                case TriggerCondition.Always:
                    return true;
                case TriggerCondition.OnLowHealth:
                    return CurrentHealth / MaxHealth <= 0.3f;
                default:
                    return true;
            }
        }

        private void ApplySkillEffect(SkillEffect effect)
        {
            switch (effect.effectType)
            {
                case EffectType.Heal:
                    Heal(effect.value);
                    break;
                case EffectType.IncreaseAttackDamage:
                    float damage = AttackDamage;
                    ModifyAttribute(ref damage, effect.value, effect.isPercentage);
                    AttackDamage = damage;
                    break;
                case EffectType.IncreaseMoveSpeed:
                    float speed = MoveSpeed;
                    ModifyAttribute(ref speed, effect.value, effect.isPercentage);
                    MoveSpeed = speed;
                    break;
                // TODO: 实现更多技能效果
            }
        }

        private void ModifyAttribute(ref float attribute, float value, bool isPercentage)
        {
            if (isPercentage)
            {
                attribute *= (1f + value);
            }
            else
            {
                attribute += value;
            }
        }

        #endregion

        #region 武器系统接口

        public void EquipWeapon(WeaponData weaponData)
        {
            if (currentWeapon != null)
            {
                currentWeapon.EquipWeapon(weaponData);
            }
        }

        public Vector2 GetAttackDirection()
        {
            return lastMoveDirection;
        }

        public float GetAttackDamage()
        {
            // 计算是否暴击
            bool isCritical = Random.Range(0f, 1f) < CriticalChance;
            float damage = AttackDamage;

            if (isCritical)
            {
                damage *= CriticalMultiplier;
            }

            return damage;
        }

        #endregion

        #region 公共接口

        public void SetCharacterData(CharacterData data)
        {
            characterData = data;
            if (gameObject.activeInHierarchy)
            {
                InitializeCharacter();
            }
        }

        public void SetCanMove(bool canMove)
        {
            CanMove = canMove;
            if (!canMove)
            {
                rb.velocity = Vector2.zero;
            }
        }

        public void SetCanAttack(bool canAttack)
        {
            CanAttack = canAttack;
            if (currentWeapon != null)
            {
                currentWeapon.SetActive(canAttack);
            }
        }

        public float GetActiveSkillCooldownPercent()
        {
            if (characterData.activeSkill == null) return 0f;
            return Mathf.Clamp01(activeSkillCooldownTimer / characterData.activeSkill.cooldown);
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 创建简单的方形精灵
        /// </summary>
        private Sprite CreateSimpleSprite()
        {
            // 创建一个1x1的纹理
            Texture2D texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, Color.white);
            texture.Apply();

            // 创建精灵
            return Sprite.Create(texture, new Rect(0, 0, 1, 1), new Vector2(0.5f, 0.5f), 100f);
        }

        #endregion
    }
}
