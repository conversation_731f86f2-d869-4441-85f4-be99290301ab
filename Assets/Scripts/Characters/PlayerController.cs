using UnityEngine;
using RoguelikeGame.Core;
using RoguelikeGame.Weapons;

namespace RoguelikeGame.Characters
{
    /// <summary>
    /// 玩家控制器，处理玩家移动、攻击和技能使用
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D))]
    [RequireComponent(typeof(Collider2D))]
    public class PlayerController : MonoBehaviour
    {
        [Header("角色数据")]
        public CharacterData characterData;

        [Header("移动设置")]
        public float acceleration = 10f;
        public float deceleration = 10f;

        [Header("调试设置")]
        public bool debugMode = false;

        // 组件引用
        private Rigidbody2D rb;
        private Animator animator;
        private SpriteRenderer spriteRenderer;

        // 角色属性
        public float CurrentHealth { get; private set; }
        public float MaxHealth { get; private set; }
        public float AttackDamage { get; private set; }
        public float AttackSpeed { get; private set; }
        public float MoveSpeed { get; private set; }
        public float Defense { get; private set; }
        public float CriticalChance { get; private set; }
        public float CriticalMultiplier { get; private set; }

        // 状态
        public bool IsAlive { get; private set; } = true;
        public bool CanMove { get; private set; } = true;
        public bool CanAttack { get; private set; } = true;

        // 移动相关
        private Vector2 moveInput;
        private Vector2 currentVelocity;
        private Vector2 lastMoveDirection = Vector2.down;

        // 武器系统
        private WeaponController currentWeapon;

        // 技能系统
        private float activeSkillCooldownTimer;

        // 事件系统
        public System.Action<float, float> OnHealthChanged;
        public System.Action OnPlayerDeath;
        public System.Action<Vector2> OnPlayerMoved;

        private void Awake()
        {
            Debug.Log("PlayerController.Awake() 被调用");
            InitializeComponents();
        }

        private void Start()
        {
            Debug.Log("PlayerController.Start() 被调用");
            InitializeCharacter();
        }

        private void Update()
        {
            // 强制调试输出 - 检查Update是否被调用
            Debug.Log($"PlayerController.Update 被调用！Time.timeScale: {Time.timeScale}, Time.deltaTime: {Time.deltaTime}");
            Debug.Log($"PlayerController组件状态 - enabled: {enabled}, gameObject.activeInHierarchy: {gameObject.activeInHierarchy}");
            Debug.Log($"PlayerController生命状态 - IsAlive: {IsAlive}, CanMove: {CanMove}");

            if (!IsAlive)
            {
                Debug.Log("PlayerController.Update - 玩家已死亡，跳过");
                return;
            }

            // 检查HandleInput前的moveInput状态
            Vector2 moveInputBefore = moveInput;

            HandleInput();

            // 检查HandleInput后的moveInput状态
            Vector2 moveInputAfter = moveInput;

            // 只在有输入变化时显示详细调试
            if (moveInputBefore != Vector2.zero || moveInputAfter != Vector2.zero)
            {
                Debug.Log($"=== Update调试 ===");
                Debug.Log($"HandleInput前: {moveInputBefore}");
                Debug.Log($"HandleInput后: {moveInputAfter}");

                if (moveInputBefore != moveInputAfter)
                {
                    Debug.Log($"HandleInput改变了moveInput: {moveInputBefore} -> {moveInputAfter}");
                }
            }

            UpdateSkillCooldowns();
            UpdateAnimations();

            // 移除直接修改Transform.position的代码，让FixedUpdate中的Rigidbody2D处理移动

            // 临时测试：手动检查FixedUpdate是否应该被调用
            if (Time.frameCount % 60 == 0) // 每秒检查一次
            {
                Debug.Log($"每秒检查 - Time.timeScale: {Time.timeScale}, Time.fixedDeltaTime: {Time.fixedDeltaTime}");
                Debug.Log($"Physics2D.simulationMode: {Physics2D.simulationMode}");
                Debug.Log($"FixedUpdate应该被调用，但实际上没有被调用！");

                // 强制修复Time.timeScale
                if (Time.timeScale == 0)
                {
                    Debug.LogError("检测到Time.timeScale为0！强制设置为1！");
                    Time.timeScale = 1f;
                }
            }
        }

        private void FixedUpdate()
        {
            // 记录FixedUpdate开始时的位置
            Vector3 positionAtStart = transform.position;

            // 强制调试输出 - 总是显示，不管任何条件
            Debug.Log($"PlayerController.FixedUpdate 被调用！Frame: {Time.fixedTime:F2}, IsAlive: {IsAlive}, CanMove: {CanMove}, moveInput: {moveInput}, enabled: {enabled}");
            Debug.Log($"FixedUpdate开始时位置: {positionAtStart}");
            Debug.Log($"Time.timeScale: {Time.timeScale}, Time.fixedDeltaTime: {Time.fixedDeltaTime}");
            Debug.Log($"GameObject.activeInHierarchy: {gameObject.activeInHierarchy}, Component.enabled: {enabled}");

            // 检查GameManager状态
            if (GameManager.Instance != null)
            {
                Debug.Log($"GameManager状态 - CurrentState: {GameManager.Instance.CurrentState}, IsGamePaused: {GameManager.Instance.IsGamePaused}");
            }
            else
            {
                Debug.Log("GameManager.Instance为null - 可能是时序问题，稍后会创建");
                // 不要因为GameManager为null就跳过移动处理
            }

            if (!IsAlive || !CanMove)
            {
                Debug.Log($"PlayerController.FixedUpdate - 跳过移动处理，IsAlive: {IsAlive}, CanMove: {CanMove}");
                return;
            }

            HandleMovement();

            // 记录FixedUpdate结束时的位置
            Vector3 positionAtEnd = transform.position;
            Debug.Log($"FixedUpdate结束时位置: {positionAtEnd}");

            if (Vector3.Distance(positionAtStart, positionAtEnd) > 0.001f)
            {
                Debug.Log($"FixedUpdate中位置发生变化: {positionAtStart} -> {positionAtEnd}, 距离: {Vector3.Distance(positionAtStart, positionAtEnd):F6}");
            }
        }

        private void LateUpdate()
        {
            // 检测是否有其他代码在修改位置
            if (lastRecordedPosition != Vector3.zero)
            {
                Vector3 currentPos = transform.position;
                float distance = Vector3.Distance(lastRecordedPosition, currentPos);

                if (distance > 0.001f)
                {
                    Debug.Log($"LateUpdate检测到位置变化: {lastRecordedPosition} -> {currentPos}, 距离: {distance:F6}");

                    // 检查是否是由于Rigidbody2D移动导致的
                    if (rb != null && rb.velocity.magnitude > 0.1f)
                    {
                        Debug.Log($"位置变化可能由Rigidbody2D移动导致，当前速度: {rb.velocity}");
                    }
                    else
                    {
                        Debug.LogWarning($"位置变化不是由Rigidbody2D导致！可能有其他代码在修改位置！");
                        Debug.LogWarning($"当前Rigidbody2D速度: {(rb != null ? rb.velocity.ToString() : "null")}");
                    }
                }
            }

            lastRecordedPosition = transform.position;
        }

        private Vector3 lastRecordedPosition = Vector3.zero;

        private void InitializeComponents()
        {
            Debug.Log("PlayerController.InitializeComponents() 开始");

            rb = GetComponent<Rigidbody2D>();
            if (rb == null)
            {
                Debug.LogError("PlayerController需要Rigidbody2D组件！正在创建...");
                rb = gameObject.AddComponent<Rigidbody2D>();
            }

            Debug.Log($"找到/创建Rigidbody2D组件: {rb}");

            animator = GetComponent<Animator>();
            spriteRenderer = GetComponent<SpriteRenderer>();

            // 如果没有SpriteRenderer，创建一个
            if (spriteRenderer == null)
            {
                spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
                // 创建一个简单的方形精灵
                spriteRenderer.sprite = CreateSimpleSprite();
                spriteRenderer.color = Color.green; // 玩家使用绿色
            }

            // 设置Rigidbody2D - 确保配置正确
            rb.bodyType = RigidbodyType2D.Dynamic; // 确保是动态的
            rb.isKinematic = false; // 确保不是运动学的
            rb.gravityScale = 0f; // 2D俯视角游戏不需要重力
            rb.drag = 0f; // 不需要阻力
            rb.angularDrag = 0f; // 不需要角阻力
            rb.freezeRotation = true; // 冻结旋转
            rb.constraints = RigidbodyConstraints2D.FreezeRotation; // 只冻结旋转，不冻结位置
            rb.simulated = true; // 确保物理模拟启用
            rb.useAutoMass = false; // 使用手动设置的质量
            rb.mass = 1f; // 设置质量为1

            // 强制刷新物理设置
            rb.WakeUp();

            // 检查物理设置
            CheckPhysicsSettings();

            Debug.Log($"Rigidbody2D详细配置:");
            Debug.Log($"  bodyType: {rb.bodyType}");
            Debug.Log($"  isKinematic: {rb.isKinematic}");
            Debug.Log($"  gravityScale: {rb.gravityScale}");
            Debug.Log($"  drag: {rb.drag}");
            Debug.Log($"  angularDrag: {rb.angularDrag}");
            Debug.Log($"  constraints: {rb.constraints}");
            Debug.Log($"  simulated: {rb.simulated}");
            Debug.Log($"  mass: {rb.mass}");
            Debug.Log($"  useAutoMass: {rb.useAutoMass}");
            Debug.Log($"  sleepMode: {rb.sleepMode}");
            Debug.Log($"  interpolation: {rb.interpolation}");
            Debug.Log("PlayerController.InitializeComponents() 完成");
        }

        private void CheckPhysicsSettings()
        {
            Debug.Log("=== 检查物理设置 ===");
            Debug.Log($"Physics2D.gravity: {Physics2D.gravity}");
            Debug.Log($"Time.fixedDeltaTime: {Time.fixedDeltaTime}");
            Debug.Log($"Physics2D.velocityIterations: {Physics2D.velocityIterations}");
            Debug.Log($"Physics2D.positionIterations: {Physics2D.positionIterations}");
            Debug.Log($"Physics2D.velocityThreshold: {Physics2D.velocityThreshold}");
            Debug.Log($"Physics2D.maxLinearCorrection: {Physics2D.maxLinearCorrection}");
            Debug.Log($"Physics2D.maxAngularCorrection: {Physics2D.maxAngularCorrection}");
            Debug.Log($"Physics2D.maxTranslationSpeed: {Physics2D.maxTranslationSpeed}");
            Debug.Log($"Physics2D.maxRotationSpeed: {Physics2D.maxRotationSpeed}");
            Debug.Log($"Physics2D.defaultContactOffset: {Physics2D.defaultContactOffset}");
            Debug.Log($"Physics2D.baumgarteScale: {Physics2D.baumgarteScale}");
            Debug.Log($"Physics2D.baumgarteTOIScale: {Physics2D.baumgarteTOIScale}");
            Debug.Log($"Physics2D.timeToSleep: {Physics2D.timeToSleep}");
            Debug.Log($"Physics2D.linearSleepTolerance: {Physics2D.linearSleepTolerance}");
            Debug.Log($"Physics2D.angularSleepTolerance: {Physics2D.angularSleepTolerance}");
            Debug.Log("=== 物理设置检查完成 ===");
        }

        private void InitializeCharacter()
        {
            if (characterData == null)
            {
                Debug.LogError("角色数据未设置！");
                return;
            }

            // 初始化属性
            MaxHealth = characterData.baseHealth;
            CurrentHealth = MaxHealth;
            AttackDamage = characterData.baseAttackDamage;
            AttackSpeed = characterData.baseAttackSpeed;
            MoveSpeed = characterData.baseMoveSpeed;
            Defense = characterData.baseDefense;
            CriticalChance = characterData.baseCriticalChance;
            CriticalMultiplier = characterData.baseCriticalMultiplier;

            // 设置动画控制器
            if (animator != null && characterData.animatorController != null)
            {
                animator.runtimeAnimatorController = characterData.animatorController;
            }

            // 初始化武器
            InitializeWeapon();

            // 通知UI更新
            OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);

            if (debugMode)
            {
                Debug.Log($"角色初始化完成: {characterData.characterName}");
            }
        }

        private void InitializeWeapon()
        {
            // 查找武器控制器
            currentWeapon = GetComponentInChildren<WeaponController>();
            if (currentWeapon == null)
            {
                // 如果没有武器，创建默认武器
                GameObject weaponObj = new GameObject("DefaultWeapon");
                weaponObj.transform.SetParent(transform);
                weaponObj.transform.localPosition = Vector3.zero;
                currentWeapon = weaponObj.AddComponent<WeaponController>();
            }

            // 设置武器拥有者
            currentWeapon.SetOwner(this);
        }

        private void HandleInput()
        {
            Vector2 oldMoveInput = moveInput;

            if (InputManager.Instance != null)
            {
                // 使用输入管理器获取输入
                Vector2 inputFromManager = InputManager.Instance.MovementInput;
                moveInput = inputFromManager;

                // 强制调试输出 - 总是显示
                Debug.Log($"PlayerController.HandleInput - InputManager.MovementInput: {inputFromManager}, 设置moveInput: {moveInput}");

                // 记录最后的移动方向
                if (moveInput != Vector2.zero)
                {
                    lastMoveDirection = moveInput;
                }

                // 主动技能输入
                if (InputManager.Instance.SkillPressed && CanUseActiveSkill())
                {
                    UseActiveSkill();
                }
            }
            else
            {
                Debug.Log("InputManager.Instance为null，使用备用输入");
                // 备用输入处理
                moveInput = Vector2.zero;
                if (Input.GetKey(KeyCode.W)) moveInput.y += 1f;
                if (Input.GetKey(KeyCode.S)) moveInput.y -= 1f;
                if (Input.GetKey(KeyCode.A)) moveInput.x -= 1f;
                if (Input.GetKey(KeyCode.D)) moveInput.x += 1f;

                // 调试输出
                if (moveInput != Vector2.zero)
                {
                    Debug.Log($"PlayerController.HandleInput - 备用输入检测到: {moveInput}");
                }

                // 记录最后的移动方向
                if (moveInput != Vector2.zero)
                {
                    lastMoveDirection = moveInput;
                }

                // 主动技能输入
                if (Input.GetKeyDown(KeyCode.Space) && CanUseActiveSkill())
                {
                    UseActiveSkill();
                }
            }

            // 强制调试输出移动输入变化
            if (oldMoveInput != moveInput)
            {
                Debug.Log($"PlayerController移动输入变化: {oldMoveInput} -> {moveInput}");
            }
        }

        private void HandleMovement()
        {
            Vector2 targetVelocity = moveInput * MoveSpeed;
            Vector3 oldPosition = transform.position;

            // 强制调试输出 - 总是显示
            Debug.Log($"PlayerController移动处理 - moveInput: {moveInput}, MoveSpeed: {MoveSpeed}, targetVelocity: {targetVelocity}");
            Debug.Log($"Rigidbody2D状态 - bodyType: {rb.bodyType}, isKinematic: {rb.isKinematic}, simulated: {rb.simulated}");
            Debug.Log($"Rigidbody2D约束 - constraints: {rb.constraints}, freezeRotation: {rb.freezeRotation}");

            // 检查Rigidbody2D是否有效
            if (rb == null)
            {
                Debug.LogError("Rigidbody2D为null！");
                return;
            }

            if (rb.bodyType != RigidbodyType2D.Dynamic)
            {
                Debug.LogError($"Rigidbody2D不是Dynamic类型！当前类型: {rb.bodyType}");
                rb.bodyType = RigidbodyType2D.Dynamic;
            }

            if (rb.isKinematic)
            {
                Debug.LogError("Rigidbody2D是Kinematic模式！");
                rb.isKinematic = false;
            }

            // 平滑移动
            if (moveInput != Vector2.zero)
            {
                currentVelocity = Vector2.MoveTowards(currentVelocity, targetVelocity, acceleration * Time.fixedDeltaTime);
            }
            else
            {
                currentVelocity = Vector2.MoveTowards(currentVelocity, Vector2.zero, deceleration * Time.fixedDeltaTime);
            }

            // 设置速度前的调试
            Debug.Log($"准备设置速度 - 当前rb.velocity: {rb.velocity}, 新的currentVelocity: {currentVelocity}");

            rb.velocity = currentVelocity;

            // 设置速度后立即检查
            Debug.Log($"设置速度后 - rb.velocity: {rb.velocity}");

            // 强制调试输出 - 显示速度设置
            Debug.Log($"PlayerController设置速度 - currentVelocity: {currentVelocity}, rb.velocity: {rb.velocity}");

            // 在下一帧检查位置变化（因为物理更新可能在稍后发生）
            StartCoroutine(CheckPositionChangeNextFrame(oldPosition));

            // 触发移动事件
            if (currentVelocity.magnitude > 0.1f)
            {
                OnPlayerMoved?.Invoke(currentVelocity.normalized);
            }
        }

        private System.Collections.IEnumerator CheckPositionChangeNextFrame(Vector3 oldPosition)
        {
            yield return new WaitForFixedUpdate();
            Vector3 newPosition = transform.position;
            float distance = Vector3.Distance(oldPosition, newPosition);
            Debug.Log($"下一帧位置检查 - 从 {oldPosition} 到 {newPosition}, 距离: {distance:F6}");

            if (distance < 0.0001f && rb.velocity.magnitude > 0.1f)
            {
                Debug.LogError("警告：Rigidbody2D有速度但位置没有改变！可能被其他代码阻止了移动！");
            }
        }

        private void UpdateSkillCooldowns()
        {
            Vector2 moveInputBefore = moveInput;

            if (activeSkillCooldownTimer > 0)
            {
                activeSkillCooldownTimer -= Time.deltaTime;
            }

            Vector2 moveInputAfter = moveInput;

            if (moveInputBefore != moveInputAfter)
            {
                Debug.Log($"UpdateSkillCooldowns改变了moveInput: {moveInputBefore} -> {moveInputAfter}");
            }
        }

        private void UpdateAnimations()
        {
            if (animator == null) return;

            Vector2 moveInputBefore = moveInput;

            // 设置移动动画参数
            animator.SetFloat("MoveX", lastMoveDirection.x);
            animator.SetFloat("MoveY", lastMoveDirection.y);
            animator.SetBool("IsMoving", moveInput.magnitude > 0.1f);
            animator.SetFloat("MoveSpeed", currentVelocity.magnitude);

            Vector2 moveInputAfter = moveInput;

            if (moveInputBefore != moveInputAfter)
            {
                Debug.Log($"UpdateAnimations改变了moveInput: {moveInputBefore} -> {moveInputAfter}");
            }
        }

        #region 战斗系统

        public void TakeDamage(float damage, Vector2 knockbackForce = default)
        {
            if (!IsAlive) return;

            // 计算实际伤害（考虑防御力）
            float actualDamage = Mathf.Max(1f, damage - Defense);
            CurrentHealth -= actualDamage;
            CurrentHealth = Mathf.Max(0f, CurrentHealth);

            // 播放受击音效
            AudioManager.Instance?.PlayPlayerHit();

            // 击退效果
            if (knockbackForce != Vector2.zero)
            {
                rb.AddForce(knockbackForce, ForceMode2D.Impulse);
            }

            // 受击动画
            if (animator != null)
            {
                animator.SetTrigger("Hit");
            }

            // 通知UI更新
            OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);

            // 检查死亡
            if (CurrentHealth <= 0)
            {
                Die();
            }

            if (debugMode)
            {
                Debug.Log($"玩家受到 {actualDamage} 点伤害，剩余血量: {CurrentHealth}");
            }
        }

        public void Heal(float amount)
        {
            if (!IsAlive) return;

            CurrentHealth += amount;
            CurrentHealth = Mathf.Min(MaxHealth, CurrentHealth);

            OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);

            if (debugMode)
            {
                Debug.Log($"玩家恢复 {amount} 点血量，当前血量: {CurrentHealth}");
            }
        }

        private void Die()
        {
            IsAlive = false;
            CanMove = false;
            CanAttack = false;

            // 停止移动
            rb.velocity = Vector2.zero;

            // 播放死亡动画
            if (animator != null)
            {
                animator.SetTrigger("Death");
            }

            // 禁用武器
            if (currentWeapon != null)
            {
                currentWeapon.SetActive(false);
            }

            // 触发死亡事件
            OnPlayerDeath?.Invoke();

            // 通知游戏管理器
            GameManager.Instance?.GameOver();

            if (debugMode)
            {
                Debug.Log("玩家死亡");
            }
        }

        #endregion

        #region 技能系统

        private bool CanUseActiveSkill()
        {
            return IsAlive &&
                   characterData.activeSkill != null &&
                   activeSkillCooldownTimer <= 0;
        }

        private void UseActiveSkill()
        {
            if (!CanUseActiveSkill()) return;

            // 设置冷却时间
            activeSkillCooldownTimer = characterData.activeSkill.cooldown;

            // 执行技能效果
            ExecuteSkillEffects(characterData.activeSkill);

            // 播放技能动画
            if (animator != null)
            {
                animator.SetTrigger("UseSkill");
            }

            if (debugMode)
            {
                Debug.Log($"使用主动技能: {characterData.activeSkill.skillName}");
            }
        }

        private void ExecuteSkillEffects(CharacterSkill skill)
        {
            foreach (var effect in skill.effects)
            {
                // 检查触发条件和概率
                if (ShouldTriggerEffect(effect))
                {
                    ApplySkillEffect(effect);
                }
            }
        }

        private bool ShouldTriggerEffect(SkillEffect effect)
        {
            // 检查触发概率
            if (Random.Range(0f, 1f) > effect.triggerChance)
                return false;

            // 检查触发条件
            switch (effect.triggerCondition)
            {
                case TriggerCondition.Always:
                    return true;
                case TriggerCondition.OnLowHealth:
                    return CurrentHealth / MaxHealth <= 0.3f;
                default:
                    return true;
            }
        }

        private void ApplySkillEffect(SkillEffect effect)
        {
            switch (effect.effectType)
            {
                case EffectType.Heal:
                    Heal(effect.value);
                    break;
                case EffectType.IncreaseAttackDamage:
                    float damage = AttackDamage;
                    ModifyAttribute(ref damage, effect.value, effect.isPercentage);
                    AttackDamage = damage;
                    break;
                case EffectType.IncreaseMoveSpeed:
                    float speed = MoveSpeed;
                    ModifyAttribute(ref speed, effect.value, effect.isPercentage);
                    MoveSpeed = speed;
                    break;
                // TODO: 实现更多技能效果
            }
        }

        private void ModifyAttribute(ref float attribute, float value, bool isPercentage)
        {
            if (isPercentage)
            {
                attribute *= (1f + value);
            }
            else
            {
                attribute += value;
            }
        }

        #endregion

        #region 武器系统接口

        public void EquipWeapon(WeaponData weaponData)
        {
            if (currentWeapon != null)
            {
                currentWeapon.EquipWeapon(weaponData);
            }
        }

        public Vector2 GetAttackDirection()
        {
            return lastMoveDirection;
        }

        public float GetAttackDamage()
        {
            // 计算是否暴击
            bool isCritical = Random.Range(0f, 1f) < CriticalChance;
            float damage = AttackDamage;

            if (isCritical)
            {
                damage *= CriticalMultiplier;
            }

            return damage;
        }

        #endregion

        #region 公共接口

        public void SetCharacterData(CharacterData data)
        {
            characterData = data;
            if (gameObject.activeInHierarchy)
            {
                InitializeCharacter();
            }
        }

        public void SetCanMove(bool canMove)
        {
            CanMove = canMove;
            if (!canMove)
            {
                rb.velocity = Vector2.zero;
            }
        }

        public void SetCanAttack(bool canAttack)
        {
            CanAttack = canAttack;
            if (currentWeapon != null)
            {
                currentWeapon.SetActive(canAttack);
            }
        }

        public float GetActiveSkillCooldownPercent()
        {
            if (characterData.activeSkill == null) return 0f;
            return Mathf.Clamp01(activeSkillCooldownTimer / characterData.activeSkill.cooldown);
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 创建简单的方形精灵
        /// </summary>
        private Sprite CreateSimpleSprite()
        {
            // 创建一个1x1的纹理
            Texture2D texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, Color.white);
            texture.Apply();

            // 创建精灵
            return Sprite.Create(texture, new Rect(0, 0, 1, 1), new Vector2(0.5f, 0.5f), 100f);
        }

        #endregion
    }
}
