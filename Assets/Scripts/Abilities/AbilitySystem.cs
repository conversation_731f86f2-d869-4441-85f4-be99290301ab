using UnityEngine;
using RoguelikeGame.Core;
using RoguelikeGame.Characters;
using System.Collections.Generic;
using System.Linq;

namespace RoguelikeGame.Abilities
{
    /// <summary>
    /// 能力系统，管理玩家的能力选择和升级
    /// </summary>
    public class AbilitySystem : MonoBehaviour
    {
        [Header("能力设置")]
        public AbilityData[] availableAbilities;
        public int choicesPerLevel = 3;
        public int maxAbilityLevel = 5;

        [Header("能力权重")]
        public float commonWeight = 50f;
        public float rareWeight = 30f;
        public float epicWeight = 15f;
        public float legendaryWeight = 5f;

        [Header("调试设置")]
        public bool debugMode = false;

        // 单例模式
        public static AbilitySystem Instance { get; private set; }

        // 玩家能力
        private Dictionary<string, PlayerAbility> playerAbilities;
        private List<AbilityData> currentChoices;

        // 事件系统
        public System.Action<List<AbilityData>> OnAbilityChoicesGenerated;
        public System.Action<AbilityData> OnAbilitySelected;
        public System.Action<PlayerAbility> OnAbilityUpgraded;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAbilitySystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupEventListeners();
        }

        private void InitializeAbilitySystem()
        {
            playerAbilities = new Dictionary<string, PlayerAbility>();
            currentChoices = new List<AbilityData>();

            // 验证能力数据
            ValidateAbilityData();
        }

        private void SetupEventListeners()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged += OnGameStateChanged;
            }
        }

        private void OnDestroy()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged -= OnGameStateChanged;
            }
        }

        private void ValidateAbilityData()
        {
            if (availableAbilities == null || availableAbilities.Length == 0)
            {
                Debug.LogWarning("没有设置可用的能力数据！将在稍后设置。");
                return;
            }

            foreach (var ability in availableAbilities)
            {
                if (ability == null)
                {
                    Debug.LogWarning("发现空的能力数据引用");
                }
            }
        }

        /// <summary>
        /// 设置可用能力数据并重新初始化
        /// </summary>
        public void SetAvailableAbilities(AbilityData[] abilities)
        {
            availableAbilities = abilities;

            // 重新验证数据
            ValidateAbilityData();

            if (debugMode)
            {
                Debug.Log($"设置了 {abilities?.Length ?? 0} 个可用能力");
            }
        }

        public void GenerateAbilityChoices()
        {
            currentChoices.Clear();

            // 获取可选择的能力
            var availableChoices = GetAvailableAbilities();

            if (availableChoices.Count == 0)
            {
                Debug.LogWarning("没有可用的能力选择");
                return;
            }

            // 随机选择指定数量的能力
            int choiceCount = Mathf.Min(choicesPerLevel, availableChoices.Count);

            for (int i = 0; i < choiceCount; i++)
            {
                AbilityData selectedAbility = SelectWeightedRandomAbility(availableChoices);
                if (selectedAbility != null)
                {
                    currentChoices.Add(selectedAbility);
                    availableChoices.Remove(selectedAbility);
                }
            }

            // 触发事件
            OnAbilityChoicesGenerated?.Invoke(new List<AbilityData>(currentChoices));

            if (debugMode)
            {
                Debug.Log($"生成 {currentChoices.Count} 个能力选择");
                foreach (var choice in currentChoices)
                {
                    Debug.Log($"- {choice.abilityName} ({choice.rarity})");
                }
            }
        }

        private List<AbilityData> GetAvailableAbilities()
        {
            var available = new List<AbilityData>();

            foreach (var ability in availableAbilities)
            {
                if (ability == null) continue;

                // 检查是否已拥有该能力
                if (playerAbilities.ContainsKey(ability.abilityId))
                {
                    var playerAbility = playerAbilities[ability.abilityId];
                    // 如果还能升级，则可以选择
                    if (playerAbility.currentLevel < maxAbilityLevel && playerAbility.currentLevel < ability.maxLevel)
                    {
                        available.Add(ability);
                    }
                }
                else
                {
                    // 检查前置条件
                    if (CheckPrerequisites(ability))
                    {
                        available.Add(ability);
                    }
                }
            }

            return available;
        }

        private bool CheckPrerequisites(AbilityData ability)
        {
            if (ability.prerequisites == null || ability.prerequisites.Length == 0)
                return true;

            foreach (var prerequisite in ability.prerequisites)
            {
                if (!playerAbilities.ContainsKey(prerequisite.abilityId))
                    return false;

                var playerAbility = playerAbilities[prerequisite.abilityId];
                if (playerAbility.currentLevel < prerequisite.requiredLevel)
                    return false;
            }

            return true;
        }

        private AbilityData SelectWeightedRandomAbility(List<AbilityData> abilities)
        {
            if (abilities.Count == 0) return null;

            // 计算总权重
            float totalWeight = 0f;
            foreach (var ability in abilities)
            {
                totalWeight += GetAbilityWeight(ability.rarity);
            }

            // 随机选择
            float randomValue = Random.Range(0f, totalWeight);
            float currentWeight = 0f;

            foreach (var ability in abilities)
            {
                currentWeight += GetAbilityWeight(ability.rarity);
                if (randomValue <= currentWeight)
                {
                    return ability;
                }
            }

            // 备用选择
            return abilities[Random.Range(0, abilities.Count)];
        }

        private float GetAbilityWeight(AbilityRarity rarity)
        {
            switch (rarity)
            {
                case AbilityRarity.Common:
                    return commonWeight;
                case AbilityRarity.Rare:
                    return rareWeight;
                case AbilityRarity.Epic:
                    return epicWeight;
                case AbilityRarity.Legendary:
                    return legendaryWeight;
                default:
                    return commonWeight;
            }
        }

        public void SelectAbility(int choiceIndex)
        {
            if (choiceIndex < 0 || choiceIndex >= currentChoices.Count)
            {
                Debug.LogError($"无效的能力选择索引: {choiceIndex}");
                return;
            }

            AbilityData selectedAbility = currentChoices[choiceIndex];
            SelectAbility(selectedAbility);
        }

        public void SelectAbility(AbilityData abilityData)
        {
            if (abilityData == null)
            {
                Debug.LogError("尝试选择空的能力数据");
                return;
            }

            PlayerAbility playerAbility;

            if (playerAbilities.ContainsKey(abilityData.abilityId))
            {
                // 升级现有能力
                playerAbility = playerAbilities[abilityData.abilityId];
                playerAbility.currentLevel++;
                OnAbilityUpgraded?.Invoke(playerAbility);
            }
            else
            {
                // 获得新能力
                playerAbility = new PlayerAbility
                {
                    abilityData = abilityData,
                    currentLevel = 1,
                    isActive = true
                };
                playerAbilities[abilityData.abilityId] = playerAbility;
            }

            // 应用能力效果
            ApplyAbilityEffects(playerAbility);

            // 播放音效
            AudioManager.Instance?.PlayAbilitySelect();

            // 触发事件
            OnAbilitySelected?.Invoke(abilityData);

            // 清空当前选择
            currentChoices.Clear();

            if (debugMode)
            {
                Debug.Log($"选择能力: {abilityData.abilityName} (等级 {playerAbility.currentLevel})");
            }
        }

        private void ApplyAbilityEffects(PlayerAbility playerAbility)
        {
            var player = FindObjectOfType<PlayerController>();
            if (player == null)
            {
                Debug.LogError("找不到玩家控制器");
                return;
            }

            var abilityData = playerAbility.abilityData;
            int level = playerAbility.currentLevel;

            foreach (var effect in abilityData.effects)
            {
                ApplyAbilityEffect(player, effect, level);
            }
        }

        private void ApplyAbilityEffect(PlayerController player, AbilityEffect effect, int level)
        {
            float effectValue = effect.baseValue + (effect.valuePerLevel * (level - 1));

            switch (effect.effectType)
            {
                case AbilityEffectType.IncreaseHealth:
                    // TODO: 实现血量增加
                    if (debugMode)
                        Debug.Log($"增加血量: {effectValue}");
                    break;

                case AbilityEffectType.IncreaseAttackDamage:
                    // TODO: 实现攻击力增加
                    if (debugMode)
                        Debug.Log($"增加攻击力: {effectValue}");
                    break;

                case AbilityEffectType.IncreaseAttackSpeed:
                    // TODO: 实现攻击速度增加
                    if (debugMode)
                        Debug.Log($"增加攻击速度: {effectValue}");
                    break;

                case AbilityEffectType.IncreaseMoveSpeed:
                    // TODO: 实现移动速度增加
                    if (debugMode)
                        Debug.Log($"增加移动速度: {effectValue}");
                    break;

                case AbilityEffectType.IncreaseCriticalChance:
                    // TODO: 实现暴击率增加
                    if (debugMode)
                        Debug.Log($"增加暴击率: {effectValue}");
                    break;

                case AbilityEffectType.AddWeaponEffect:
                    // TODO: 实现武器效果添加
                    if (debugMode)
                        Debug.Log($"添加武器效果: {effect.weaponEffect}");
                    break;

                case AbilityEffectType.UnlockSkill:
                    // TODO: 实现技能解锁
                    if (debugMode)
                        Debug.Log($"解锁技能: {effect.skillToUnlock}");
                    break;
            }
        }

        #region 数据管理

        public List<PlayerAbility> GetPlayerAbilities()
        {
            return new List<PlayerAbility>(playerAbilities.Values);
        }

        public PlayerAbility GetPlayerAbility(string abilityId)
        {
            playerAbilities.TryGetValue(abilityId, out PlayerAbility ability);
            return ability;
        }

        public bool HasAbility(string abilityId)
        {
            return playerAbilities.ContainsKey(abilityId);
        }

        public int GetAbilityLevel(string abilityId)
        {
            if (playerAbilities.ContainsKey(abilityId))
            {
                return playerAbilities[abilityId].currentLevel;
            }
            return 0;
        }

        public void ResetAbilities()
        {
            playerAbilities.Clear();
            currentChoices.Clear();

            if (debugMode)
            {
                Debug.Log("重置所有能力");
            }
        }

        #endregion

        #region 事件处理

        private void OnGameStateChanged(GameState newState)
        {
            switch (newState)
            {
                case GameState.LevelComplete:
                    // 关卡完成时生成能力选择
                    GenerateAbilityChoices();
                    break;
            }
        }

        #endregion

        #region 公共接口

        public List<AbilityData> GetCurrentChoices()
        {
            return new List<AbilityData>(currentChoices);
        }

        public void ForceGenerateChoices()
        {
            GenerateAbilityChoices();
        }

        public bool HasCurrentChoices()
        {
            return currentChoices.Count > 0;
        }

        #endregion
    }

    /// <summary>
    /// 玩家拥有的能力数据
    /// </summary>
    [System.Serializable]
    public class PlayerAbility
    {
        public AbilityData abilityData;
        public int currentLevel;
        public bool isActive;
        public float cooldownRemaining;

        public string GetDescription()
        {
            return abilityData.GetDescriptionAtLevel(currentLevel);
        }

        public bool CanUpgrade()
        {
            return currentLevel < abilityData.maxLevel;
        }
    }
}
