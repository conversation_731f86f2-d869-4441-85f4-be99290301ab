using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
using RoguelikeGame.Characters;

namespace RoguelikeGame.Core
{
    /// <summary>
    /// 游戏主管理器，负责游戏状态管理、场景切换等核心功能
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("游戏设置")]
        public int maxLevel = 50;
        public int bossLevelInterval = 5;
        public float levelTimeLimit = 300f; // 5分钟

        [Header("调试设置")]
        public bool debugMode = false;

        // 单例模式
        public static GameManager Instance { get; private set; }

        // 游戏状态
        public GameState CurrentState { get; private set; }
        public int CurrentLevel { get; private set; } = 1;
        public float LevelTimeRemaining { get; private set; }
        public bool IsGamePaused { get; private set; }

        // 事件系统
        public System.Action<GameState> OnGameStateChanged;
        public System.Action<int> OnLevelChanged;
        public System.Action<float> OnTimeUpdated;
        public System.Action OnGamePaused;
        public System.Action OnGameResumed;

        private void Awake()
        {
            // 单例模式实现
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            ChangeGameState(GameState.MainMenu);
        }

        private void Update()
        {
            if (CurrentState == GameState.Playing)
            {
                UpdateLevelTimer();
            }

            HandleInput();
        }

        private void InitializeGame()
        {
            // 初始化游戏设置
            Application.targetFrameRate = 60;
            QualitySettings.vSyncCount = 1;

            // 初始化其他管理器
            if (UIManager.Instance == null)
            {
                GameObject uiManagerPrefab = Resources.Load<GameObject>("Managers/UIManager");
                if (uiManagerPrefab != null)
                {
                    Instantiate(uiManagerPrefab);
                }
            }

            if (AudioManager.Instance == null)
            {
                GameObject audioManagerPrefab = Resources.Load<GameObject>("Managers/AudioManager");
                if (audioManagerPrefab != null)
                {
                    Instantiate(audioManagerPrefab);
                }
            }
        }

        private void HandleInput()
        {
            // ESC键暂停/恢复游戏
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (CurrentState == GameState.Playing)
                {
                    if (IsGamePaused)
                        ResumeGame();
                    else
                        PauseGame();
                }
            }

            // 调试功能
            if (debugMode)
            {
                if (Input.GetKeyDown(KeyCode.N))
                {
                    NextLevel();
                }

                if (Input.GetKeyDown(KeyCode.R))
                {
                    RestartLevel();
                }
            }
        }

        private void UpdateLevelTimer()
        {
            if (!IsGamePaused)
            {
                LevelTimeRemaining -= Time.deltaTime;
                OnTimeUpdated?.Invoke(LevelTimeRemaining);

                if (LevelTimeRemaining <= 0)
                {
                    LevelTimeUp();
                }
            }
        }

        public void ChangeGameState(GameState newState)
        {
            if (CurrentState == newState) return;

            GameState previousState = CurrentState;
            CurrentState = newState;

            if (debugMode)
            {
                Debug.Log($"游戏状态改变: {previousState} -> {newState}");
            }

            OnGameStateChanged?.Invoke(newState);

            // 根据状态执行相应逻辑
            switch (newState)
            {
                case GameState.MainMenu:
                    Time.timeScale = 1f;
                    break;

                case GameState.Loading:
                    Time.timeScale = 1f; // 加载时也保持时间正常
                    break;

                case GameState.Playing:
                    Time.timeScale = 1f;
                    IsGamePaused = false;
                    break;

                case GameState.Paused:
                    Time.timeScale = 0f;
                    IsGamePaused = true;
                    break;

                case GameState.LevelComplete:
                    Time.timeScale = 0f;
                    break;

                case GameState.GameOver:
                    Time.timeScale = 0f;
                    break;

                case GameState.Victory:
                    Time.timeScale = 0f;
                    break;
            }

            // 强制调试输出 - 显示状态变化和时间缩放
            Debug.Log($"GameManager状态变化: {previousState} -> {newState}, Time.timeScale设置为: {Time.timeScale}");
        }

        public void StartGame()
        {
            // 加载选中的角色
            LoadSelectedCharacter();

            CurrentLevel = 1;

            // 切换到游戏场景
            if (RoguelikeGame.Core.SceneManager.Instance != null)
            {
                RoguelikeGame.Core.SceneManager.Instance.LoadGameplay();
            }
            else
            {
                LoadLevel(CurrentLevel);
            }

            if (debugMode)
            {
                Debug.Log("游戏开始");
            }
        }

        private void LoadSelectedCharacter()
        {
            string selectedCharacterName = PlayerPrefs.GetString("SelectedCharacter", "");
            if (!string.IsNullOrEmpty(selectedCharacterName) && DataManager.Instance != null)
            {
                var characterData = DataManager.Instance.GetCharacterData(selectedCharacterName);
                if (characterData != null)
                {
                    // 设置玩家角色
                    var player = FindObjectOfType<PlayerController>();
                    if (player != null)
                    {
                        player.SetCharacterData(characterData);
                    }
                }
            }
        }

        public void LoadLevel(int levelNumber)
        {
            CurrentLevel = Mathf.Clamp(levelNumber, 1, maxLevel);
            LevelTimeRemaining = levelTimeLimit;

            OnLevelChanged?.Invoke(CurrentLevel);

            // 判断是否为Boss关
            bool isBossLevel = (CurrentLevel % bossLevelInterval) == 0;

            if (debugMode)
            {
                Debug.Log($"加载关卡 {CurrentLevel}" + (isBossLevel ? " (Boss关)" : ""));
            }

            // 加载对应场景
            string sceneName = isBossLevel ? "BossLevel" : "NormalLevel";
            StartCoroutine(LoadSceneAsync(sceneName));
        }

        private IEnumerator LoadSceneAsync(string sceneName)
        {
            ChangeGameState(GameState.Loading);

            AsyncOperation asyncLoad = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(sceneName);

            while (!asyncLoad.isDone)
            {
                yield return null;
            }

            ChangeGameState(GameState.Playing);
        }

        public void NextLevel()
        {
            if (CurrentLevel >= maxLevel)
            {
                GameComplete();
            }
            else
            {
                LoadLevel(CurrentLevel + 1);
            }
        }

        public void RestartLevel()
        {
            LoadLevel(CurrentLevel);
        }

        public void LevelComplete()
        {
            ChangeGameState(GameState.LevelComplete);
        }

        public void LevelTimeUp()
        {
            // 时间到了，可以选择失败或者强制进入下一关
            LevelComplete();
        }

        public void GameOver()
        {
            ChangeGameState(GameState.GameOver);
        }

        public void GameComplete()
        {
            ChangeGameState(GameState.Victory);
        }

        public void PauseGame()
        {
            if (CurrentState == GameState.Playing)
            {
                ChangeGameState(GameState.Paused);
                OnGamePaused?.Invoke();
            }
        }

        public void ResumeGame()
        {
            if (CurrentState == GameState.Paused)
            {
                ChangeGameState(GameState.Playing);
                OnGameResumed?.Invoke();
            }
        }

        public void ReturnToMainMenu()
        {
            Time.timeScale = 1f;

            if (RoguelikeGame.Core.SceneManager.Instance != null)
            {
                RoguelikeGame.Core.SceneManager.Instance.LoadMainMenu();
            }
            else
            {
                UnityEngine.SceneManagement.SceneManager.LoadScene("MainMenu");
            }

            ChangeGameState(GameState.MainMenu);
        }

        public void QuitGame()
        {
            if (RoguelikeGame.Core.SceneManager.Instance != null)
            {
                RoguelikeGame.Core.SceneManager.Instance.QuitGame();
            }
            else
            {
                if (debugMode)
                {
                    Debug.Log("退出游戏");
                }

#if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
#else
                Application.Quit();
#endif
            }
        }

        public bool IsBossLevel()
        {
            return (CurrentLevel % bossLevelInterval) == 0;
        }

        public bool IsBossLevel(int level)
        {
            return (level % bossLevelInterval) == 0;
        }
    }

    /// <summary>
    /// 游戏状态枚举
    /// </summary>
    public enum GameState
    {
        MainMenu,       // 主菜单
        Loading,        // 加载中
        Playing,        // 游戏进行中
        Paused,         // 暂停
        LevelComplete,  // 关卡完成
        GameOver,       // 游戏结束
        Victory         // 游戏胜利
    }
}
