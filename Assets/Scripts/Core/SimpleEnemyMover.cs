using UnityEngine;
using RoguelikeGame.Characters;

namespace RoguelikeGame.Core
{
    /// <summary>
    /// 简单的敌人移动脚本，用于基础的AI行为
    /// </summary>
    public class SimpleEnemyMover : MonoBehaviour
    {
        [Header("移动设置")]
        public float moveSpeed = 2f;
        public float detectionRange = 5f;
        public float stopDistance = 1f;
        
        [Header("调试设置")]
        public bool debugMode = false;
        
        // 组件引用
        private Rigidbody2D rb;
        private Transform target;
        
        // 状态
        private bool canMove = true;
        private Vector2 lastDirection = Vector2.zero;
        
        private void Awake()
        {
            rb = GetComponent<Rigidbody2D>();
            if (rb == null)
            {
                rb = gameObject.AddComponent<Rigidbody2D>();
                rb.gravityScale = 0f;
                rb.freezeRotation = true;
            }
        }
        
        private void Start()
        {
            // 寻找玩家目标
            FindTarget();
        }
        
        private void Update()
        {
            if (!canMove) return;
            
            // 如果没有目标，尝试重新寻找
            if (target == null)
            {
                FindTarget();
                return;
            }
            
            // 计算到目标的距离
            float distanceToTarget = Vector2.Distance(transform.position, target.position);
            
            if (distanceToTarget <= detectionRange && distanceToTarget > stopDistance)
            {
                // 移动向目标
                MoveTowardsTarget();
            }
            else
            {
                // 停止移动
                rb.velocity = Vector2.zero;
            }
        }
        
        private void FindTarget()
        {
            var player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                target = player.transform;
                if (debugMode)
                {
                    Debug.Log($"{gameObject.name} 找到玩家目标: {target.name}");
                }
            }
            else
            {
                if (debugMode)
                {
                    Debug.LogWarning($"{gameObject.name} 找不到玩家目标");
                }
            }
        }
        
        private void MoveTowardsTarget()
        {
            Vector2 direction = (target.position - transform.position).normalized;
            lastDirection = direction;
            
            // 设置速度
            rb.velocity = direction * moveSpeed;
            
            if (debugMode)
            {
                Debug.Log($"{gameObject.name} 移动向目标，方向: {direction}, 速度: {rb.velocity}");
            }
        }
        
        public void SetCanMove(bool canMove)
        {
            this.canMove = canMove;
            if (!canMove)
            {
                rb.velocity = Vector2.zero;
            }
        }
        
        public void SetTarget(Transform newTarget)
        {
            target = newTarget;
        }
        
        private void OnDrawGizmosSelected()
        {
            // 绘制检测范围
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRange);
            
            // 绘制停止距离
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, stopDistance);
            
            // 绘制到目标的线
            if (target != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawLine(transform.position, target.position);
            }
        }
    }
}
