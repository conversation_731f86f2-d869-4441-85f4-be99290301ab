using UnityEngine;
using RoguelikeGame.Characters;

namespace RoguelikeGame.Core
{
    /// <summary>
    /// 输入管理器，统一处理玩家输入
    /// </summary>
    public class InputManager : MonoBehaviour
    {
        [Header("输入设置")]
        public bool enableKeyboardInput = true;
        public bool enableMouseInput = true;
        public bool enableGamepadInput = true;

        [Header("键盘设置")]
        public KeyCode pauseKey = KeyCode.Escape;
        public KeyCode skillKey = KeyCode.Space;
        public KeyCode interactKey = KeyCode.E;
        public KeyCode inventoryKey = KeyCode.Tab;

        [Header("鼠标设置")]
        public bool mouseControlsMovement = false;
        public bool mouseControlsAiming = true;

        [Header("手柄设置")]
        public string horizontalAxis = "Horizontal";
        public string verticalAxis = "Vertical";
        public string skillButton = "Fire1";
        public string pauseButton = "Cancel";

        [Header("调试设置")]
        public bool debugMode = false;
        public bool showInputDebug = false;

        // 单例模式
        public static InputManager Instance { get; private set; }

        // 输入状态
        public Vector2 MovementInput { get; private set; }
        public Vector2 AimDirection { get; private set; }
        public bool SkillPressed { get; private set; }
        public bool SkillHeld { get; private set; }
        public bool PausePressed { get; private set; }
        public bool InteractPressed { get; private set; }
        public bool InventoryPressed { get; private set; }

        // 输入启用状态
        public bool InputEnabled { get; private set; } = true;

        // 组件引用
        private Camera playerCamera;
        private PlayerController player;

        // 事件系统
        public System.Action<Vector2> OnMovementInput;
        public System.Action<Vector2> OnAimInput;
        public System.Action OnSkillInput;
        public System.Action OnPauseInput;
        public System.Action OnInteractInput;
        public System.Action OnInventoryInput;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeInputManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            FindReferences();
        }

        private void Update()
        {
            if (!InputEnabled) return;

            HandleInput();
            UpdateDebugDisplay();
        }

        private void InitializeInputManager()
        {
            // 设置光标状态
            SetCursorState(false);
        }

        private void FindReferences()
        {
            if (playerCamera == null)
            {
                playerCamera = Camera.main;
                if (playerCamera == null)
                {
                    playerCamera = FindObjectOfType<Camera>();
                }
            }

            if (player == null)
            {
                player = FindObjectOfType<PlayerController>();
            }
        }

        private void HandleInput()
        {
            // 处理移动输入
            HandleMovementInput();

            // 处理瞄准输入
            HandleAimInput();

            // 处理技能输入
            HandleSkillInput();

            // 处理系统输入
            HandleSystemInput();
        }

        private void HandleMovementInput()
        {
            Vector2 movement = Vector2.zero;

            // 键盘输入 - 直接检测WASD键
            if (enableKeyboardInput)
            {
                // 直接检测WASD键
                if (Input.GetKey(KeyCode.W)) movement.y += 1f;
                if (Input.GetKey(KeyCode.S)) movement.y -= 1f;
                if (Input.GetKey(KeyCode.A)) movement.x -= 1f;
                if (Input.GetKey(KeyCode.D)) movement.x += 1f;

                // 强制调试输出 - 总是显示WASD状态
                bool anyKeyPressed = Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.D);
                if (anyKeyPressed)
                {
                    Debug.Log($"InputManager WASD状态: W={Input.GetKey(KeyCode.W)}, A={Input.GetKey(KeyCode.A)}, S={Input.GetKey(KeyCode.S)}, D={Input.GetKey(KeyCode.D)}");
                    Debug.Log($"InputManager计算的movement: {movement}");
                }

                // 也尝试使用轴输入作为备用
                if (movement == Vector2.zero)
                {
                    movement.x = Input.GetAxisRaw("Horizontal");
                    movement.y = Input.GetAxisRaw("Vertical");

                    if (movement != Vector2.zero)
                    {
                        Debug.Log($"InputManager使用轴输入: Horizontal={movement.x}, Vertical={movement.y}");
                    }
                }

                // 调试输出
                if (debugMode && movement != Vector2.zero)
                {
                    Debug.Log($"InputManager检测到移动输入: {movement}");
                }
            }

            // 手柄输入
            if (enableGamepadInput)
            {
                float gamepadX = Input.GetAxis(horizontalAxis);
                float gamepadY = Input.GetAxis(verticalAxis);

                if (Mathf.Abs(gamepadX) > 0.1f || Mathf.Abs(gamepadY) > 0.1f)
                {
                    movement.x = gamepadX;
                    movement.y = gamepadY;
                }
            }

            // 鼠标移动控制（可选）
            if (enableMouseInput && mouseControlsMovement && playerCamera != null)
            {
                // 检查摄像机是否有效
                if (playerCamera.pixelRect.width > 0 && playerCamera.pixelRect.height > 0)
                {
                    Vector3 mousePosition = Input.mousePosition;
                    mousePosition.z = 10f; // 设置Z距离
                    Vector3 worldPosition = playerCamera.ScreenToWorldPoint(mousePosition);

                    if (player != null)
                    {
                        Vector2 direction = (worldPosition - player.transform.position).normalized;
                        float distance = Vector2.Distance(worldPosition, player.transform.position);

                        if (distance > 1f) // 死区
                        {
                            movement = direction;
                        }
                    }
                }
            }

            // 设置MovementInput前的调试
            Vector2 oldMovementInput = MovementInput;
            MovementInput = movement.normalized;

            // 强制调试输出 - 显示MovementInput的变化
            if (movement != Vector2.zero || oldMovementInput != Vector2.zero)
            {
                Debug.Log($"InputManager设置MovementInput: {oldMovementInput} -> {MovementInput}");
            }

            // 触发移动事件
            if (MovementInput != Vector2.zero)
            {
                OnMovementInput?.Invoke(MovementInput);
                Debug.Log($"InputManager触发移动事件: {MovementInput}");
            }
        }

        private void HandleAimInput()
        {
            Vector2 aimDirection = Vector2.zero;

            if (enableMouseInput && mouseControlsAiming && playerCamera != null)
            {
                // 检查摄像机是否有效
                if (playerCamera.pixelRect.width > 0 && playerCamera.pixelRect.height > 0)
                {
                    // 鼠标瞄准
                    Vector3 mousePosition = Input.mousePosition;
                    mousePosition.z = 10f; // 设置Z距离
                    Vector3 worldPosition = playerCamera.ScreenToWorldPoint(mousePosition);

                    if (player != null)
                    {
                        aimDirection = (worldPosition - player.transform.position).normalized;
                    }
                }
                else
                {
                    // 摄像机无效时使用移动方向
                    aimDirection = MovementInput;
                }
            }
            else
            {
                // 使用移动方向作为瞄准方向
                aimDirection = MovementInput;
            }

            if (aimDirection != Vector2.zero)
            {
                AimDirection = aimDirection;
                OnAimInput?.Invoke(AimDirection);
            }
        }

        private void HandleSkillInput()
        {
            // 技能按键
            bool skillDown = false;
            bool skillHeld = false;

            if (enableKeyboardInput)
            {
                skillDown = Input.GetKeyDown(skillKey);
                skillHeld = Input.GetKey(skillKey);
            }

            if (enableGamepadInput)
            {
                skillDown = skillDown || Input.GetButtonDown(skillButton);
                skillHeld = skillHeld || Input.GetButton(skillButton);
            }

            if (enableMouseInput)
            {
                skillDown = skillDown || Input.GetMouseButtonDown(0);
                skillHeld = skillHeld || Input.GetMouseButton(0);
            }

            SkillPressed = skillDown;
            SkillHeld = skillHeld;

            if (SkillPressed)
            {
                OnSkillInput?.Invoke();
            }
        }

        private void HandleSystemInput()
        {
            // 暂停输入
            bool pauseDown = false;
            if (enableKeyboardInput)
            {
                pauseDown = Input.GetKeyDown(pauseKey);
            }
            if (enableGamepadInput)
            {
                pauseDown = pauseDown || Input.GetButtonDown(pauseButton);
            }

            PausePressed = pauseDown;
            if (PausePressed)
            {
                OnPauseInput?.Invoke();
            }

            // 交互输入
            bool interactDown = false;
            if (enableKeyboardInput)
            {
                interactDown = Input.GetKeyDown(interactKey);
            }

            InteractPressed = interactDown;
            if (InteractPressed)
            {
                OnInteractInput?.Invoke();
            }

            // 背包输入
            bool inventoryDown = false;
            if (enableKeyboardInput)
            {
                inventoryDown = Input.GetKeyDown(inventoryKey);
            }

            InventoryPressed = inventoryDown;
            if (InventoryPressed)
            {
                OnInventoryInput?.Invoke();
            }
        }

        private void UpdateDebugDisplay()
        {
            if (!debugMode || !showInputDebug) return;

            // 在屏幕上显示输入信息
            string debugText = $"Movement: {MovementInput}\n";
            debugText += $"Aim: {AimDirection}\n";
            debugText += $"Skill: {SkillHeld}\n";
            debugText += $"Input Enabled: {InputEnabled}";

            // TODO: 显示调试文本
        }

        #region 输入控制

        public void SetInputEnabled(bool enabled)
        {
            InputEnabled = enabled;

            if (!enabled)
            {
                // 清空所有输入状态
                MovementInput = Vector2.zero;
                AimDirection = Vector2.zero;
                SkillPressed = false;
                SkillHeld = false;
                PausePressed = false;
                InteractPressed = false;
                InventoryPressed = false;
            }
        }

        public void SetCursorState(bool visible)
        {
            Cursor.visible = visible;
            Cursor.lockState = visible ? CursorLockMode.None : CursorLockMode.Locked;
        }

        public void EnableKeyboardInput(bool enable)
        {
            enableKeyboardInput = enable;
        }

        public void EnableMouseInput(bool enable)
        {
            enableMouseInput = enable;
        }

        public void EnableGamepadInput(bool enable)
        {
            enableGamepadInput = enable;
        }

        #endregion

        #region 输入查询

        public bool IsMoving()
        {
            return MovementInput.magnitude > 0.1f;
        }

        public bool IsAiming()
        {
            return AimDirection.magnitude > 0.1f;
        }

        public Vector2 GetMovementDirection()
        {
            return MovementInput;
        }

        public Vector2 GetAimDirection()
        {
            return AimDirection;
        }

        #endregion

        #region 振动反馈

        public void TriggerVibration(float duration = 0.1f, float intensity = 1f)
        {
            if (!enableGamepadInput) return;

            // TODO: 实现手柄振动
            if (debugMode)
            {
                Debug.Log($"触发振动: 持续时间={duration}, 强度={intensity}");
            }
        }

        #endregion

        private void OnGUI()
        {
            if (!debugMode || !showInputDebug) return;

            GUILayout.BeginArea(new Rect(10, 100, 300, 200));
            GUILayout.Label("=== 输入调试 ===");
            GUILayout.Label($"移动输入: {MovementInput}");
            GUILayout.Label($"瞄准方向: {AimDirection}");
            GUILayout.Label($"技能按下: {SkillPressed}");
            GUILayout.Label($"技能持续: {SkillHeld}");
            GUILayout.Label($"暂停按下: {PausePressed}");
            GUILayout.Label($"输入启用: {InputEnabled}");
            GUILayout.EndArea();
        }
    }
}
