using UnityEngine;

namespace RoguelikeGame.Core
{
    /// <summary>
    /// 游戏启动器配置，可以在编辑器中设置
    /// </summary>
    [CreateAssetMenu(fileName = "GameLauncherConfig", menuName = "RoguelikeGame/Game Launcher Config")]
    public class GameLauncherConfig : ScriptableObject
    {
        [Header("启动设置")]
        public bool autoStartGame = true;
        public bool showMainMenu = true;
        public bool enableTestMode = false;

        [Header("游戏设置")]
        public bool createDefaultPlayer = true;
        public bool createDefaultEnemies = true;
        public Vector3 playerStartPosition = Vector3.zero;
        
        [Header("素材设置")]
        [Tooltip("玩家角色精灵图，如果为空则使用默认方块")]
        public Sprite playerSprite;
        
        [Tooltip("敌人精灵图，如果为空则使用默认方块")]
        public Sprite enemySprite;
        
        [Tooltip("背景瓦片，用于创建地面")]
        public Sprite backgroundTile;
        
        [Tooltip("背景材质")]
        public Material backgroundMaterial;
        
        [Header("角色设置")]
        [Tooltip("玩家精灵缩放比例")]
        [Range(0.5f, 3.0f)]
        public float playerScale = 1.5f;
        
        [Tooltip("敌人精灵缩放比例")]
        [Range(0.5f, 3.0f)]
        public float enemyScale = 1.0f;
        
        [Header("颜色设置（当没有自定义精灵时使用）")]
        public Color playerColor = Color.green;
        public Color normalEnemyColor = Color.red;
        public Color eliteEnemyColor = Color.magenta;
        public Color bossEnemyColor = Color.black;
    }
}
