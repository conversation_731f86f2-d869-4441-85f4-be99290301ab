using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
using RoguelikeGame.Levels;
using System;

namespace RoguelikeGame.Core
{
    /// <summary>
    /// 场景管理器，处理场景切换和加载
    /// </summary>
    public class SceneManager : MonoBehaviour
    {
        [Header("场景设置")]
        public string mainMenuSceneName = "MainMenu";
        public string gameplaySceneName = "Gameplay";
        public string loadingSceneName = "Loading";
        
        [Header("加载设置")]
        public bool useLoadingScreen = true;
        public float minimumLoadingTime = 1f;
        public bool preloadGameplayScene = false;
        
        [Header("调试设置")]
        public bool debugMode = false;
        
        // 单例模式
        public static SceneManager Instance { get; private set; }
        
        // 当前场景状态
        public string CurrentSceneName { get; private set; }
        public bool IsLoading { get; private set; }
        
        // 事件系统
        public System.Action<string> OnSceneLoadStarted;
        public System.Action<string> OnSceneLoadCompleted;
        public System.Action<float> OnLoadingProgress;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeSceneManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            CurrentSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
            
            if (preloadGameplayScene && CurrentSceneName == mainMenuSceneName)
            {
                StartCoroutine(PreloadSceneCoroutine(gameplaySceneName));
            }
        }
        
        private void InitializeSceneManager()
        {
            // 设置场景加载事件监听
            UnityEngine.SceneManagement.SceneManager.sceneLoaded += OnSceneLoaded;
            UnityEngine.SceneManagement.SceneManager.sceneUnloaded += OnSceneUnloaded;
        }
        
        private void OnDestroy()
        {
            // 清理事件监听
            UnityEngine.SceneManagement.SceneManager.sceneLoaded -= OnSceneLoaded;
            UnityEngine.SceneManagement.SceneManager.sceneUnloaded -= OnSceneUnloaded;
        }
        
        #region 场景切换
        
        public void LoadMainMenu()
        {
            LoadScene(mainMenuSceneName);
        }
        
        public void LoadGameplay()
        {
            LoadScene(gameplaySceneName);
        }
        
        public void LoadScene(string sceneName)
        {
            if (IsLoading)
            {
                if (debugMode)
                {
                    Debug.LogWarning($"场景正在加载中，忽略加载请求: {sceneName}");
                }
                return;
            }
            
            if (sceneName == CurrentSceneName)
            {
                if (debugMode)
                {
                    Debug.LogWarning($"尝试加载当前场景: {sceneName}");
                }
                return;
            }
            
            StartCoroutine(LoadSceneCoroutine(sceneName));
        }
        
        public void ReloadCurrentScene()
        {
            LoadScene(CurrentSceneName);
        }
        
        private IEnumerator LoadSceneCoroutine(string sceneName)
        {
            IsLoading = true;
            float startTime = Time.realtimeSinceStartup;
            
            // 触发加载开始事件
            OnSceneLoadStarted?.Invoke(sceneName);
            
            if (debugMode)
            {
                Debug.Log($"开始加载场景: {sceneName}");
            }
            
            // 如果使用加载屏幕
            if (useLoadingScreen && !string.IsNullOrEmpty(loadingSceneName))
            {
                yield return StartCoroutine(LoadSceneWithLoadingScreen(sceneName));
            }
            else
            {
                yield return StartCoroutine(LoadSceneDirectly(sceneName));
            }
            
            // 确保最小加载时间
            float elapsedTime = Time.realtimeSinceStartup - startTime;
            if (elapsedTime < minimumLoadingTime)
            {
                yield return new WaitForSecondsRealtime(minimumLoadingTime - elapsedTime);
            }
            
            CurrentSceneName = sceneName;
            IsLoading = false;
            
            // 触发加载完成事件
            OnSceneLoadCompleted?.Invoke(sceneName);
            
            if (debugMode)
            {
                Debug.Log($"场景加载完成: {sceneName}");
            }
        }
        
        private IEnumerator LoadSceneWithLoadingScreen(string targetSceneName)
        {
            // 先加载加载屏幕
            AsyncOperation loadingOperation = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(loadingSceneName);
            yield return loadingOperation;
            
            // 等待一帧确保加载屏幕显示
            yield return null;
            
            // 加载目标场景
            AsyncOperation targetOperation = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(targetSceneName);
            targetOperation.allowSceneActivation = false;
            
            // 更新加载进度
            while (targetOperation.progress < 0.9f)
            {
                OnLoadingProgress?.Invoke(targetOperation.progress);
                yield return null;
            }
            
            // 完成加载
            OnLoadingProgress?.Invoke(1f);
            targetOperation.allowSceneActivation = true;
            
            yield return targetOperation;
        }
        
        private IEnumerator LoadSceneDirectly(string sceneName)
        {
            AsyncOperation operation = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(sceneName);
            
            while (!operation.isDone)
            {
                OnLoadingProgress?.Invoke(operation.progress);
                yield return null;
            }
            
            OnLoadingProgress?.Invoke(1f);
        }
        
        #endregion
        
        #region 场景预加载
        
        private IEnumerator PreloadSceneCoroutine(string sceneName)
        {
            if (debugMode)
            {
                Debug.Log($"开始预加载场景: {sceneName}");
            }
            
            AsyncOperation operation = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Additive);
            operation.allowSceneActivation = false;
            
            yield return operation;
            
            if (debugMode)
            {
                Debug.Log($"场景预加载完成: {sceneName}");
            }
        }
        
        public void ActivatePreloadedScene(string sceneName)
        {
            Scene scene = UnityEngine.SceneManagement.SceneManager.GetSceneByName(sceneName);
            if (scene.isLoaded)
            {
                UnityEngine.SceneManagement.SceneManager.SetActiveScene(scene);
                
                // 卸载其他场景
                for (int i = 0; i < UnityEngine.SceneManagement.SceneManager.sceneCount; i++)
                {
                    Scene otherScene = UnityEngine.SceneManagement.SceneManager.GetSceneAt(i);
                    if (otherScene.name != sceneName && otherScene.name != gameObject.scene.name)
                    {
                        UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync(otherScene);
                    }
                }
                
                CurrentSceneName = sceneName;
            }
        }
        
        #endregion
        
        #region 场景事件处理
        
        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            if (debugMode)
            {
                Debug.Log($"场景已加载: {scene.name}, 模式: {mode}");
            }
            
            // 根据场景类型执行特定初始化
            switch (scene.name)
            {
                case var name when name == mainMenuSceneName:
                    OnMainMenuLoaded();
                    break;
                case var name when name == gameplaySceneName:
                    OnGameplayLoaded();
                    break;
            }
        }
        
        private void OnSceneUnloaded(Scene scene)
        {
            if (debugMode)
            {
                Debug.Log($"场景已卸载: {scene.name}");
            }
        }
        
        private void OnMainMenuLoaded()
        {
            // 主菜单场景加载完成
            if (GameManager.Instance != null)
            {
                GameManager.Instance.ChangeGameState(GameState.MainMenu);
            }
            
            // 播放主菜单音乐
            AudioManager.Instance?.PlayMainMenuMusic();
        }
        
        private void OnGameplayLoaded()
        {
            // 游戏场景加载完成
            if (GameManager.Instance != null)
            {
                GameManager.Instance.ChangeGameState(GameState.Playing);
            }
            
            // 播放游戏音乐
            AudioManager.Instance?.PlayGameplayMusic();
            
            // 初始化关卡
            if (LevelManager.Instance != null)
            {
                LevelManager.Instance.StartLevel();
            }
        }
        
        #endregion
        
        #region 公共接口
        
        public bool IsSceneLoaded(string sceneName)
        {
            Scene scene = UnityEngine.SceneManagement.SceneManager.GetSceneByName(sceneName);
            return scene.isLoaded;
        }
        
        public void QuitGame()
        {
            if (debugMode)
            {
                Debug.Log("退出游戏");
            }
            
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }

        internal static AsyncOperation LoadSceneAsync(string sceneName)
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}
