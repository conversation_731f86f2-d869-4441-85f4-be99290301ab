using UnityEngine;

namespace RoguelikeGame.Core
{
    /// <summary>
    /// 在场景中自动创建GameLauncher的辅助脚本
    /// 这个脚本可以放在场景中的空GameObject上，用于配置游戏启动器
    /// </summary>
    public class GameLauncherSetup : MonoBehaviour
    {
        [Header("配置文件")]
        [Tooltip("游戏启动器配置文件，包含所有设置和素材")]
        public GameLauncherConfig config;
        
        [Header("直接设置素材（如果没有配置文件）")]
        [Tooltip("玩家角色精灵图，如果为空则使用默认方块")]
        public Sprite playerSprite;
        
        [Tooltip("敌人精灵图，如果为空则使用默认方块")]
        public Sprite enemySprite;
        
        [Tooltip("背景瓦片，用于创建地面")]
        public Sprite backgroundTile;
        
        [Tooltip("背景材质")]
        public Material backgroundMaterial;
        
        [Header("游戏设置")]
        public bool autoStartGame = true;
        public bool showMainMenu = true;
        public bool enableTestMode = false;
        public bool createDefaultPlayer = true;
        public bool createDefaultEnemies = true;
        
        [Header("缩放设置")]
        [Range(0.5f, 3.0f)]
        public float playerScale = 1.5f;
        [Range(0.5f, 3.0f)]
        public float enemyScale = 1.0f;
        
        [Header("颜色设置")]
        public Color playerColor = Color.green;
        public Color normalEnemyColor = Color.red;
        public Color eliteEnemyColor = Color.magenta;
        public Color bossEnemyColor = Color.black;

        private void Awake()
        {
            // 检查是否已经有GameLauncher
            var existingLauncher = FindObjectOfType<GameLauncher>();
            if (existingLauncher != null)
            {
                Debug.Log("GameLauncher已存在，跳过创建");
                return;
            }

            // 创建GameLauncher
            GameObject launcherObj = new GameObject("GameLauncher");
            var launcher = launcherObj.AddComponent<GameLauncher>();
            
            // 如果有配置文件，直接使用
            if (config != null)
            {
                launcher.config = config;
                Debug.Log($"使用配置文件: {config.name}");
            }
            else
            {
                // 创建临时配置
                var tempConfig = ScriptableObject.CreateInstance<GameLauncherConfig>();
                tempConfig.autoStartGame = autoStartGame;
                tempConfig.showMainMenu = showMainMenu;
                tempConfig.enableTestMode = enableTestMode;
                tempConfig.createDefaultPlayer = createDefaultPlayer;
                tempConfig.createDefaultEnemies = createDefaultEnemies;
                tempConfig.playerSprite = playerSprite;
                tempConfig.enemySprite = enemySprite;
                tempConfig.backgroundTile = backgroundTile;
                tempConfig.backgroundMaterial = backgroundMaterial;
                tempConfig.playerScale = playerScale;
                tempConfig.enemyScale = enemyScale;
                tempConfig.playerColor = playerColor;
                tempConfig.normalEnemyColor = normalEnemyColor;
                tempConfig.eliteEnemyColor = eliteEnemyColor;
                tempConfig.bossEnemyColor = bossEnemyColor;
                
                launcher.config = tempConfig;
                Debug.Log("使用直接设置的素材和配置");
            }
            
            Debug.Log("GameLauncher创建完成");
        }
    }
}
