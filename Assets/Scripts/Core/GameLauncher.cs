using UnityEngine;
using UnityEngine.UI;
using RoguelikeGame.Core;
using RoguelikeGame.Characters;
using RoguelikeGame.Utils;

namespace RoguelikeGame.Core
{
    /// <summary>
    /// 游戏启动器，负责游戏的初始化和启动流程
    /// </summary>
    public class GameLauncher : MonoBehaviour
    {
        [Header("启动设置")]
        public bool autoStartGame = true;
        public bool showMainMenu = true;
        public bool enableTestMode = false;

        [Header("游戏设置")]
        public bool createDefaultPlayer = true;
        public bool createDefaultEnemies = true;
        public Vector3 playerStartPosition = Vector3.zero;

        private bool gameStarted = false;
        private bool showMenu = true;
        private GameObject menuCanvas;
        private bool useSimpleGUI = true; // 临时使用简单GUI进行测试

        // 调试变量
        private bool mousePressed = false;
        private bool mouseReleased = false;
        private Vector2 lastMousePos = Vector2.zero;
        private int buttonHover = -1; // -1=无, 0=开始游戏, 1=测试模式, 2=退出游戏

        private void Start()
        {
            Debug.Log("=== 游戏启动器启动 ===");
            Debug.Log($"autoStartGame: {autoStartGame}");
            Debug.Log($"showMainMenu: {showMainMenu}");
            Debug.Log($"enableTestMode: {enableTestMode}");

            // 初始化核心系统
            InitializeCoreSystem();

            if (autoStartGame && !showMainMenu)
            {
                Debug.Log("自动启动游戏");
                StartGame();
            }
            else if (showMainMenu)
            {
                Debug.Log("显示主菜单");
                showMenu = true;

                if (useSimpleGUI)
                {
                    Debug.Log("使用简单GUI模式");
                }
                else
                {
                    CreateMenuUI();
                }
            }

            Debug.Log($"最终状态 - showMenu: {showMenu}, gameStarted: {gameStarted}");
        }

        private void Update()
        {
            // 检测鼠标输入
            Vector2 currentMousePos = Input.mousePosition;
            if (currentMousePos != lastMousePos)
            {
                lastMousePos = currentMousePos;
            }

            // 检测鼠标按下和释放
            if (Input.GetMouseButtonDown(0))
            {
                mousePressed = true;
                Debug.Log($"鼠标按下！位置: {Input.mousePosition}");
            }

            if (Input.GetMouseButtonUp(0))
            {
                mouseReleased = true;
                Debug.Log($"鼠标释放！位置: {Input.mousePosition}");
            }

            // 检测键盘输入作为备用
            if (Input.GetKeyDown(KeyCode.Space))
            {
                Debug.Log("空格键被按下 - 尝试开始游戏");
                StartGame();
            }

            if (Input.GetKeyDown(KeyCode.T))
            {
                Debug.Log("T键被按下 - 尝试进入测试模式");
                enableTestMode = true;
                CreateGameManagers();
                showMenu = false;
            }

            if (Input.GetKeyDown(KeyCode.Escape))
            {
                Debug.Log("ESC键被按下 - 尝试退出游戏");
                Application.Quit();
            }

            // 调试玩家移动
            if (gameStarted && !showMenu)
            {
                // 检测WASD输入
                if (Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.D))
                {
                    Debug.Log($"检测到WASD输入: W={Input.GetKey(KeyCode.W)}, A={Input.GetKey(KeyCode.A)}, S={Input.GetKey(KeyCode.S)}, D={Input.GetKey(KeyCode.D)}");

                    // 检查InputManager
                    if (InputManager.Instance != null)
                    {
                        Vector2 movement = InputManager.Instance.GetMovementDirection();
                        Debug.Log($"InputManager移动输入: {movement}");
                    }
                    else
                    {
                        Debug.LogWarning("InputManager.Instance为null！");
                    }

                    // 检查玩家
                    var player = FindObjectOfType<PlayerController>();
                    if (player != null)
                    {
                        Debug.Log($"找到玩家，位置: {player.transform.position}");
                        Debug.Log($"玩家状态 - MoveSpeed: {player.MoveSpeed}, CanMove: {player.CanMove}, IsAlive: {player.IsAlive}");

                        var rb = player.GetComponent<Rigidbody2D>();
                        if (rb != null)
                        {
                            Debug.Log($"Rigidbody2D速度: {rb.velocity}");
                        }
                    }
                    else
                    {
                        Debug.LogWarning("找不到PlayerController！");
                    }
                }
            }
        }

        private void InitializeCoreSystem()
        {
            Debug.Log("初始化核心系统...");

            // 创建摄像机
            CreateMainCamera();

            // 创建游戏管理器
            CreateGameManagers();

            Debug.Log("核心系统初始化完成");
        }

        private void CreateMainCamera()
        {
            Camera existingCamera = Camera.main;
            if (existingCamera == null)
            {
                existingCamera = FindObjectOfType<Camera>();
            }

            if (existingCamera != null)
            {
                Debug.Log("摄像机已存在");
                return;
            }

            GameObject cameraObj = new GameObject("Main Camera");
            cameraObj.tag = "MainCamera";

            var camera = cameraObj.AddComponent<Camera>();
            camera.orthographic = true;
            camera.orthographicSize = 5f; // 减小尺寸，提高清晰度
            camera.backgroundColor = Color.black;
            camera.clearFlags = CameraClearFlags.SolidColor;

            // 设置更好的渲染质量
            camera.allowHDR = false;
            camera.allowMSAA = false;

            cameraObj.transform.position = new Vector3(0, 0, -10);

            Debug.Log("创建主摄像机完成");
        }

        private void CreateGameManagers()
        {
            // 创建GameManager
            if (GameManager.Instance == null)
            {
                GameObject gameManagerObj = new GameObject("GameManager");
                gameManagerObj.AddComponent<GameManager>();
                Debug.Log("创建GameManager");
            }

            // 创建InputManager
            if (InputManager.Instance == null)
            {
                GameObject inputManagerObj = new GameObject("InputManager");
                var inputManager = inputManagerObj.AddComponent<InputManager>();

                // 暂时禁用InputManager的GUI调试，避免重叠
                inputManager.debugMode = true;
                inputManager.showInputDebug = false; // 关闭GUI显示，只保留Console日志

                Debug.Log("创建InputManager（调试模式已启用）");
            }

            // 暂时不创建LevelManager，避免立即触发关卡完成
            // TODO: 稍后在需要时创建LevelManager
            Debug.Log("暂时跳过LevelManager创建，避免关卡完成问题");

            // 如果启用测试模式，创建GameTester
            if (enableTestMode)
            {
                var existingTester = FindObjectOfType<GameTester>();
                if (existingTester == null)
                {
                    GameObject testerObj = new GameObject("GameTester");
                    testerObj.AddComponent<GameTester>();
                    Debug.Log("创建GameTester（测试模式）");
                }
            }
        }

        public void StartGame()
        {
            Debug.Log("StartGame方法被调用！");

            if (gameStarted)
            {
                Debug.Log("游戏已经开始，跳过");
                return;
            }

            Debug.Log("=== 开始游戏 ===");

            try
            {
                // 确保时间缩放正常
                Time.timeScale = 1f;
                Debug.Log($"设置Time.timeScale = {Time.timeScale}");
                Debug.Log($"Time.fixedDeltaTime = {Time.fixedDeltaTime}");
                Debug.Log($"Time.deltaTime = {Time.deltaTime}");
                Debug.Log($"Time.time = {Time.time}");
                Debug.Log($"Time.fixedTime = {Time.fixedTime}");

                gameStarted = true;
                showMenu = false;

                // 隐藏菜单UI
                if (menuCanvas != null)
                {
                    menuCanvas.SetActive(false);
                }

                // 如果使用简单GUI，只需要设置showMenu = false即可

                Debug.Log("设置游戏状态完成");

                // 创建游戏世界
                CreateGameWorld();

                // 不调用GameManager.StartGame()，因为它会尝试加载场景
                // 直接在当前场景中开始游戏
                Debug.Log("在当前场景中开始游戏，跳过场景加载");

                if (GameManager.Instance != null)
                {
                    // 使用ChangeGameState方法设置游戏状态
                    GameManager.Instance.ChangeGameState(GameState.Playing);
                    Debug.Log("设置游戏状态为Playing");
                }
                else
                {
                    Debug.LogWarning("GameManager.Instance为null");
                }

                Debug.Log("游戏启动完成！");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"启动游戏时发生错误: {e.Message}");
                Debug.LogError($"堆栈跟踪: {e.StackTrace}");
            }
        }

        private void CreateGameWorld()
        {
            Debug.Log("创建游戏世界...");

            // 创建网格背景
            CreateGridBackground();

            // 创建参考点
            CreateReferencePoints();

            if (createDefaultPlayer)
            {
                CreatePlayer();
            }

            if (createDefaultEnemies)
            {
                CreateEnemies();
            }

            Debug.Log("游戏世界创建完成");
        }

        private void CreateReferencePoints()
        {
            Debug.Log("创建参考点...");

            // 创建原点参考点 (0,0) - 红色
            CreateReferencePoint("Origin", Vector3.zero, Color.red, 1.0f);

            // 创建方向参考点
            CreateReferencePoint("North", new Vector3(0, 2, 0), Color.blue, 0.5f);
            CreateReferencePoint("East", new Vector3(2, 0, 0), Color.yellow, 0.5f);
            CreateReferencePoint("South", new Vector3(0, -2, 0), Color.cyan, 0.5f);
            CreateReferencePoint("West", new Vector3(-2, 0, 0), Color.magenta, 0.5f);

            Debug.Log("参考点创建完成");
        }

        private void CreateReferencePoint(string name, Vector3 position, Color color, float size)
        {
            GameObject refPoint = new GameObject($"RefPoint_{name}");
            refPoint.transform.position = position;

            var spriteRenderer = refPoint.AddComponent<SpriteRenderer>();
            spriteRenderer.sprite = CreateSquareSprite();
            spriteRenderer.color = color;
            spriteRenderer.transform.localScale = Vector3.one * size;
            spriteRenderer.sortingOrder = 10; // 确保在最上层

            Debug.Log($"创建参考点: {name} 在位置 {position}, 颜色 {color}");
        }

        private void CreatePlayer()
        {
            Debug.Log("=== 开始创建玩家 ===");

            var existingPlayer = FindObjectOfType<PlayerController>();
            if (existingPlayer != null)
            {
                Debug.Log($"玩家已存在，跳过创建。现有玩家: {existingPlayer.name}");

                // 强制启用调试模式
                existingPlayer.debugMode = true;
                Debug.Log($"为现有玩家启用调试模式: {existingPlayer.debugMode}");
                return;
            }

            Debug.Log("创建玩家GameObject...");
            GameObject playerObj = new GameObject("Player");
            playerObj.layer = 0;
            Debug.Log($"玩家GameObject创建完成: {playerObj.name}");

            // 添加视觉组件
            var spriteRenderer = playerObj.AddComponent<SpriteRenderer>();
            spriteRenderer.sprite = CreateSquareSprite();
            spriteRenderer.color = new Color(0f, 1f, 0f, 1f); // 纯绿色，确保不会与红色混合
            spriteRenderer.transform.localScale = Vector3.one * 1.5f; // 让玩家更大一点
            spriteRenderer.sortingOrder = 15; // 确保在所有参考点之上

            // 添加物理组件
            var rb = playerObj.AddComponent<Rigidbody2D>();
            rb.gravityScale = 0f;
            rb.freezeRotation = true;

            var collider = playerObj.AddComponent<CircleCollider2D>();
            collider.radius = 0.5f;

            // 添加玩家控制器
            Debug.Log("正在添加PlayerController组件...");
            var playerController = playerObj.AddComponent<PlayerController>();
            Debug.Log($"PlayerController组件添加完成: {playerController}");

            // 启用调试模式
            Debug.Log("启用PlayerController调试模式...");
            playerController.debugMode = true;
            Debug.Log($"调试模式已启用: {playerController.debugMode}");

            // 创建角色数据
            Debug.Log("创建角色数据...");
            var characterData = CreateDefaultCharacterData();
            Debug.Log($"设置角色数据前 - 角色数据: {characterData}, 移动速度: {characterData.baseMoveSpeed}");

            Debug.Log("设置角色数据...");
            playerController.SetCharacterData(characterData);

            Debug.Log($"设置角色数据后 - 玩家移动速度: {playerController.MoveSpeed}");

            // 设置位置 - 偏离原点以避免与参考点重叠
            Vector3 offsetStartPosition = new Vector3(1.5f, 1.5f, 0f);
            playerObj.transform.position = offsetStartPosition;
            Debug.Log($"玩家位置设置为: {offsetStartPosition} (偏离原点以避免重叠)");

            Debug.Log("=== 创建玩家完成 ===");
        }

        private void CreateEnemies()
        {
            // 创建几个不同类型的敌人，使用新的颜色系统
            CreateEnemyWithData("Enemy_1", CreateNormalEnemyData("普通敌人1", Color.red), new Vector3(5, 0, 0));
            CreateEnemyWithData("Enemy_2", CreateNormalEnemyData("普通敌人2", Color.red), new Vector3(-5, 0, 0));
            CreateEnemyWithData("Elite_Enemy", CreateEliteEnemyData("精英敌人", Color.magenta), new Vector3(0, 5, 0));
            CreateEnemyWithData("Boss_Enemy", CreateBossEnemyData("Boss敌人", Color.black), new Vector3(0, -5, 0));

            Debug.Log("创建敌人完成 - 使用新的颜色系统");
        }

        private void CreateEnemyWithData(string name, RoguelikeGame.Enemies.EnemyData enemyData, Vector3 position)
        {
            GameObject enemyObj = new GameObject(name);
            enemyObj.layer = 0;

            // 添加物理组件
            var rb = enemyObj.AddComponent<Rigidbody2D>();
            rb.gravityScale = 0f;
            rb.freezeRotation = true;

            var collider = enemyObj.AddComponent<CircleCollider2D>();
            collider.radius = 0.5f;

            // 添加敌人血量组件
            var enemyHealth = enemyObj.AddComponent<RoguelikeGame.Enemies.EnemyHealth>();

            // 添加敌人控制器
            var enemyController = enemyObj.AddComponent<RoguelikeGame.Enemies.EnemyController>();
            enemyController.enemyData = enemyData;

            // 设置位置
            enemyObj.transform.position = position;

            Debug.Log($"创建敌人: {name} ({enemyData.enemyName}) 在位置 {position}，颜色: {enemyData.enemyColor}");
        }

        private RoguelikeGame.Enemies.EnemyData CreateNormalEnemyData(string name, Color color)
        {
            var enemyData = ScriptableObject.CreateInstance<RoguelikeGame.Enemies.EnemyData>();
            enemyData.enemyName = name;
            enemyData.enemyType = RoguelikeGame.Enemies.EnemyType.Normal;
            enemyData.enemyColor = color;
            enemyData.baseHealth = 50f;
            enemyData.baseAttackDamage = 10f;
            enemyData.baseAttackSpeed = 1f;
            enemyData.baseMoveSpeed = 2f;
            enemyData.baseDefense = 0f;
            enemyData.detectionRange = 5f;
            enemyData.attackRange = 1.5f;
            enemyData.chaseRange = 8f;
            enemyData.patrolRange = 3f;
            enemyData.aiType = RoguelikeGame.Enemies.EnemyAIType.Aggressive;
            enemyData.experienceValue = 10;
            return enemyData;
        }

        private RoguelikeGame.Enemies.EnemyData CreateEliteEnemyData(string name, Color color)
        {
            var enemyData = ScriptableObject.CreateInstance<RoguelikeGame.Enemies.EnemyData>();
            enemyData.enemyName = name;
            enemyData.enemyType = RoguelikeGame.Enemies.EnemyType.Elite;
            enemyData.enemyColor = color;
            enemyData.baseHealth = 100f;
            enemyData.baseAttackDamage = 20f;
            enemyData.baseAttackSpeed = 1.2f;
            enemyData.baseMoveSpeed = 2.5f;
            enemyData.baseDefense = 5f;
            enemyData.detectionRange = 6f;
            enemyData.attackRange = 2f;
            enemyData.chaseRange = 10f;
            enemyData.patrolRange = 4f;
            enemyData.aiType = RoguelikeGame.Enemies.EnemyAIType.Cautious;
            enemyData.experienceValue = 25;
            return enemyData;
        }

        private RoguelikeGame.Enemies.EnemyData CreateBossEnemyData(string name, Color color)
        {
            var enemyData = ScriptableObject.CreateInstance<RoguelikeGame.Enemies.EnemyData>();
            enemyData.enemyName = name;
            enemyData.enemyType = RoguelikeGame.Enemies.EnemyType.Boss;
            enemyData.enemyColor = color;
            enemyData.baseHealth = 300f;
            enemyData.baseAttackDamage = 40f;
            enemyData.baseAttackSpeed = 0.8f;
            enemyData.baseMoveSpeed = 1.5f;
            enemyData.baseDefense = 15f;
            enemyData.detectionRange = 8f;
            enemyData.attackRange = 3f;
            enemyData.chaseRange = 12f;
            enemyData.patrolRange = 5f;
            enemyData.aiType = RoguelikeGame.Enemies.EnemyAIType.Tank;
            enemyData.experienceValue = 100;
            return enemyData;
        }

        private Sprite CreateSquareSprite()
        {
            // 创建一个简单的方形精灵
            Texture2D texture = new Texture2D(32, 32);
            Color[] pixels = new Color[32 * 32];

            for (int i = 0; i < pixels.Length; i++)
            {
                pixels[i] = Color.white;
            }

            texture.SetPixels(pixels);
            texture.Apply();

            return Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f));
        }

        private CharacterData CreateDefaultCharacterData()
        {
            var characterData = ScriptableObject.CreateInstance<CharacterData>();
            characterData.characterName = "英雄";
            characterData.description = "勇敢的冒险者";
            characterData.rarity = CharacterRarity.C;

            // 设置基础属性
            characterData.baseHealth = 100f;
            characterData.baseAttackDamage = 20f;
            characterData.baseAttackSpeed = 1f;
            characterData.baseMoveSpeed = 5f; // 确保移动速度不为0
            characterData.baseDefense = 5f;
            characterData.baseCriticalChance = 0.1f;
            characterData.baseCriticalMultiplier = 2f;

            Debug.Log($"创建角色数据 - 移动速度: {characterData.baseMoveSpeed}");

            return characterData;
        }

        private void CreateMenuUI()
        {
            Debug.Log("创建菜单UI...");

            // 创建Canvas
            menuCanvas = new GameObject("MenuCanvas");
            var canvas = menuCanvas.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 100; // 确保在最上层

            var canvasScaler = menuCanvas.AddComponent<CanvasScaler>();
            canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            canvasScaler.referenceResolution = new Vector2(1920, 1080);

            menuCanvas.AddComponent<GraphicRaycaster>();

            // 创建EventSystem（如果不存在）
            if (FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
            {
                var eventSystemObj = new GameObject("EventSystem");
                eventSystemObj.AddComponent<UnityEngine.EventSystems.EventSystem>();
                eventSystemObj.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
            }

            // 创建主面板
            var panel = CreatePanel(menuCanvas.transform, "MainPanel");

            // 创建标题
            CreateText(panel.transform, "=== Roguelike Game ===", 24, new Vector2(0, 100));

            // 创建状态信息
            CreateText(panel.transform, $"游戏状态: {(gameStarted ? "已开始" : "未开始")}", 16, new Vector2(0, 50));

            // 创建按钮
            CreateButton(panel.transform, "开始游戏", new Vector2(0, 0), () => {
                Debug.Log("点击了开始游戏按钮！");
                StartGame();
            });

            CreateButton(panel.transform, "测试模式", new Vector2(0, -60), () => {
                Debug.Log("点击了测试模式按钮！");
                enableTestMode = true;
                CreateGameManagers();
                if (menuCanvas != null)
                {
                    menuCanvas.SetActive(false);
                }
            });

            CreateButton(panel.transform, "退出游戏", new Vector2(0, -120), () => {
                Debug.Log("点击了退出游戏按钮！");
                Application.Quit();
            });

            Debug.Log("菜单UI创建完成");
        }

        private GameObject CreatePanel(Transform parent, string name)
        {
            var panelObj = new GameObject(name);
            panelObj.transform.SetParent(parent, false);

            var rectTransform = panelObj.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            var image = panelObj.AddComponent<Image>();
            image.color = new Color(0, 0, 0, 0.8f); // 半透明黑色背景

            return panelObj;
        }

        private GameObject CreateText(Transform parent, string text, int fontSize, Vector2 position)
        {
            var textObj = new GameObject("Text");
            textObj.transform.SetParent(parent, false);

            var rectTransform = textObj.AddComponent<RectTransform>();
            rectTransform.anchoredPosition = position;
            rectTransform.sizeDelta = new Vector2(400, 50);

            var textComponent = textObj.AddComponent<Text>();
            textComponent.text = text;
            textComponent.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            textComponent.fontSize = fontSize;
            textComponent.color = Color.white;
            textComponent.alignment = TextAnchor.MiddleCenter;

            return textObj;
        }

        private GameObject CreateButton(Transform parent, string text, Vector2 position, System.Action onClick)
        {
            var buttonObj = new GameObject("Button");
            buttonObj.transform.SetParent(parent, false);

            var rectTransform = buttonObj.AddComponent<RectTransform>();
            rectTransform.anchoredPosition = position;
            rectTransform.sizeDelta = new Vector2(200, 50);

            var image = buttonObj.AddComponent<Image>();
            image.color = new Color(0.2f, 0.3f, 0.8f, 1f); // 蓝色按钮

            var button = buttonObj.AddComponent<Button>();
            button.targetGraphic = image;

            // 添加点击事件
            button.onClick.AddListener(() => {
                Debug.Log($"按钮 '{text}' 被点击！");
                onClick?.Invoke();
            });

            // 创建按钮文本
            var textObj = new GameObject("Text");
            textObj.transform.SetParent(buttonObj.transform, false);

            var textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;

            var textComponent = textObj.AddComponent<Text>();
            textComponent.text = text;
            textComponent.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            textComponent.fontSize = 18;
            textComponent.color = Color.white;
            textComponent.alignment = TextAnchor.MiddleCenter;

            return buttonObj;
        }

        // 简单的OnGUI作为备用方案
        private void OnGUI()
        {
            // 如果游戏已开始，显示游戏信息
            if (gameStarted && !showMenu)
            {
                DisplayGameInfo();
                return;
            }

            if (!useSimpleGUI || !showMenu) return;

            // 设置GUI样式
            GUI.skin.button.fontSize = 20;
            GUI.skin.label.fontSize = 24;
            GUI.skin.label.alignment = TextAnchor.MiddleCenter;

            // 计算屏幕中心位置
            float centerX = Screen.width / 2f;
            float centerY = Screen.height / 2f;
            float buttonWidth = 200f;
            float buttonHeight = 50f;

            // 背景框
            GUI.Box(new Rect(centerX - 150, centerY - 150, 300, 300), "");

            // 标题
            GUI.Label(new Rect(centerX - 150, centerY - 120, 300, 40), "=== Roguelike Game ===");

            // 状态信息
            GUI.Label(new Rect(centerX - 150, centerY - 80, 300, 30), $"状态: {(gameStarted ? "已开始" : "未开始")}");

            // 检测按钮悬停
            Vector2 mousePos = Input.mousePosition;
            mousePos.y = Screen.height - mousePos.y; // GUI坐标系Y轴翻转

            Rect button1Rect = new Rect(centerX - buttonWidth/2, centerY - 30, buttonWidth, buttonHeight);
            Rect button2Rect = new Rect(centerX - buttonWidth/2, centerY + 30, buttonWidth, buttonHeight);
            Rect button3Rect = new Rect(centerX - buttonWidth/2, centerY + 90, buttonWidth, buttonHeight);

            buttonHover = -1;
            if (button1Rect.Contains(mousePos)) buttonHover = 0;
            else if (button2Rect.Contains(mousePos)) buttonHover = 1;
            else if (button3Rect.Contains(mousePos)) buttonHover = 2;

            // 开始游戏按钮 - 带颜色变化
            Color originalColor = GUI.backgroundColor;
            GUI.backgroundColor = (buttonHover == 0) ? Color.yellow : Color.white;
            if (GUI.Button(button1Rect, "开始游戏"))
            {
                Debug.Log("简单GUI - 点击了开始游戏按钮！");
                StartGame();
            }

            // 测试模式按钮 - 带颜色变化
            GUI.backgroundColor = (buttonHover == 1) ? Color.yellow : Color.white;
            if (GUI.Button(button2Rect, "测试模式"))
            {
                Debug.Log("简单GUI - 点击了测试模式按钮！");
                enableTestMode = true;
                CreateGameManagers();
                showMenu = false;
            }

            // 退出游戏按钮 - 带颜色变化
            GUI.backgroundColor = (buttonHover == 2) ? Color.yellow : Color.white;
            if (GUI.Button(button3Rect, "退出游戏"))
            {
                Debug.Log("简单GUI - 点击了退出游戏按钮！");
                Application.Quit();
            }

            // 恢复原始颜色
            GUI.backgroundColor = originalColor;

            // 调试信息
            GUI.Label(new Rect(10, 10, 300, 20), $"Screen: {Screen.width}x{Screen.height}");
            GUI.Label(new Rect(10, 30, 300, 20), $"Mouse: {Input.mousePosition}");
            GUI.Label(new Rect(10, 50, 300, 20), $"GUI Mouse: {mousePos}");
            GUI.Label(new Rect(10, 70, 300, 20), $"Hover: {buttonHover}");
            GUI.Label(new Rect(10, 90, 300, 20), $"Pressed: {mousePressed}, Released: {mouseReleased}");

            // 重置鼠标状态
            if (mousePressed || mouseReleased)
            {
                mousePressed = false;
                mouseReleased = false;
            }

            // 键盘提示
            GUI.Label(new Rect(10, Screen.height - 80, 400, 20), "键盘快捷键:");
            GUI.Label(new Rect(10, Screen.height - 60, 400, 20), "空格键 = 开始游戏");
            GUI.Label(new Rect(10, Screen.height - 40, 400, 20), "T键 = 测试模式");
            GUI.Label(new Rect(10, Screen.height - 20, 400, 20), "ESC键 = 退出游戏");
        }

        private void CreateGridBackground()
        {
            Debug.Log("创建网格背景...");

            // 创建网格线
            for (int x = -10; x <= 10; x++)
            {
                CreateGridLine(new Vector3(x, -10, 0), new Vector3(x, 10, 0), Color.gray);
            }

            for (int y = -10; y <= 10; y++)
            {
                CreateGridLine(new Vector3(-10, y, 0), new Vector3(10, y, 0), Color.gray);
            }

            // 创建坐标轴
            CreateGridLine(new Vector3(0, -10, 0), new Vector3(0, 10, 0), Color.red); // Y轴
            CreateGridLine(new Vector3(-10, 0, 0), new Vector3(10, 0, 0), Color.green); // X轴

            Debug.Log("网格背景创建完成");
        }

        private void CreateGridLine(Vector3 start, Vector3 end, Color color)
        {
            GameObject lineObj = new GameObject("GridLine");
            LineRenderer lr = lineObj.AddComponent<LineRenderer>();

            // 创建材质并设置颜色
            Material lineMaterial = new Material(Shader.Find("Sprites/Default"));
            lineMaterial.color = color;
            lr.material = lineMaterial;

            lr.startWidth = 0.05f;
            lr.endWidth = 0.05f;
            lr.positionCount = 2;
            lr.useWorldSpace = true;
            lr.sortingOrder = -1; // 确保在背景

            lr.SetPosition(0, start);
            lr.SetPosition(1, end);
        }

        private void DisplayGameInfo()
        {
            // 显示游戏中的信息
            GUI.Label(new Rect(10, 10, 300, 20), "=== 游戏中 ===");

            // 显示玩家信息
            var player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                Vector3 playerPos = player.transform.position;
                GUI.Label(new Rect(10, 30, 400, 20), $"玩家位置: ({playerPos.x:F3}, {playerPos.y:F3}, {playerPos.z:F3})");

                // 显示输入状态
                bool w = Input.GetKey(KeyCode.W);
                bool a = Input.GetKey(KeyCode.A);
                bool s = Input.GetKey(KeyCode.S);
                bool d = Input.GetKey(KeyCode.D);

                GUI.Label(new Rect(10, 50, 300, 20), $"输入: W={w} A={a} S={s} D={d}");

                // 显示玩家状态
                GUI.Label(new Rect(10, 70, 300, 20), $"移动速度: {player.MoveSpeed:F1}");
                GUI.Label(new Rect(10, 90, 300, 20), $"能移动: {player.CanMove}, 存活: {player.IsAlive}");

                // 显示速度
                var rb = player.GetComponent<Rigidbody2D>();
                if (rb != null)
                {
                    GUI.Label(new Rect(10, 110, 300, 20), $"物理速度: ({rb.velocity.x:F1}, {rb.velocity.y:F1})");
                }

                // 显示InputManager状态
                if (InputManager.Instance != null)
                {
                    Vector2 inputDir = InputManager.Instance.GetMovementDirection();
                    GUI.Label(new Rect(10, 130, 300, 20), $"输入方向: ({inputDir.x:F1}, {inputDir.y:F1})");
                }
            }
            else
            {
                GUI.Label(new Rect(10, 30, 300, 20), "找不到玩家！");
            }

            // 显示敌人信息
            var enemies = FindObjectsOfType<SimpleEnemyMover>();
            GUI.Label(new Rect(10, 150, 300, 20), $"敌人数量: {enemies.Length}");

            for (int i = 0; i < enemies.Length && i < 3; i++)
            {
                Vector3 enemyPos = enemies[i].transform.position;
                GUI.Label(new Rect(10, 170 + i * 20, 300, 20), $"敌人{i+1}: ({enemyPos.x:F1}, {enemyPos.y:F1})");
            }

            // 控制提示
            GUI.Label(new Rect(10, Screen.height - 80, 300, 20), "控制:");
            GUI.Label(new Rect(10, Screen.height - 60, 300, 20), "WASD - 移动");
            GUI.Label(new Rect(10, Screen.height - 40, 300, 20), "空格 - 技能");
            GUI.Label(new Rect(10, Screen.height - 20, 300, 20), "ESC - 返回菜单");

            // ESC返回菜单
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                RestartGame();
            }
        }

        public void RestartGame()
        {
            gameStarted = false;
            showMenu = true;

            // 清理现有游戏对象
            var players = FindObjectsOfType<PlayerController>();
            foreach (var player in players)
            {
                DestroyImmediate(player.gameObject);
            }

            // 清理敌人（简单的方法，通过名称）
            var enemies = GameObject.FindGameObjectsWithTag("Untagged");
            foreach (var enemy in enemies)
            {
                if (enemy.name.Contains("Enemy"))
                {
                    DestroyImmediate(enemy);
                }
            }

            Debug.Log("游戏重置完成");
        }
    }
}
