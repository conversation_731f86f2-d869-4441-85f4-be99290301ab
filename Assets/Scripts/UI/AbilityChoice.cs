using UnityEngine;
using UnityEngine.UI;
using TMPro;
using RoguelikeGame.Abilities;
using RoguelikeGame.Core;

namespace RoguelikeGame.UI
{
    /// <summary>
    /// 能力选择UI组件
    /// </summary>
    public class AbilityChoice : MonoBehaviour
    {
        [Header("UI组件")]
        public Image abilityIcon;
        public Image backgroundImage;
        public Image rarityFrame;
        public TextMeshProUGUI abilityNameText;
        public TextMeshProUGUI abilityDescriptionText;
        public TextMeshProUGUI rarityText;
        public Button choiceButton;
        
        [Header("颜色设置")]
        public Color commonColor = Color.white;
        public Color rareColor = Color.blue;
        public Color epicColor = Color.magenta;
        public Color legendaryColor = Color.yellow;
        
        [Header("动画设置")]
        public bool enableHoverEffect = true;
        public float hoverScale = 1.05f;
        public float animationDuration = 0.2f;
        
        // 数据
        private AbilityData abilityData;
        private int choiceIndex;
        
        // 事件
        public System.Action<int> OnAbilityChosen;
        
        private void Start()
        {
            SetupButton();
        }
        
        private void SetupButton()
        {
            if (choiceButton == null)
            {
                choiceButton = GetComponent<Button>();
                if (choiceButton == null)
                {
                    choiceButton = gameObject.AddComponent<Button>();
                }
            }
            
            choiceButton.onClick.AddListener(OnChoiceClicked);
            
            // 添加悬停效果
            if (enableHoverEffect)
            {
                var eventTrigger = gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>();
                if (eventTrigger == null)
                {
                    eventTrigger = gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();
                }
                
                // 鼠标进入
                var pointerEnter = new UnityEngine.EventSystems.EventTrigger.Entry();
                pointerEnter.eventID = UnityEngine.EventSystems.EventTriggerType.PointerEnter;
                pointerEnter.callback.AddListener((data) => OnPointerEnter());
                eventTrigger.triggers.Add(pointerEnter);
                
                // 鼠标离开
                var pointerExit = new UnityEngine.EventSystems.EventTrigger.Entry();
                pointerExit.eventID = UnityEngine.EventSystems.EventTriggerType.PointerExit;
                pointerExit.callback.AddListener((data) => OnPointerExit());
                eventTrigger.triggers.Add(pointerExit);
            }
        }
        
        public void Initialize(AbilityData ability, int index)
        {
            abilityData = ability;
            choiceIndex = index;
            UpdateDisplay();
        }
        
        private void UpdateDisplay()
        {
            if (abilityData == null) return;
            
            // 设置能力图标
            if (abilityIcon != null && abilityData.abilityIcon != null)
            {
                abilityIcon.sprite = abilityData.abilityIcon;
            }
            
            // 设置能力名称
            if (abilityNameText != null)
            {
                abilityNameText.text = abilityData.abilityName;
            }
            
            // 设置能力描述
            if (abilityDescriptionText != null)
            {
                // 检查是否已拥有该能力
                int currentLevel = 0;
                if (AbilitySystem.Instance != null)
                {
                    currentLevel = AbilitySystem.Instance.GetAbilityLevel(abilityData.abilityId);
                }
                
                if (currentLevel > 0)
                {
                    // 显示升级后的描述
                    abilityDescriptionText.text = $"[升级到 Lv.{currentLevel + 1}]\n{abilityData.GetDescriptionAtLevel(currentLevel + 1)}";
                }
                else
                {
                    // 显示基础描述
                    abilityDescriptionText.text = abilityData.description;
                }
            }
            
            // 设置稀有度
            if (rarityText != null)
            {
                rarityText.text = GetRarityDisplayName(abilityData.rarity);
                rarityText.color = GetRarityColor(abilityData.rarity);
            }
            
            // 设置稀有度边框
            if (rarityFrame != null)
            {
                rarityFrame.color = GetRarityColor(abilityData.rarity);
            }
            
            // 设置背景颜色
            if (backgroundImage != null)
            {
                Color bgColor = GetRarityColor(abilityData.rarity);
                bgColor.a = 0.1f; // 半透明背景
                backgroundImage.color = bgColor;
            }
        }
        
        private Color GetRarityColor(AbilityRarity rarity)
        {
            switch (rarity)
            {
                case AbilityRarity.Common:
                    return commonColor;
                case AbilityRarity.Rare:
                    return rareColor;
                case AbilityRarity.Epic:
                    return epicColor;
                case AbilityRarity.Legendary:
                    return legendaryColor;
                default:
                    return commonColor;
            }
        }
        
        private string GetRarityDisplayName(AbilityRarity rarity)
        {
            switch (rarity)
            {
                case AbilityRarity.Common:
                    return "普通";
                case AbilityRarity.Rare:
                    return "稀有";
                case AbilityRarity.Epic:
                    return "史诗";
                case AbilityRarity.Legendary:
                    return "传说";
                default:
                    return "未知";
            }
        }
        
        private void OnChoiceClicked()
        {
            if (abilityData != null)
            {
                // 播放选择音效
                if (abilityData.selectSound != null)
                {
                    AudioManager.Instance?.PlaySFX(abilityData.selectSound);
                }
                else
                {
                    AudioManager.Instance?.PlayAbilitySelect();
                }
                
                // 触发选择事件
                OnAbilityChosen?.Invoke(choiceIndex);
            }
        }
        
        private void OnPointerEnter()
        {
            StartCoroutine(ScaleAnimation(hoverScale));
            
            // 播放悬停音效
            AudioManager.Instance?.PlaySFX(null); // TODO: 添加悬停音效
        }
        
        private void OnPointerExit()
        {
            StartCoroutine(ScaleAnimation(1f));
        }
        
        private System.Collections.IEnumerator ScaleAnimation(float targetScale)
        {
            Vector3 startScale = transform.localScale;
            Vector3 endScale = Vector3.one * targetScale;
            float elapsedTime = 0f;
            
            while (elapsedTime < animationDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float t = elapsedTime / animationDuration;
                transform.localScale = Vector3.Lerp(startScale, endScale, t);
                yield return null;
            }
            
            transform.localScale = endScale;
        }
    }
}
