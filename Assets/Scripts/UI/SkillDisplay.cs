using UnityEngine;
using UnityEngine.UI;
using TMPro;
using RoguelikeGame.Characters;

namespace RoguelikeGame.UI
{
    /// <summary>
    /// 技能显示UI组件
    /// </summary>
    public class SkillDisplay : MonoBehaviour
    {
        [Header("UI组件")]
        public Image skillIcon;
        public Image backgroundImage;
        public TextMeshProUGUI skillNameText;
        public TextMeshProUGUI skillTypeText;
        public TextMeshProUGUI skillDescriptionText;
        public TextMeshProUGUI cooldownText;
        
        [Header("颜色设置")]
        public Color passiveColor = Color.green;
        public Color activeColor = Color.blue;
        public Color triggerColor = Color.yellow;
        public Color defaultColor = Color.white;
        
        // 数据
        private CharacterSkill skillData;
        private string skillTypeString;
        
        /// <summary>
        /// 初始化技能显示
        /// </summary>
        /// <param name="skill">技能数据</param>
        /// <param name="skillType">技能类型字符串</param>
        public void Initialize(CharacterSkill skill, string skillType)
        {
            skillData = skill;
            skillTypeString = skillType;
            UpdateDisplay();
        }
        
        private void UpdateDisplay()
        {
            if (skillData == null) return;
            
            // 设置技能图标
            if (skillIcon != null && skillData.skillIcon != null)
            {
                skillIcon.sprite = skillData.skillIcon;
            }
            
            // 设置技能名称
            if (skillNameText != null)
            {
                skillNameText.text = skillData.skillName;
            }
            
            // 设置技能类型
            if (skillTypeText != null)
            {
                skillTypeText.text = skillTypeString;
                skillTypeText.color = GetSkillTypeColor(skillData.skillType);
            }
            
            // 设置技能描述
            if (skillDescriptionText != null)
            {
                skillDescriptionText.text = skillData.description;
            }
            
            // 设置冷却时间（仅对主动技能显示）
            if (cooldownText != null)
            {
                if (skillData.skillType == SkillType.Active && skillData.cooldown > 0)
                {
                    cooldownText.text = $"冷却: {skillData.cooldown}秒";
                    cooldownText.gameObject.SetActive(true);
                }
                else
                {
                    cooldownText.gameObject.SetActive(false);
                }
            }
            
            // 设置背景颜色
            if (backgroundImage != null)
            {
                Color bgColor = GetSkillTypeColor(skillData.skillType);
                bgColor.a = 0.1f; // 半透明背景
                backgroundImage.color = bgColor;
            }
        }
        
        private Color GetSkillTypeColor(SkillType skillType)
        {
            switch (skillType)
            {
                case SkillType.Passive:
                    return passiveColor;
                case SkillType.Active:
                    return activeColor;
                case SkillType.Trigger:
                    return triggerColor;
                default:
                    return defaultColor;
            }
        }
        
        /// <summary>
        /// 获取技能数据
        /// </summary>
        public CharacterSkill GetSkillData()
        {
            return skillData;
        }
        
        /// <summary>
        /// 获取技能类型字符串
        /// </summary>
        public string GetSkillTypeString()
        {
            return skillTypeString;
        }
    }
}
