using UnityEngine;
using UnityEngine.UI;

namespace RoguelikeGame.UI
{
    /// <summary>
    /// 血条UI组件，显示角色的血量
    /// </summary>
    public class HealthBarUI : MonoBehaviour
    {
        [Header("血条设置")]
        public Image healthBarFill;
        public Image healthBarBackground;
        public Text healthText;
        
        [Header("颜色设置")]
        public Color fullHealthColor = Color.green;
        public Color halfHealthColor = Color.yellow;
        public Color lowHealthColor = Color.red;
        public Color backgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        
        [Header("动画设置")]
        public bool smoothTransition = true;
        public float transitionSpeed = 5f;
        
        private float currentHealth;
        private float maxHealth;
        private float targetFillAmount;
        private float currentFillAmount;
        
        private void Start()
        {
            InitializeHealthBar();
        }
        
        private void Update()
        {
            if (smoothTransition && Mathf.Abs(currentFillAmount - targetFillAmount) > 0.01f)
            {
                currentFillAmount = Mathf.Lerp(currentFillAmount, targetFillAmount, transitionSpeed * Time.deltaTime);
                UpdateHealthBarVisual();
            }
        }
        
        private void InitializeHealthBar()
        {
            if (healthBarBackground != null)
            {
                healthBarBackground.color = backgroundColor;
            }
            
            if (healthBarFill != null)
            {
                healthBarFill.fillMethod = Image.FillMethod.Horizontal;
                healthBarFill.type = Image.Type.Filled;
            }
        }
        
        public void SetHealth(float current, float max)
        {
            currentHealth = current;
            maxHealth = max;
            targetFillAmount = maxHealth > 0 ? currentHealth / maxHealth : 0f;
            
            if (!smoothTransition)
            {
                currentFillAmount = targetFillAmount;
                UpdateHealthBarVisual();
            }
            
            UpdateHealthText();
        }
        
        private void UpdateHealthBarVisual()
        {
            if (healthBarFill != null)
            {
                healthBarFill.fillAmount = currentFillAmount;
                
                // 根据血量百分比改变颜色
                Color targetColor = GetHealthColor(currentFillAmount);
                healthBarFill.color = targetColor;
            }
        }
        
        private Color GetHealthColor(float healthPercentage)
        {
            if (healthPercentage > 0.6f)
            {
                // 绿色到黄色
                return Color.Lerp(halfHealthColor, fullHealthColor, (healthPercentage - 0.6f) / 0.4f);
            }
            else if (healthPercentage > 0.3f)
            {
                // 黄色到红色
                return Color.Lerp(lowHealthColor, halfHealthColor, (healthPercentage - 0.3f) / 0.3f);
            }
            else
            {
                // 红色
                return lowHealthColor;
            }
        }
        
        private void UpdateHealthText()
        {
            if (healthText != null)
            {
                healthText.text = $"{currentHealth:F0}/{maxHealth:F0}";
            }
        }
        
        public void SetVisible(bool visible)
        {
            gameObject.SetActive(visible);
        }
        
        public void Flash(Color flashColor, float duration = 0.2f)
        {
            StartCoroutine(FlashCoroutine(flashColor, duration));
        }
        
        private System.Collections.IEnumerator FlashCoroutine(Color flashColor, float duration)
        {
            Color originalColor = healthBarFill.color;
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                float t = elapsed / duration;
                healthBarFill.color = Color.Lerp(flashColor, originalColor, t);
                elapsed += Time.deltaTime;
                yield return null;
            }
            
            healthBarFill.color = originalColor;
        }
    }
}
