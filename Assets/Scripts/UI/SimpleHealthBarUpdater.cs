using UnityEngine;
using UnityEngine.UI;
using RoguelikeGame.Characters;

namespace RoguelikeGame.UI
{
    /// <summary>
    /// 简单的血条更新器，自动找到玩家并更新血条显示
    /// </summary>
    public class SimpleHealthBarUpdater : MonoBehaviour
    {
        [Header("UI组件")]
        public Image fillImage;
        public Text healthText;
        
        [Header("颜色设置")]
        public Color fullHealthColor = Color.green;
        public Color halfHealthColor = Color.yellow;
        public Color lowHealthColor = Color.red;
        
        [Header("动画设置")]
        public bool smoothTransition = true;
        public float transitionSpeed = 5f;
        
        private PlayerController player;
        private float currentFillAmount = 1f;
        private float targetFillAmount = 1f;
        
        private void Start()
        {
            FindPlayer();
        }
        
        private void Update()
        {
            // 如果没有玩家，尝试找到玩家
            if (player == null)
            {
                FindPlayer();
                return;
            }
            
            // 更新血条
            UpdateHealthBar();
            
            // 平滑过渡
            if (smoothTransition && Mathf.Abs(currentFillAmount - targetFillAmount) > 0.01f)
            {
                currentFillAmount = Mathf.Lerp(currentFillAmount, targetFillAmount, transitionSpeed * Time.deltaTime);
                UpdateVisuals();
            }
        }
        
        private void FindPlayer()
        {
            var foundPlayer = FindObjectOfType<PlayerController>();
            if (foundPlayer != null && foundPlayer != player)
            {
                // 取消之前玩家的事件订阅
                if (player != null)
                {
                    player.OnHealthChanged -= OnPlayerHealthChanged;
                }
                
                player = foundPlayer;
                
                // 订阅新玩家的事件
                player.OnHealthChanged += OnPlayerHealthChanged;
                
                Debug.Log("SimpleHealthBarUpdater: 找到并连接到玩家");
                
                // 立即更新血条
                UpdateHealthBar();
            }
        }
        
        private void UpdateHealthBar()
        {
            if (player == null) return;
            
            float healthPercentage = player.MaxHealth > 0 ? player.CurrentHealth / player.MaxHealth : 0f;
            targetFillAmount = healthPercentage;
            
            if (!smoothTransition)
            {
                currentFillAmount = targetFillAmount;
                UpdateVisuals();
            }
            
            UpdateHealthText();
        }
        
        private void UpdateVisuals()
        {
            if (fillImage != null)
            {
                fillImage.fillAmount = currentFillAmount;
                fillImage.color = GetHealthColor(currentFillAmount);
            }
        }
        
        private void UpdateHealthText()
        {
            if (healthText != null && player != null)
            {
                healthText.text = $"{player.CurrentHealth:F0}/{player.MaxHealth:F0}";
            }
        }
        
        private Color GetHealthColor(float healthPercentage)
        {
            if (healthPercentage > 0.6f)
            {
                // 绿色到黄色
                return Color.Lerp(halfHealthColor, fullHealthColor, (healthPercentage - 0.6f) / 0.4f);
            }
            else if (healthPercentage > 0.3f)
            {
                // 黄色到红色
                return Color.Lerp(lowHealthColor, halfHealthColor, (healthPercentage - 0.3f) / 0.3f);
            }
            else
            {
                // 红色
                return lowHealthColor;
            }
        }
        
        private void OnPlayerHealthChanged(float currentHealth, float maxHealth)
        {
            Debug.Log($"血条更新: {currentHealth}/{maxHealth}");
            
            // 血量低时闪烁效果
            if (currentHealth / maxHealth < 0.3f)
            {
                StartCoroutine(FlashEffect());
            }
        }
        
        private System.Collections.IEnumerator FlashEffect()
        {
            if (fillImage == null) yield break;
            
            Color originalColor = fillImage.color;
            float flashDuration = 0.2f;
            float elapsed = 0f;
            
            while (elapsed < flashDuration)
            {
                float t = elapsed / flashDuration;
                fillImage.color = Color.Lerp(Color.red, originalColor, t);
                elapsed += Time.deltaTime;
                yield return null;
            }
            
            fillImage.color = originalColor;
        }
        
        private void OnDestroy()
        {
            // 取消事件订阅
            if (player != null)
            {
                player.OnHealthChanged -= OnPlayerHealthChanged;
            }
        }
    }
}
