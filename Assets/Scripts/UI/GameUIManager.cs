using UnityEngine;
using UnityEngine.UI;
using RoguelikeGame.Characters;

namespace RoguelikeGame.UI
{
    /// <summary>
    /// 游戏UI管理器，管理游戏中的所有UI元素
    /// </summary>
    public class GameUIManager : MonoBehaviour
    {
        [Header("UI组件")]
        public HealthBarUI playerHealthBar;
        public Text playerLevelText;
        public Text playerExpText;
        public Text gameStatusText;
        
        [Header("UI设置")]
        public bool autoFindPlayer = true;
        public bool showDebugInfo = true;
        
        // 单例模式
        public static GameUIManager Instance { get; private set; }
        
        // 玩家引用
        private PlayerController player;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }
        }
        
        private void Start()
        {
            InitializeUI();
            
            if (autoFindPlayer)
            {
                FindPlayer();
            }
        }
        
        private void Update()
        {
            if (player == null && autoFindPlayer)
            {
                FindPlayer();
            }
            
            UpdateUI();
        }
        
        private void InitializeUI()
        {
            // 初始化UI组件
            if (playerHealthBar != null)
            {
                playerHealthBar.SetVisible(false);
            }
            
            if (gameStatusText != null)
            {
                gameStatusText.text = "等待游戏开始...";
            }
        }
        
        private void FindPlayer()
        {
            var foundPlayer = FindObjectOfType<PlayerController>();
            if (foundPlayer != null && foundPlayer != player)
            {
                SetPlayer(foundPlayer);
            }
        }
        
        public void SetPlayer(PlayerController newPlayer)
        {
            // 取消之前玩家的事件订阅
            if (player != null)
            {
                player.OnHealthChanged -= OnPlayerHealthChanged;
                player.OnPlayerDeath -= OnPlayerDeath;
            }
            
            player = newPlayer;
            
            if (player != null)
            {
                // 订阅新玩家的事件
                player.OnHealthChanged += OnPlayerHealthChanged;
                player.OnPlayerDeath += OnPlayerDeath;
                
                // 显示血条
                if (playerHealthBar != null)
                {
                    playerHealthBar.SetVisible(true);
                    playerHealthBar.SetHealth(player.CurrentHealth, player.MaxHealth);
                }
                
                if (gameStatusText != null)
                {
                    gameStatusText.text = "游戏进行中";
                }
                
                Debug.Log("GameUIManager: 玩家UI已连接");
            }
        }
        
        private void UpdateUI()
        {
            if (player == null) return;
            
            // 更新调试信息
            if (showDebugInfo && gameStatusText != null)
            {
                gameStatusText.text = $"血量: {player.CurrentHealth:F0}/{player.MaxHealth:F0}\n" +
                                    $"位置: ({player.transform.position.x:F1}, {player.transform.position.y:F1})\n" +
                                    $"存活: {player.IsAlive}";
            }
        }
        
        private void OnPlayerHealthChanged(float currentHealth, float maxHealth)
        {
            if (playerHealthBar != null)
            {
                playerHealthBar.SetHealth(currentHealth, maxHealth);
                
                // 血量低时闪烁
                if (currentHealth / maxHealth < 0.3f)
                {
                    playerHealthBar.Flash(Color.red, 0.1f);
                }
            }
            
            Debug.Log($"玩家血量变化: {currentHealth}/{maxHealth}");
        }
        
        private void OnPlayerDeath()
        {
            if (gameStatusText != null)
            {
                gameStatusText.text = "玩家死亡！";
            }
            
            if (playerHealthBar != null)
            {
                playerHealthBar.Flash(Color.red, 1f);
            }
            
            Debug.Log("玩家死亡，UI已更新");
        }
        
        public void ShowDamageText(Vector3 worldPosition, float damage, Color color)
        {
            // TODO: 实现伤害数字显示
            Debug.Log($"伤害: {damage} 在位置 {worldPosition}");
        }
        
        public void UpdateGameStatus(string status)
        {
            if (gameStatusText != null)
            {
                gameStatusText.text = status;
            }
        }
    }
}
