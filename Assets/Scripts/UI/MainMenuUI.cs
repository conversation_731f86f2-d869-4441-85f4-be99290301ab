using UnityEngine;
using UnityEngine.UI;
using TMPro;
using RoguelikeGame.Core;
using RoguelikeGame.Cards;
using RoguelikeGame.Characters;

namespace RoguelikeGame.UI
{
    /// <summary>
    /// 主菜单UI控制器
    /// </summary>
    public class MainMenuUI : MonoBehaviour
    {
        [Header("主菜单面板")]
        public GameObject mainMenuPanel;
        public Button startGameButton;
        public Button characterSelectButton;
        public Button cardDrawButton;
        public Button settingsButton;
        public Button quitButton;

        [Header("角色选择面板")]
        public GameObject characterSelectPanel;
        public Transform characterGrid;
        public GameObject characterCardPrefab;
        public Button confirmCharacterButton;
        public Button backFromCharacterButton;

        [Header("抽卡面板")]
        public GameObject cardDrawPanel;
        public Transform cardContainer;
        public GameObject cardDisplayPrefab;
        public Button drawSingleButton;
        public Button drawTenButton;
        public Button backFromCardButton;
        public TextMeshProUGUI cardTicketsText;
        public TextMeshProUGUI premiumTicketsText;

        [Header("设置面板")]
        public GameObject settingsPanel;
        public Slider masterVolumeSlider;
        public Slider musicVolumeSlider;
        public Slider sfxVolumeSlider;
        public Button backFromSettingsButton;

        [Header("角色信息显示")]
        public GameObject characterInfoPanel;
        public Image characterPortrait;
        public TextMeshProUGUI characterNameText;
        public TextMeshProUGUI characterDescriptionText;
        public Transform skillContainer;
        public GameObject skillDisplayPrefab;

        // 当前选中的角色
        private CharacterData selectedCharacter;
        private GameObject selectedCharacterCard;

        private void Start()
        {
            InitializeUI();
            SetupEventListeners();
            ShowMainMenu();
        }

        private void InitializeUI()
        {
            // 初始时隐藏所有子面板
            if (characterSelectPanel) characterSelectPanel.SetActive(false);
            if (cardDrawPanel) cardDrawPanel.SetActive(false);
            if (settingsPanel) settingsPanel.SetActive(false);
            if (characterInfoPanel) characterInfoPanel.SetActive(false);

            // 显示主菜单
            if (mainMenuPanel) mainMenuPanel.SetActive(true);

            // 初始化音量滑块
            InitializeVolumeSliders();

            // 更新抽卡券显示
            UpdateCardTicketsDisplay();
        }

        private void SetupEventListeners()
        {
            // 主菜单按钮
            if (startGameButton)
                startGameButton.onClick.AddListener(OnStartGameClicked);
            if (characterSelectButton)
                characterSelectButton.onClick.AddListener(OnCharacterSelectClicked);
            if (cardDrawButton)
                cardDrawButton.onClick.AddListener(OnCardDrawClicked);
            if (settingsButton)
                settingsButton.onClick.AddListener(OnSettingsClicked);
            if (quitButton)
                quitButton.onClick.AddListener(OnQuitClicked);

            // 角色选择按钮
            if (confirmCharacterButton)
                confirmCharacterButton.onClick.AddListener(OnConfirmCharacterClicked);
            if (backFromCharacterButton)
                backFromCharacterButton.onClick.AddListener(OnBackFromCharacterClicked);

            // 抽卡按钮
            if (drawSingleButton)
                drawSingleButton.onClick.AddListener(OnDrawSingleClicked);
            if (drawTenButton)
                drawTenButton.onClick.AddListener(OnDrawTenClicked);
            if (backFromCardButton)
                backFromCardButton.onClick.AddListener(OnBackFromCardClicked);

            // 设置按钮
            if (backFromSettingsButton)
                backFromSettingsButton.onClick.AddListener(OnBackFromSettingsClicked);

            // 音量滑块
            if (masterVolumeSlider)
                masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);
            if (musicVolumeSlider)
                musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
            if (sfxVolumeSlider)
                sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);

            // 抽卡系统事件
            if (CardSystem.Instance != null)
            {
                CardSystem.Instance.OnCharacterDrawn += OnCharacterDrawn;
                CardSystem.Instance.OnCardTicketsChanged += OnCardTicketsChanged;
                CardSystem.Instance.OnPremiumTicketsChanged += OnPremiumTicketsChanged;
            }
        }

        private void OnDestroy()
        {
            // 清理事件监听
            if (CardSystem.Instance != null)
            {
                CardSystem.Instance.OnCharacterDrawn -= OnCharacterDrawn;
                CardSystem.Instance.OnCardTicketsChanged -= OnCardTicketsChanged;
                CardSystem.Instance.OnPremiumTicketsChanged -= OnPremiumTicketsChanged;
            }
        }

        #region 面板切换

        private void ShowMainMenu()
        {
            HideAllPanels();
            if (mainMenuPanel) mainMenuPanel.SetActive(true);
        }

        private void ShowCharacterSelect()
        {
            HideAllPanels();
            if (characterSelectPanel) characterSelectPanel.SetActive(true);
            PopulateCharacterGrid();
        }

        private void ShowCardDraw()
        {
            HideAllPanels();
            if (cardDrawPanel) cardDrawPanel.SetActive(true);
            UpdateCardTicketsDisplay();
            DisplayOwnedCharacters();
        }

        private void ShowSettings()
        {
            HideAllPanels();
            if (settingsPanel) settingsPanel.SetActive(true);
        }

        private void HideAllPanels()
        {
            if (mainMenuPanel) mainMenuPanel.SetActive(false);
            if (characterSelectPanel) characterSelectPanel.SetActive(false);
            if (cardDrawPanel) cardDrawPanel.SetActive(false);
            if (settingsPanel) settingsPanel.SetActive(false);
            if (characterInfoPanel) characterInfoPanel.SetActive(false);
        }

        #endregion

        #region 角色选择

        private void PopulateCharacterGrid()
        {
            if (characterGrid == null || CardSystem.Instance == null) return;

            // 清空现有的角色卡片
            foreach (Transform child in characterGrid)
            {
                Destroy(child.gameObject);
            }

            // 获取拥有的角色
            var ownedCharacters = CardSystem.Instance.GetOwnedCharacters();

            foreach (var character in ownedCharacters)
            {
                CreateCharacterCard(character);
            }

            // 如果没有角色，显示提示
            if (ownedCharacters.Count == 0)
            {
                CreateNoCharacterPrompt();
            }
        }

        private void CreateCharacterCard(CharacterData character)
        {
            if (characterCardPrefab == null) return;

            GameObject cardObj = Instantiate(characterCardPrefab, characterGrid);
            CharacterCard cardComponent = cardObj.GetComponent<CharacterCard>();

            if (cardComponent != null)
            {
                cardComponent.Initialize(character);
                cardComponent.OnCardSelected += OnCharacterCardSelected;
            }
        }

        private void CreateNoCharacterPrompt()
        {
            GameObject promptObj = new GameObject("NoCharacterPrompt");
            promptObj.transform.SetParent(characterGrid);

            TextMeshProUGUI promptText = promptObj.AddComponent<TextMeshProUGUI>();
            promptText.text = "没有角色！请先抽卡获得角色。";
            promptText.fontSize = 24;
            promptText.alignment = TextAlignmentOptions.Center;

            RectTransform rectTransform = promptObj.GetComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(400, 100);
        }

        private void OnCharacterCardSelected(CharacterData character, GameObject cardObject)
        {
            // 取消之前选中的卡片
            if (selectedCharacterCard != null)
            {
                var prevCard = selectedCharacterCard.GetComponent<CharacterCard>();
                if (prevCard != null) prevCard.SetSelected(false);
            }

            // 选中新卡片
            selectedCharacter = character;
            selectedCharacterCard = cardObject;

            var cardComponent = cardObject.GetComponent<CharacterCard>();
            if (cardComponent != null) cardComponent.SetSelected(true);

            // 显示角色信息
            ShowCharacterInfo(character);

            // 启用确认按钮
            if (confirmCharacterButton)
                confirmCharacterButton.interactable = true;
        }

        private void ShowCharacterInfo(CharacterData character)
        {
            if (characterInfoPanel == null) return;

            characterInfoPanel.SetActive(true);

            if (characterPortrait && character.characterPortrait)
                characterPortrait.sprite = character.characterPortrait;

            if (characterNameText)
                characterNameText.text = character.characterName;

            if (characterDescriptionText)
                characterDescriptionText.text = character.description;

            // 显示技能信息
            DisplayCharacterSkills(character);
        }

        private void DisplayCharacterSkills(CharacterData character)
        {
            if (skillContainer == null) return;

            // 清空现有技能显示
            foreach (Transform child in skillContainer)
            {
                Destroy(child.gameObject);
            }

            // 显示被动技能
            if (character.passiveSkill != null)
            {
                CreateSkillDisplay(character.passiveSkill, "被动技能");
            }

            // 显示主动技能
            if (character.activeSkill != null)
            {
                CreateSkillDisplay(character.activeSkill, "主动技能");
            }
        }

        private void CreateSkillDisplay(CharacterSkill skill, string skillType)
        {
            if (skillDisplayPrefab == null) return;

            GameObject skillObj = Instantiate(skillDisplayPrefab, skillContainer);

            // Try to get the SkillDisplay component and call Initialize
            var skillDisplayComponent = skillObj.GetComponent("SkillDisplay");
            if (skillDisplayComponent != null)
            {
                // Use reflection to call the Initialize method
                var initializeMethod = skillDisplayComponent.GetType().GetMethod("Initialize",
                    new System.Type[] { typeof(CharacterSkill), typeof(string) });
                if (initializeMethod != null)
                {
                    initializeMethod.Invoke(skillDisplayComponent, new object[] { skill, skillType });
                }
            }
        }

        #endregion

        #region 抽卡系统

        private void UpdateCardTicketsDisplay()
        {
            if (CardSystem.Instance == null) return;

            if (cardTicketsText)
                cardTicketsText.text = $"抽卡券: {CardSystem.Instance.GetCardTickets()}";

            if (premiumTicketsText)
                premiumTicketsText.text = $"高级券: {CardSystem.Instance.GetPremiumTickets()}";

            // 更新按钮状态
            if (drawSingleButton)
                drawSingleButton.interactable = CardSystem.Instance.CanDraw(false);

            if (drawTenButton)
                drawTenButton.interactable = CardSystem.Instance.GetCardTickets() >= 10;
        }

        private void DisplayOwnedCharacters()
        {
            if (cardContainer == null || CardSystem.Instance == null) return;

            // 清空现有显示
            foreach (Transform child in cardContainer)
            {
                Destroy(child.gameObject);
            }

            // 显示拥有的角色
            var ownedCharacters = CardSystem.Instance.GetOwnedCharacters();
            foreach (var character in ownedCharacters)
            {
                CreateCardDisplay(character);
            }
        }

        private void CreateCardDisplay(CharacterData character)
        {
            if (cardDisplayPrefab == null) return;

            GameObject cardObj = Instantiate(cardDisplayPrefab, cardContainer);

            // Try to get the CardDisplay component and call Initialize
            var cardDisplayComponent = cardObj.GetComponent("CardDisplay");
            if (cardDisplayComponent != null)
            {
                // Use reflection to call the Initialize method
                var initializeMethod = cardDisplayComponent.GetType().GetMethod("Initialize",
                    new System.Type[] { typeof(CharacterData) });
                if (initializeMethod != null)
                {
                    initializeMethod.Invoke(cardDisplayComponent, new object[] { character });
                }
            }
        }

        private void OnCharacterDrawn(CharacterData character)
        {
            // 播放抽卡动画和音效
            AudioManager.Instance?.PlayCardDraw();

            // 刷新显示
            if (cardDrawPanel.activeInHierarchy)
            {
                DisplayOwnedCharacters();
            }

            // 显示获得角色的提示
            ShowCharacterObtainedPopup(character);
        }

        private void ShowCharacterObtainedPopup(CharacterData character)
        {
            // TODO: 实现获得角色的弹窗动画
            Debug.Log($"获得角色: {character.characterName} ({character.rarity})");
        }

        #endregion

        #region 设置系统

        private void InitializeVolumeSliders()
        {
            if (AudioManager.Instance == null) return;

            if (masterVolumeSlider)
                masterVolumeSlider.value = AudioManager.Instance.masterVolume;

            if (musicVolumeSlider)
                musicVolumeSlider.value = AudioManager.Instance.musicVolume;

            if (sfxVolumeSlider)
                sfxVolumeSlider.value = AudioManager.Instance.sfxVolume;
        }

        #endregion

        #region 按钮事件

        private void OnStartGameClicked()
        {
            AudioManager.Instance?.PlayButtonClick();

            if (selectedCharacter != null)
            {
                // 设置选中的角色并开始游戏
                PlayerPrefs.SetString("SelectedCharacter", selectedCharacter.name);
                GameManager.Instance?.StartGame();
            }
            else
            {
                // 没有选中角色，打开角色选择
                ShowCharacterSelect();
            }
        }

        private void OnCharacterSelectClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            ShowCharacterSelect();
        }

        private void OnCardDrawClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            ShowCardDraw();
        }

        private void OnSettingsClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            ShowSettings();
        }

        private void OnQuitClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            GameManager.Instance?.QuitGame();
        }

        private void OnConfirmCharacterClicked()
        {
            AudioManager.Instance?.PlayButtonClick();

            if (selectedCharacter != null)
            {
                PlayerPrefs.SetString("SelectedCharacter", selectedCharacter.name);
                GameManager.Instance?.StartGame();
            }
        }

        private void OnBackFromCharacterClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            ShowMainMenu();
        }

        private void OnDrawSingleClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            CardSystem.Instance?.DrawCard(false);
            UpdateCardTicketsDisplay();
        }

        private void OnDrawTenClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            CardSystem.Instance?.DrawMultipleCards(10, false);
            UpdateCardTicketsDisplay();
        }

        private void OnBackFromCardClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            ShowMainMenu();
        }

        private void OnBackFromSettingsClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            ShowMainMenu();
        }

        private void OnMasterVolumeChanged(float value)
        {
            AudioManager.Instance?.SetMasterVolume(value);
        }

        private void OnMusicVolumeChanged(float value)
        {
            AudioManager.Instance?.SetMusicVolume(value);
        }

        private void OnSFXVolumeChanged(float value)
        {
            AudioManager.Instance?.SetSFXVolume(value);
        }

        private void OnCardTicketsChanged(int newCount)
        {
            UpdateCardTicketsDisplay();
        }

        private void OnPremiumTicketsChanged(int newCount)
        {
            UpdateCardTicketsDisplay();
        }

        #endregion
    }
}
