using UnityEngine;
using UnityEngine.UI;
using TMPro;
using RoguelikeGame.Characters;

namespace RoguelikeGame.UI
{
    /// <summary>
    /// 卡片显示UI组件，用于在抽卡界面显示拥有的角色卡片
    /// </summary>
    public class CardDisplay : MonoBehaviour
    {
        [Header("UI组件")]
        public Image characterIcon;
        public Image backgroundImage;
        public Image rarityFrame;
        public TextMeshProUGUI characterNameText;
        public TextMeshProUGUI rarityText;
        public Transform starContainer;
        public GameObject starPrefab;
        
        [Header("动画设置")]
        public bool enableHoverEffect = true;
        public float hoverScale = 1.05f;
        public float animationDuration = 0.2f;
        
        [Header("新卡片效果")]
        public GameObject newCardIndicator;
        public ParticleSystem sparkleEffect;
        
        // 数据
        private CharacterData characterData;
        private bool isNewCard = false;
        
        private void Start()
        {
            SetupHoverEffect();
        }
        
        private void SetupHoverEffect()
        {
            // 添加悬停效果
            if (enableHoverEffect)
            {
                var eventTrigger = gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>();
                if (eventTrigger == null)
                {
                    eventTrigger = gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();
                }
                
                // 鼠标进入
                var pointerEnter = new UnityEngine.EventSystems.EventTrigger.Entry();
                pointerEnter.eventID = UnityEngine.EventSystems.EventTriggerType.PointerEnter;
                pointerEnter.callback.AddListener((data) => OnPointerEnter());
                eventTrigger.triggers.Add(pointerEnter);
                
                // 鼠标离开
                var pointerExit = new UnityEngine.EventSystems.EventTrigger.Entry();
                pointerExit.eventID = UnityEngine.EventSystems.EventTriggerType.PointerExit;
                pointerExit.callback.AddListener((data) => OnPointerExit());
                eventTrigger.triggers.Add(pointerExit);
            }
        }
        
        /// <summary>
        /// 初始化卡片显示
        /// </summary>
        /// <param name="character">角色数据</param>
        /// <param name="isNew">是否为新获得的卡片</param>
        public void Initialize(CharacterData character, bool isNew = false)
        {
            characterData = character;
            isNewCard = isNew;
            UpdateDisplay();
        }
        
        /// <summary>
        /// 初始化卡片显示（重载方法，兼容现有代码）
        /// </summary>
        /// <param name="character">角色数据</param>
        public void Initialize(CharacterData character)
        {
            Initialize(character, false);
        }
        
        private void UpdateDisplay()
        {
            if (characterData == null) return;
            
            // 设置角色图标
            if (characterIcon != null && characterData.characterIcon != null)
            {
                characterIcon.sprite = characterData.characterIcon;
            }
            
            // 设置角色名称
            if (characterNameText != null)
            {
                characterNameText.text = characterData.characterName;
            }
            
            // 设置稀有度
            if (rarityText != null)
            {
                rarityText.text = characterData.rarity.ToString();
                rarityText.color = characterData.GetRarityColor();
            }
            
            // 设置稀有度边框
            if (rarityFrame != null)
            {
                rarityFrame.color = characterData.GetRarityColor();
            }
            
            // 设置背景颜色
            if (backgroundImage != null)
            {
                Color bgColor = characterData.GetRarityColor();
                bgColor.a = 0.1f; // 半透明背景
                backgroundImage.color = bgColor;
            }
            
            // 设置星级
            UpdateStarDisplay();
            
            // 设置新卡片效果
            SetupNewCardEffect();
        }
        
        private void UpdateStarDisplay()
        {
            if (starContainer == null || starPrefab == null) return;
            
            // 清空现有星星
            foreach (Transform child in starContainer)
            {
                Destroy(child.gameObject);
            }
            
            // 创建星星
            int starCount = characterData.GetStarCount();
            for (int i = 0; i < starCount; i++)
            {
                GameObject star = Instantiate(starPrefab, starContainer);
                Image starImage = star.GetComponent<Image>();
                if (starImage != null)
                {
                    starImage.color = characterData.GetRarityColor();
                }
            }
        }
        
        private void SetupNewCardEffect()
        {
            // 显示新卡片指示器
            if (newCardIndicator != null)
            {
                newCardIndicator.SetActive(isNewCard);
            }
            
            // 播放闪烁特效
            if (isNewCard && sparkleEffect != null)
            {
                sparkleEffect.Play();
            }
        }
        
        private void OnPointerEnter()
        {
            if (!enableHoverEffect) return;
            
            StartCoroutine(ScaleAnimation(hoverScale));
        }
        
        private void OnPointerExit()
        {
            if (!enableHoverEffect) return;
            
            StartCoroutine(ScaleAnimation(1f));
        }
        
        private System.Collections.IEnumerator ScaleAnimation(float targetScale)
        {
            Vector3 startScale = transform.localScale;
            Vector3 endScale = Vector3.one * targetScale;
            
            float elapsedTime = 0f;
            while (elapsedTime < animationDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float t = elapsedTime / animationDuration;
                t = Mathf.SmoothStep(0f, 1f, t);
                
                transform.localScale = Vector3.Lerp(startScale, endScale, t);
                yield return null;
            }
            
            transform.localScale = endScale;
        }
        
        /// <summary>
        /// 获取角色数据
        /// </summary>
        public CharacterData GetCharacterData()
        {
            return characterData;
        }
        
        /// <summary>
        /// 设置是否为新卡片
        /// </summary>
        public void SetNewCard(bool isNew)
        {
            isNewCard = isNew;
            SetupNewCardEffect();
        }
    }
}
