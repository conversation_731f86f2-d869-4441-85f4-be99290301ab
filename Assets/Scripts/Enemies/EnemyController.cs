using UnityEngine;
using RoguelikeGame.Core;
using RoguelikeGame.Characters;
using System.Collections;

namespace RoguelikeGame.Enemies
{
    /// <summary>
    /// 敌人控制器，处理敌人的AI行为、移动和攻击
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D))]
    [RequireComponent(typeof(Collider2D))]
    [RequireComponent(typeof(EnemyHealth))]
    public class EnemyController : MonoBehaviour
    {
        [Header("敌人数据")]
        public EnemyData enemyData;

        [Header("AI设置")]
        public LayerMask playerLayerMask = 1 << 0; // 玩家层
        public LayerMask obstacleLayerMask = 1 << 9; // 障碍物层

        [Header("调试设置")]
        public bool debugMode = false;
        public bool showDetectionRange = false;
        public bool showAttackRange = false;

        // 组件引用
        private Rigidbody2D rb;
        private Collider2D col;
        private Animator animator;
        private SpriteRenderer spriteRenderer;
        private EnemyHealth enemyHealth;

        // AI状态
        public EnemyAIState CurrentState { get; private set; }
        private Transform player;
        private Vector2 lastKnownPlayerPosition;
        private Vector2 patrolStartPosition;
        private Vector2 patrolTarget;
        private float stateTimer;
        private float lastAttackTime;

        // 当前属性
        public float Health => enemyHealth.CurrentHealth;
        public float MaxHealth => enemyHealth.maxHealth;
        public float AttackDamage { get; private set; }
        public float AttackSpeed { get; private set; }
        public float MoveSpeed { get; private set; }
        public float Defense { get; private set; }

        // 状态标志
        public bool IsAlive => enemyHealth.IsAlive;
        public bool CanMove { get; private set; } = true;
        public bool CanAttack { get; private set; } = true;

        // 事件系统
        public System.Action<EnemyController> OnEnemyDeath;
        public System.Action<EnemyController, float> OnEnemyDamaged;
        public System.Action<EnemyController> OnPlayerDetected;

        public EnemyData EnemyData => enemyData;

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            InitializeEnemy();
            FindPlayer();
        }

        private void Update()
        {
            if (!IsAlive) return;

            UpdateAI();
            UpdateAnimations();
        }

        private void InitializeComponents()
        {
            rb = GetComponent<Rigidbody2D>();
            col = GetComponent<Collider2D>();
            animator = GetComponent<Animator>();
            spriteRenderer = GetComponent<SpriteRenderer>();
            enemyHealth = GetComponent<EnemyHealth>();

            // 如果没有SpriteRenderer，创建一个
            if (spriteRenderer == null)
            {
                spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
                // 创建一个简单的方形精灵
                spriteRenderer.sprite = CreateSimpleSprite();
            }

            // 设置Rigidbody2D
            rb.gravityScale = 0f;
            rb.drag = 5f; // 添加阻力使移动更平滑
            rb.freezeRotation = true;
        }

        private void InitializeEnemy()
        {
            if (enemyData == null)
            {
                Debug.LogError($"敌人 {name} 没有设置EnemyData！");
                return;
            }

            // 根据当前关卡调整属性
            int currentLevel = GameManager.Instance ? GameManager.Instance.CurrentLevel : 1;
            var stats = enemyData.GetStatsForLevel(currentLevel);

            // 设置属性
            enemyHealth.SetMaxHealth(stats.health);
            AttackDamage = stats.attackDamage;
            AttackSpeed = stats.attackSpeed;
            MoveSpeed = stats.moveSpeed;
            Defense = stats.defense;

            // 设置视觉
            if (spriteRenderer && enemyData.enemySprite)
            {
                spriteRenderer.sprite = enemyData.enemySprite;
                spriteRenderer.color = enemyData.enemyColor;
            }

            // 设置动画控制器
            if (animator && enemyData.animatorController)
            {
                animator.runtimeAnimatorController = enemyData.animatorController;
            }

            // 初始化AI状态
            patrolStartPosition = transform.position;
            ChangeState(EnemyAIState.Patrol);

            // 设置事件监听
            enemyHealth.OnDeath += HandleDeath;
            enemyHealth.OnDamageTaken += HandleDamage;

            if (debugMode)
            {
                Debug.Log($"敌人初始化: {enemyData.enemyName}, 血量: {stats.health}, 攻击力: {stats.attackDamage}");
            }
        }

        private void FindPlayer()
        {
            var playerController = FindObjectOfType<PlayerController>();
            if (playerController != null)
            {
                player = playerController.transform;
            }
        }

        private void UpdateAI()
        {
            if (!CanMove) return;

            stateTimer += Time.deltaTime;

            // 检测玩家
            bool playerInDetectionRange = IsPlayerInRange(enemyData.detectionRange);
            bool playerInAttackRange = IsPlayerInRange(enemyData.attackRange);

            switch (CurrentState)
            {
                case EnemyAIState.Patrol:
                    HandlePatrolState(playerInDetectionRange);
                    break;

                case EnemyAIState.Chase:
                    HandleChaseState(playerInDetectionRange, playerInAttackRange);
                    break;

                case EnemyAIState.Attack:
                    HandleAttackState(playerInAttackRange);
                    break;

                case EnemyAIState.Retreat:
                    HandleRetreatState(playerInDetectionRange);
                    break;

                case EnemyAIState.Stunned:
                    HandleStunnedState();
                    break;
            }
        }

        private void HandlePatrolState(bool playerDetected)
        {
            if (playerDetected)
            {
                ChangeState(EnemyAIState.Chase);
                OnPlayerDetected?.Invoke(this);
                return;
            }

            // 巡逻逻辑
            switch (enemyData.aiType)
            {
                case EnemyAIType.Patrol:
                    PatrolBehavior();
                    break;
                case EnemyAIType.Ambush:
                    // 伏击型敌人静止等待
                    rb.velocity = Vector2.zero;
                    break;
                default:
                    // 其他类型随机移动
                    RandomMovement();
                    break;
            }
        }

        private void HandleChaseState(bool playerDetected, bool playerInAttackRange)
        {
            if (!playerDetected)
            {
                // 失去目标，返回巡逻
                if (stateTimer > 3f) // 3秒后放弃追击
                {
                    ChangeState(EnemyAIState.Patrol);
                }
                else
                {
                    // 前往最后已知位置
                    MoveTowards(lastKnownPlayerPosition);
                }
                return;
            }

            if (playerInAttackRange)
            {
                ChangeState(EnemyAIState.Attack);
                return;
            }

            // 追击玩家
            ChasePlayer();
        }

        private void HandleAttackState(bool playerInAttackRange)
        {
            if (!playerInAttackRange)
            {
                Debug.Log($"[敌人AI] {enemyData.enemyName} 玩家离开攻击范围，切换到追击状态");
                ChangeState(EnemyAIState.Chase);
                return;
            }

            // 停止移动准备攻击
            rb.velocity = Vector2.zero;

            // 攻击冷却检查
            float timeSinceLastAttack = Time.time - lastAttackTime;
            float attackCooldown = 1f / AttackSpeed;

            if (timeSinceLastAttack >= attackCooldown)
            {
                Debug.Log($"[敌人AI] {enemyData.enemyName} 攻击冷却完成，执行攻击");
                PerformAttack();
            }
            else
            {
                // 每秒显示一次冷却信息
                if (Time.frameCount % 60 == 0)
                {
                    Debug.Log($"[敌人AI] {enemyData.enemyName} 攻击冷却中: {timeSinceLastAttack:F1}/{attackCooldown:F1}秒");
                }
            }
        }

        private void HandleRetreatState(bool playerDetected)
        {
            // 谨慎型AI的撤退逻辑
            if (!playerDetected || stateTimer > 2f)
            {
                ChangeState(EnemyAIState.Patrol);
                return;
            }

            // 远离玩家
            if (player != null)
            {
                Vector2 retreatDirection = (transform.position - player.position).normalized;
                MoveInDirection(retreatDirection);
            }
        }

        private void HandleStunnedState()
        {
            rb.velocity = Vector2.zero;

            if (stateTimer > 1f) // 眩晕1秒
            {
                ChangeState(EnemyAIState.Chase);
            }
        }

        private void PatrolBehavior()
        {
            float distanceToTarget = Vector2.Distance(transform.position, patrolTarget);

            if (distanceToTarget < 0.5f || stateTimer > 5f)
            {
                // 选择新的巡逻目标
                Vector2 randomDirection = Random.insideUnitCircle.normalized;
                patrolTarget = patrolStartPosition + randomDirection * enemyData.patrolRange;
                stateTimer = 0f;
            }

            MoveTowards(patrolTarget);
        }

        private void RandomMovement()
        {
            if (stateTimer > 2f)
            {
                Vector2 randomDirection = Random.insideUnitCircle.normalized;
                MoveInDirection(randomDirection);
                stateTimer = 0f;
            }
        }

        private void ChasePlayer()
        {
            if (player == null) return;

            lastKnownPlayerPosition = player.position;

            switch (enemyData.aiType)
            {
                case EnemyAIType.Aggressive:
                    // 直接冲向玩家
                    MoveTowards(player.position);
                    break;

                case EnemyAIType.Cautious:
                    // 保持距离
                    float distanceToPlayer = Vector2.Distance(transform.position, player.position);
                    if (distanceToPlayer < enemyData.attackRange * 1.5f)
                    {
                        ChangeState(EnemyAIState.Retreat);
                    }
                    else
                    {
                        MoveTowards(player.position);
                    }
                    break;

                case EnemyAIType.Swarm:
                    // 群体行为，考虑其他敌人位置
                    Vector2 swarmDirection = CalculateSwarmDirection();
                    MoveInDirection(swarmDirection);
                    break;

                default:
                    MoveTowards(player.position);
                    break;
            }
        }

        private Vector2 CalculateSwarmDirection()
        {
            if (player == null) return Vector2.zero;

            Vector2 playerDirection = (player.position - transform.position).normalized;
            Vector2 separationForce = Vector2.zero;

            // 与其他敌人保持距离
            Collider2D[] nearbyEnemies = Physics2D.OverlapCircleAll(transform.position, 2f);
            int enemyCount = 0;

            foreach (var enemy in nearbyEnemies)
            {
                // 通过名称或组件检查是否为敌人，避免标签问题
                if (enemy != col && (enemy.name.Contains("Enemy") || enemy.GetComponent<EnemyController>() != null))
                {
                    Vector2 separation = transform.position - enemy.transform.position;
                    separationForce += separation.normalized / separation.magnitude;
                    enemyCount++;
                }
            }

            if (enemyCount > 0)
            {
                separationForce /= enemyCount;
            }

            return (playerDirection + separationForce * 0.5f).normalized;
        }

        private void PerformAttack()
        {
            if (!CanAttack || player == null) return;

            lastAttackTime = Time.time;

            // 强制调试输出 - 总是显示攻击信息
            Debug.Log($"[敌人攻击] {enemyData.enemyName} 在位置 {transform.position} 攻击玩家");
            Debug.Log($"[敌人攻击] 攻击伤害: {AttackDamage}, 攻击间隔: {1f / AttackSpeed}秒");
            Debug.Log($"[敌人攻击] 距离玩家: {Vector2.Distance(transform.position, player.position)}");

            // 播放攻击动画
            if (animator)
            {
                animator.SetTrigger("Attack");
            }

            // 播放攻击音效
            if (enemyData.attackSound)
            {
                AudioManager.Instance?.PlaySFX(enemyData.attackSound);
            }

            // 对玩家造成伤害
            var playerController = player.GetComponent<PlayerController>();
            if (playerController && playerController.IsAlive)
            {
                Vector2 knockbackDirection = (player.position - transform.position).normalized;
                Debug.Log($"[敌人攻击] 对玩家造成 {AttackDamage} 点伤害，击退方向: {knockbackDirection}");
                playerController.TakeDamage(AttackDamage, knockbackDirection * 5f);
            }
            else
            {
                Debug.Log("[敌人攻击] 玩家控制器无效或玩家已死亡");
            }
        }

        private void MoveTowards(Vector2 target)
        {
            Vector2 direction = (target - (Vector2)transform.position).normalized;
            MoveInDirection(direction);
        }

        private void MoveInDirection(Vector2 direction)
        {
            if (!CanMove) return;

            // 检查路径是否被阻挡
            RaycastHit2D hit = Physics2D.Raycast(transform.position, direction, 1f, obstacleLayerMask);
            if (hit.collider != null)
            {
                // 尝试绕过障碍物
                Vector2 avoidDirection = Vector2.Perpendicular(direction);
                if (Random.value > 0.5f) avoidDirection = -avoidDirection;
                direction = (direction + avoidDirection).normalized;
            }

            rb.velocity = direction * MoveSpeed;
        }

        private bool IsPlayerInRange(float range)
        {
            if (player == null) return false;

            float distance = Vector2.Distance(transform.position, player.position);
            return distance <= range;
        }

        private void ChangeState(EnemyAIState newState)
        {
            if (CurrentState == newState) return;

            CurrentState = newState;
            stateTimer = 0f;

            if (debugMode)
            {
                Debug.Log($"{enemyData.enemyName} 状态改变: {newState}");
            }
        }

        private void UpdateAnimations()
        {
            if (animator == null) return;

            Vector2 velocity = rb.velocity;
            animator.SetFloat("MoveX", velocity.x);
            animator.SetFloat("MoveY", velocity.y);
            animator.SetBool("IsMoving", velocity.magnitude > 0.1f);
            animator.SetFloat("MoveSpeed", velocity.magnitude);
            animator.SetBool("IsAlive", IsAlive);
        }

        #region 事件处理

        public void OnTakeDamage(float damage, Vector2 knockbackDirection)
        {
            // 受击时的反应
            if (CurrentState == EnemyAIState.Patrol)
            {
                ChangeState(EnemyAIState.Chase);
            }

            OnEnemyDamaged?.Invoke(this, damage);
        }

        public void OnDeath()
        {
            CanMove = false;
            CanAttack = false;
            rb.velocity = Vector2.zero;

            // 掉落物品
            DropItems();

            // 给予经验值
            GiveExperience();

            OnEnemyDeath?.Invoke(this);

            if (debugMode)
            {
                Debug.Log($"{enemyData.enemyName} 死亡");
            }
        }

        private void HandleDeath()
        {
            OnDeath();
        }

        private void HandleDamage(float damage)
        {
            OnTakeDamage(damage, Vector2.zero);
        }

        #endregion

        #region 掉落和经验

        private void DropItems()
        {
            if (enemyData.dropItems == null) return;

            foreach (var dropItem in enemyData.dropItems)
            {
                if (Random.value <= dropItem.dropChance)
                {
                    int amount = Random.Range(dropItem.minAmount, dropItem.maxAmount + 1);

                    for (int i = 0; i < amount; i++)
                    {
                        Vector2 dropPosition = (Vector2)transform.position + Random.insideUnitCircle * 0.5f;

                        if (dropItem.itemPrefab != null)
                        {
                            Instantiate(dropItem.itemPrefab, dropPosition, Quaternion.identity);
                        }
                    }
                }
            }
        }

        private void GiveExperience()
        {
            // TODO: 实现经验系统
            if (debugMode)
            {
                Debug.Log($"获得 {enemyData.experienceValue} 点经验");
            }
        }

        #endregion

        #region 公共接口

        public void SetEnemyData(EnemyData data)
        {
            enemyData = data;
            if (gameObject.activeInHierarchy)
            {
                InitializeEnemy();
            }
        }

        public void SetCanMove(bool canMove)
        {
            CanMove = canMove;
            if (!canMove)
            {
                rb.velocity = Vector2.zero;
            }
        }

        public void SetCanAttack(bool canAttack)
        {
            CanAttack = canAttack;
        }

        public void Stun(float duration)
        {
            ChangeState(EnemyAIState.Stunned);
            StartCoroutine(StunCoroutine(duration));
        }

        private IEnumerator StunCoroutine(float duration)
        {
            yield return new WaitForSeconds(duration);
            if (CurrentState == EnemyAIState.Stunned)
            {
                ChangeState(EnemyAIState.Chase);
            }
        }

        #endregion

        #region 调试功能

        private void OnDrawGizmosSelected()
        {
            if (enemyData == null) return;

            // 检测范围
            if (showDetectionRange)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(transform.position, enemyData.detectionRange);
            }

            // 攻击范围
            if (showAttackRange)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(transform.position, enemyData.attackRange);
            }

            // 巡逻范围
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(patrolStartPosition, enemyData.patrolRange);
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 创建简单的方形精灵
        /// </summary>
        private Sprite CreateSimpleSprite()
        {
            // 创建一个1x1的纹理
            Texture2D texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, Color.white);
            texture.Apply();

            // 创建精灵
            return Sprite.Create(texture, new Rect(0, 0, 1, 1), new Vector2(0.5f, 0.5f), 100f);
        }

        #endregion
    }

    /// <summary>
    /// 敌人AI状态枚举
    /// </summary>
    public enum EnemyAIState
    {
        Patrol,     // 巡逻
        Chase,      // 追击
        Attack,     // 攻击
        Retreat,    // 撤退
        Stunned     // 眩晕
    }
}
