using UnityEngine;
using RoguelikeGame.Core;
using RoguelikeGame.Enemies;
using RoguelikeGame.Characters;
using System.Collections.Generic;
using System.Collections;

namespace RoguelikeGame.Levels
{
    /// <summary>
    /// 关卡管理器，负责敌人生成、关卡进度和目标管理
    /// </summary>
    public class LevelManager : MonoBehaviour
    {
        [Header("关卡设置")]
        public LevelData currentLevelData;
        public Transform[] spawnPoints;
        public float spawnRadius = 10f;
        public float minSpawnDistance = 3f;

        [Header("敌人设置")]
        public EnemyData[] normalEnemies;
        public EnemyData[] eliteEnemies;
        public EnemyData[] bossEnemies;
        public GameObject enemyPrefab;

        [Header("生成控制")]
        public float spawnInterval = 2f;
        public int maxEnemiesOnScreen = 50;
        public bool continuousSpawn = true;

        [Header("调试设置")]
        public bool debugMode = false;

        // 单例模式
        public static LevelManager Instance { get; private set; }

        // 关卡状态
        public int TotalEnemiesSpawned { get; private set; }
        public int TotalEnemiesKilled { get; private set; }
        public int CurrentEnemiesAlive { get; private set; }
        public bool IsLevelComplete { get; private set; }
        public bool IsBossLevel { get; private set; }

        // 敌人管理
        private List<EnemyController> activeEnemies;
        private Queue<EnemySpawnData> spawnQueue;
        private Coroutine spawnCoroutine;
        private PlayerController player;

        // 关卡目标
        private int targetEnemyCount;
        private float levelStartTime;

        // 事件系统
        public System.Action<int, int> OnEnemyCountChanged;
        public System.Action OnLevelComplete;
        public System.Action OnBossSpawned;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeLevelManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupEventListeners();
            FindPlayer();
            StartLevel();
        }

        private void InitializeLevelManager()
        {
            activeEnemies = new List<EnemyController>();
            spawnQueue = new Queue<EnemySpawnData>();

            // 如果没有设置生成点，创建默认生成点
            if (spawnPoints == null || spawnPoints.Length == 0)
            {
                CreateDefaultSpawnPoints();
            }
        }

        private void SetupEventListeners()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged += OnGameStateChanged;
                GameManager.Instance.OnLevelChanged += OnLevelChanged;
            }
        }

        private void OnDestroy()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged -= OnGameStateChanged;
                GameManager.Instance.OnLevelChanged -= OnLevelChanged;
            }
        }

        private void FindPlayer()
        {
            player = FindObjectOfType<PlayerController>();
            if (player == null)
            {
                Debug.LogError("场景中没有找到PlayerController！");
            }
        }

        private void CreateDefaultSpawnPoints()
        {
            spawnPoints = new Transform[8];

            for (int i = 0; i < 8; i++)
            {
                GameObject spawnPoint = new GameObject($"SpawnPoint_{i}");
                spawnPoint.transform.SetParent(transform);

                float angle = i * 45f * Mathf.Deg2Rad;
                Vector3 position = new Vector3(
                    Mathf.Cos(angle) * spawnRadius,
                    Mathf.Sin(angle) * spawnRadius,
                    0f
                );
                spawnPoint.transform.position = position;
                spawnPoints[i] = spawnPoint.transform;
            }
        }

        public void StartLevel()
        {
            if (GameManager.Instance == null) return;

            int currentLevel = GameManager.Instance.CurrentLevel;
            IsBossLevel = GameManager.Instance.IsBossLevel();

            // 重置关卡状态
            TotalEnemiesSpawned = 0;
            TotalEnemiesKilled = 0;
            CurrentEnemiesAlive = 0;
            IsLevelComplete = false;
            levelStartTime = Time.time;

            // 清理之前的敌人
            ClearAllEnemies();

            // 生成关卡数据
            if (currentLevelData == null)
            {
                currentLevelData = GenerateLevelData(currentLevel);
            }

            // 设置目标敌人数量
            targetEnemyCount = currentLevelData.totalEnemyCount;

            // 开始生成敌人
            if (IsBossLevel)
            {
                StartBossLevel();
            }
            else
            {
                StartNormalLevel();
            }

            // 更新UI
            UpdateEnemyCountUI();

            if (debugMode)
            {
                Debug.Log($"开始关卡 {currentLevel}, 目标敌人数量: {targetEnemyCount}, Boss关: {IsBossLevel}");
            }
        }

        private void StartNormalLevel()
        {
            // 创建生成队列
            CreateSpawnQueue();

            // 开始连续生成
            if (spawnCoroutine != null)
            {
                StopCoroutine(spawnCoroutine);
            }
            spawnCoroutine = StartCoroutine(SpawnEnemiesCoroutine());
        }

        private void StartBossLevel()
        {
            // 先生成一些小怪
            int minionsCount = Mathf.RoundToInt(targetEnemyCount * 0.3f);
            for (int i = 0; i < minionsCount; i++)
            {
                SpawnRandomEnemy(EnemyType.Normal);
            }

            // 延迟生成Boss
            StartCoroutine(SpawnBossCoroutine());
        }

        private IEnumerator SpawnBossCoroutine()
        {
            yield return new WaitForSeconds(3f);

            if (bossEnemies != null && bossEnemies.Length > 0)
            {
                EnemyData bossData = bossEnemies[Random.Range(0, bossEnemies.Length)];
                SpawnEnemy(bossData, GetCenterSpawnPosition());
                OnBossSpawned?.Invoke();

                if (debugMode)
                {
                    Debug.Log($"生成Boss: {bossData.enemyName}");
                }
            }
        }

        private void CreateSpawnQueue()
        {
            spawnQueue.Clear();

            // 根据关卡数据创建生成队列
            for (int i = 0; i < currentLevelData.normalEnemyCount; i++)
            {
                spawnQueue.Enqueue(new EnemySpawnData
                {
                    enemyType = EnemyType.Normal,
                    spawnTime = Random.Range(0f, currentLevelData.spawnDuration)
                });
            }

            for (int i = 0; i < currentLevelData.eliteEnemyCount; i++)
            {
                spawnQueue.Enqueue(new EnemySpawnData
                {
                    enemyType = EnemyType.Elite,
                    spawnTime = Random.Range(currentLevelData.spawnDuration * 0.3f, currentLevelData.spawnDuration)
                });
            }

            // 按时间排序
            var sortedSpawns = new List<EnemySpawnData>(spawnQueue);
            sortedSpawns.Sort((a, b) => a.spawnTime.CompareTo(b.spawnTime));

            spawnQueue.Clear();
            foreach (var spawn in sortedSpawns)
            {
                spawnQueue.Enqueue(spawn);
            }
        }

        private IEnumerator SpawnEnemiesCoroutine()
        {
            float elapsedTime = 0f;

            while (spawnQueue.Count > 0 || (continuousSpawn && !IsLevelComplete))
            {
                elapsedTime += Time.deltaTime;

                // 从队列生成敌人
                while (spawnQueue.Count > 0 && spawnQueue.Peek().spawnTime <= elapsedTime)
                {
                    var spawnData = spawnQueue.Dequeue();
                    if (CurrentEnemiesAlive < maxEnemiesOnScreen)
                    {
                        SpawnRandomEnemy(spawnData.enemyType);
                    }
                }

                // 连续生成模式
                if (continuousSpawn && !IsBossLevel && CurrentEnemiesAlive < maxEnemiesOnScreen / 2)
                {
                    if (Random.value < 0.3f) // 30%概率生成
                    {
                        SpawnRandomEnemy(EnemyType.Normal);
                    }
                }

                yield return new WaitForSeconds(spawnInterval);
            }
        }

        private void SpawnRandomEnemy(EnemyType enemyType)
        {
            EnemyData[] enemyPool = null;

            switch (enemyType)
            {
                case EnemyType.Normal:
                    enemyPool = normalEnemies;
                    break;
                case EnemyType.Elite:
                    enemyPool = eliteEnemies;
                    break;
                case EnemyType.Boss:
                    enemyPool = bossEnemies;
                    break;
            }

            if (enemyPool == null || enemyPool.Length == 0)
            {
                Debug.LogWarning($"没有可用的 {enemyType} 类型敌人数据");
                return;
            }

            EnemyData enemyData = enemyPool[Random.Range(0, enemyPool.Length)];
            Vector3 spawnPosition = GetRandomSpawnPosition();

            SpawnEnemy(enemyData, spawnPosition);
        }

        private void SpawnEnemy(EnemyData enemyData, Vector3 position)
        {
            if (enemyPrefab == null)
            {
                Debug.LogError("敌人预制体未设置！");
                return;
            }

            GameObject enemyObj = Instantiate(enemyPrefab, position, Quaternion.identity);
            EnemyController enemyController = enemyObj.GetComponent<EnemyController>();

            if (enemyController != null)
            {
                enemyController.SetEnemyData(enemyData);
                enemyController.OnEnemyDeath += OnEnemyKilled;
                activeEnemies.Add(enemyController);

                TotalEnemiesSpawned++;
                CurrentEnemiesAlive++;

                UpdateEnemyCountUI();

                if (debugMode)
                {
                    Debug.Log($"生成敌人: {enemyData.enemyName} 在位置 {position}");
                }
            }
        }

        private Vector3 GetRandomSpawnPosition()
        {
            Vector3 spawnPosition;
            int attempts = 0;

            do
            {
                Transform spawnPoint = spawnPoints[Random.Range(0, spawnPoints.Length)];
                Vector2 randomOffset = Random.insideUnitCircle * 2f;
                spawnPosition = spawnPoint.position + new Vector3(randomOffset.x, randomOffset.y, 0f);
                attempts++;
            }
            while (IsPositionTooCloseToPlayer(spawnPosition) && attempts < 10);

            return spawnPosition;
        }

        private Vector3 GetCenterSpawnPosition()
        {
            return Vector3.zero; // Boss在中心生成
        }

        private bool IsPositionTooCloseToPlayer(Vector3 position)
        {
            if (player == null) return false;

            float distance = Vector3.Distance(position, player.transform.position);
            return distance < minSpawnDistance;
        }

        private void OnEnemyKilled(EnemyController enemy)
        {
            if (activeEnemies.Contains(enemy))
            {
                activeEnemies.Remove(enemy);
                TotalEnemiesKilled++;
                CurrentEnemiesAlive--;

                UpdateEnemyCountUI();

                // 检查关卡完成条件
                CheckLevelCompletion();

                if (debugMode)
                {
                    Debug.Log($"敌人被击杀: {enemy.EnemyData.enemyName}, 剩余: {CurrentEnemiesAlive}");
                }
            }
        }

        private void CheckLevelCompletion()
        {
            bool levelComplete = false;

            // 强制调试输出 - 检查关卡完成条件
            Debug.Log($"CheckLevelCompletion - IsBossLevel: {IsBossLevel}, TotalEnemiesKilled: {TotalEnemiesKilled}, targetEnemyCount: {targetEnemyCount}");
            Debug.Log($"CheckLevelCompletion - levelStartTime: {levelStartTime}, Time.time: {Time.time}, 时间差: {Time.time - levelStartTime}");
            Debug.Log($"CheckLevelCompletion - levelTimeLimit: {(GameManager.Instance != null ? GameManager.Instance.levelTimeLimit : "GameManager为null")}");

            if (IsBossLevel)
            {
                // Boss关：击败所有Boss
                levelComplete = !HasAliveBoss();
                Debug.Log($"Boss关完成检查: {levelComplete}");
            }
            else
            {
                // 普通关：击败足够数量的敌人或时间到
                bool enemyCountReached = TotalEnemiesKilled >= targetEnemyCount;
                bool timeUp = GameManager.Instance != null && (Time.time - levelStartTime) >= GameManager.Instance.levelTimeLimit;

                Debug.Log($"普通关完成检查 - 敌人击杀达标: {enemyCountReached}, 时间到: {timeUp}");

                // 修复：确保targetEnemyCount > 0，并且只有在有意义的条件下才完成关卡
                levelComplete = (targetEnemyCount > 0 && enemyCountReached) || timeUp;
            }

            Debug.Log($"最终关卡完成判断: {levelComplete}, IsLevelComplete: {IsLevelComplete}");

            if (levelComplete && !IsLevelComplete)
            {
                Debug.Log("触发关卡完成！");
                CompleteLevelLevel();
            }
        }

        private bool HasAliveBoss()
        {
            foreach (var enemy in activeEnemies)
            {
                if (enemy.EnemyData.enemyType == EnemyType.Boss && enemy.IsAlive)
                {
                    return true;
                }
            }
            return false;
        }

        private void CompleteLevelLevel()
        {
            IsLevelComplete = true;

            // 停止生成敌人
            if (spawnCoroutine != null)
            {
                StopCoroutine(spawnCoroutine);
                spawnCoroutine = null;
            }

            // 播放关卡完成音效
            AudioManager.Instance?.PlayLevelComplete();

            // 触发事件
            OnLevelComplete?.Invoke();

            // 通知游戏管理器
            GameManager.Instance?.LevelComplete();

            if (debugMode)
            {
                Debug.Log($"关卡完成！击杀敌人: {TotalEnemiesKilled}/{targetEnemyCount}");
            }
        }

        private void UpdateEnemyCountUI()
        {
            OnEnemyCountChanged?.Invoke(CurrentEnemiesAlive, targetEnemyCount);
            UIManager.Instance?.UpdateEnemyCount(CurrentEnemiesAlive, targetEnemyCount);
        }

        private void ClearAllEnemies()
        {
            foreach (var enemy in activeEnemies)
            {
                if (enemy != null)
                {
                    Destroy(enemy.gameObject);
                }
            }
            activeEnemies.Clear();
            CurrentEnemiesAlive = 0;
        }

        private LevelData GenerateLevelData(int level)
        {
            var levelData = ScriptableObject.CreateInstance<LevelData>();

            // 根据关卡等级计算敌人数量
            levelData.normalEnemyCount = Mathf.RoundToInt(20 + level * 3f);
            levelData.eliteEnemyCount = Mathf.RoundToInt(level * 0.5f);
            levelData.spawnDuration = 180f; // 3分钟内生成完所有敌人

            return levelData;
        }

        #region 事件处理

        private void OnGameStateChanged(GameState newState)
        {
            switch (newState)
            {
                case GameState.Playing:
                    // 恢复敌人AI
                    foreach (var enemy in activeEnemies)
                    {
                        if (enemy != null)
                        {
                            enemy.SetCanMove(true);
                            enemy.SetCanAttack(true);
                        }
                    }
                    break;

                case GameState.Paused:
                    // 暂停敌人AI
                    foreach (var enemy in activeEnemies)
                    {
                        if (enemy != null)
                        {
                            enemy.SetCanMove(false);
                            enemy.SetCanAttack(false);
                        }
                    }
                    break;
            }
        }

        private void OnLevelChanged(int newLevel)
        {
            currentLevelData = null; // 重置关卡数据，让系统重新生成
        }

        #endregion

        #region 公共接口

        public void SetLevelData(LevelData levelData)
        {
            currentLevelData = levelData;
        }

        public void ForceSpawnEnemy(EnemyData enemyData, Vector3 position)
        {
            SpawnEnemy(enemyData, position);
        }

        public void ClearLevel()
        {
            ClearAllEnemies();
            IsLevelComplete = true;
        }

        public List<EnemyController> GetActiveEnemies()
        {
            return new List<EnemyController>(activeEnemies);
        }

        #endregion
    }

    /// <summary>
    /// 敌人生成数据结构
    /// </summary>
    [System.Serializable]
    public class EnemySpawnData
    {
        public EnemyType enemyType;
        public float spawnTime;
    }
}
