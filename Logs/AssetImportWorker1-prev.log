Tuanjie Editor version:  2022.3.55t3 (60da82e5d6ab)
Branch:                  tuanjie/1.5/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4.1 (Build 24E263)
Darwin version:          24.4.0
Architecture:            x86_64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MacOS/Tuanjie
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
-logFile
Logs/AssetImportWorker1.log
-srvPort
49861
Successfully changed project path to: /Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
/Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [140704323959744]  Target information:

Player connection [140704323959744]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2896504246 [EditorId] 2896504246 [Version] 1048832 [Id] OSXEditor(0,huangcongqiangdeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [140704323959744] Host joined multi-casting on [***********:54997]...
Player connection [140704323959744] Host joined alternative multi-casting on [***********:34997]...
AS: AutoStreaming module initializing.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Refreshing native plugins compatible for Editor in 51.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.55t3 (60da82e5d6ab)
[Subsystems] Discovering subsystems at path /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Color LCD preferred device: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Metal devices available: 1
0: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Using device Intel(R) Iris(TM) Plus Graphics 655 (low power)
Initializing Metal device caps: Intel(R) Iris(TM) Plus Graphics 655
Initialize mono
Mono path[0] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed'
Mono path[1] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56014
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Registered in 0.011544 seconds.
- Loaded All Assemblies, in  1.063 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.593 seconds
Domain Reload Profiling: 1657ms
	BeginReloadAssembly (464ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (118ms)
	LoadAllAssembliesAndSetupDomain (381ms)
		LoadAssemblies (464ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (369ms)
			TypeCache.Refresh (363ms)
				TypeCache.ScanAssembly (321ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (594ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (479ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (13ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (4ms)
			ProcessInitializeOnLoadAttributes (403ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.653 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.659 seconds
Domain Reload Profiling: 3310ms
	BeginReloadAssembly (255ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (71ms)
	LoadAllAssembliesAndSetupDomain (1243ms)
		LoadAssemblies (895ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (499ms)
			TypeCache.Refresh (416ms)
				TypeCache.ScanAssembly (378ms)
			ScanForSourceGeneratedMonoScriptInfo (52ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1661ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1225ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (10ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (756ms)
			ProcessInitializeOnLoadMethodAttributes (310ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Launching external process: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/TuanjieShaderCompiler
Launched and connected shader compiler TuanjieShaderCompiler after 0.77 seconds
Refreshing native plugins compatible for Editor in 8.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5774 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (0.8 MB). Loaded Objects now: 6240.
Memory consumption went from 233.5 MB to 232.7 MB.
Total: 12.195345 ms (FindLiveObjects: 0.670586 ms CreateObjectMapping: 0.552761 ms MarkObjects: 7.930446 ms  DeleteObjects: 3.039762 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 578552.348731 seconds.
  path: Assets/截屏2025-05-27 10.05.50.png
  artifactKey: Guid(76c9ab7991d2643169ff8c718ec2190c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/截屏2025-05-27 10.05.50.png using Guid(76c9ab7991d2643169ff8c718ec2190c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'ef2e565bccda2feded6fd4e7f786325e') in 0.248546 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.399 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.633 seconds
Domain Reload Profiling: 5033ms
	BeginReloadAssembly (2353ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (1773ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (260ms)
	RebuildCommonClasses (97ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (858ms)
		LoadAssemblies (1040ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (64ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (35ms)
	FinalizeReload (1634ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1102ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (14ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (746ms)
			ProcessInitializeOnLoadMethodAttributes (162ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5636 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6252.
Memory consumption went from 217.3 MB to 216.6 MB.
Total: 5.262974 ms (FindLiveObjects: 0.413682 ms CreateObjectMapping: 0.509761 ms MarkObjects: 3.886355 ms  DeleteObjects: 0.452105 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.792 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.905 seconds
Domain Reload Profiling: 4697ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (443ms)
		LoadAssemblies (516ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (34ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (3906ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1366ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (19ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (229ms)
			ProcessInitializeOnLoadAttributes (911ms)
			ProcessInitializeOnLoadMethodAttributes (181ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 46.00 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5636 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6257.
Memory consumption went from 221.3 MB to 220.5 MB.
Total: 11.286916 ms (FindLiveObjects: 0.750951 ms CreateObjectMapping: 0.761471 ms MarkObjects: 9.033811 ms  DeleteObjects: 0.739117 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
