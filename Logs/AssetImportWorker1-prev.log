Tuanjie Editor version:  2022.3.55t3 (60da82e5d6ab)
Branch:                  tuanjie/1.5/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4.1 (Build 24E263)
Darwin version:          24.4.0
Architecture:            x86_64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MacOS/Tuanjie
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
-logFile
Logs/AssetImportWorker1.log
-srvPort
61196
Successfully changed project path to: /Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
/Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [140704323959744]  Target information:

Player connection [140704323959744]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2126289767 [EditorId] 2126289767 [Version] 1048832 [Id] OSXEditor(0,huangcongqiangdeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [140704323959744] Host joined multi-casting on [***********:54997]...
Player connection [140704323959744] Host joined alternative multi-casting on [***********:34997]...
AS: AutoStreaming module initializing.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Refreshing native plugins compatible for Editor in 53.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.55t3 (60da82e5d6ab)
[Subsystems] Discovering subsystems at path /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Color LCD preferred device: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Metal devices available: 1
0: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Using device Intel(R) Iris(TM) Plus Graphics 655 (low power)
Initializing Metal device caps: Intel(R) Iris(TM) Plus Graphics 655
Initialize mono
Mono path[0] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed'
Mono path[1] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56119
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Registered in 0.014967 seconds.
- Loaded All Assemblies, in  1.011 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.862 seconds
Domain Reload Profiling: 1872ms
	BeginReloadAssembly (400ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (85ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (114ms)
	LoadAllAssembliesAndSetupDomain (389ms)
		LoadAssemblies (407ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (379ms)
			TypeCache.Refresh (373ms)
				TypeCache.ScanAssembly (308ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (2ms)
	FinalizeReload (862ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (673ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (20ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (5ms)
			ProcessInitializeOnLoadAttributes (592ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.876 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.783 seconds
Domain Reload Profiling: 3662ms
	BeginReloadAssembly (393ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (96ms)
	RebuildNativeTypeToScriptingClass (36ms)
	initialDomainReloadingComplete (101ms)
	LoadAllAssembliesAndSetupDomain (1252ms)
		LoadAssemblies (897ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (548ms)
			TypeCache.Refresh (451ms)
				TypeCache.ScanAssembly (409ms)
			ScanForSourceGeneratedMonoScriptInfo (65ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (1784ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1360ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (13ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (182ms)
			ProcessInitializeOnLoadAttributes (904ms)
			ProcessInitializeOnLoadMethodAttributes (244ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Launching external process: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/TuanjieShaderCompiler
Launched and connected shader compiler TuanjieShaderCompiler after 0.87 seconds
Refreshing native plugins compatible for Editor in 7.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5772 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (0.8 MB). Loaded Objects now: 6238.
Memory consumption went from 233.5 MB to 232.7 MB.
Total: 19.888079 ms (FindLiveObjects: 1.881284 ms CreateObjectMapping: 1.157544 ms MarkObjects: 12.010549 ms  DeleteObjects: 4.836642 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 536343.454574 seconds.
  path: Assets/Settings/Renderer2D.asset
  artifactKey: Guid(424799608f7334c24bf367e4bbfa7f9a) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Settings/Renderer2D.asset using Guid(424799608f7334c24bf367e4bbfa7f9a) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'c8e18dd7e56d75a8b75de2116170218c') in 0.523094 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/Scripts/UI/AbilityChoice.cs
  artifactKey: Guid(0ac7b74a3a9ed41f3866c23d48f403d8) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/UI/AbilityChoice.cs using Guid(0ac7b74a3a9ed41f3866c23d48f403d8) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '3825713dc36760e5687b413860170d5a') in 0.028892 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000567 seconds.
  path: Assets/Settings/UniversalRP.asset
  artifactKey: Guid(681886c5eb7344803b6206f758bf0b1c) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Settings/UniversalRP.asset using Guid(681886c5eb7344803b6206f758bf0b1c) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'ff5f81e45b576165d4bd6a3ee19333d6') in 0.073711 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Data/Examples/ExampleCharacterData.md
  artifactKey: Guid(c7b9e1d43c9c845f6bbecc769b9beee3) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Data/Examples/ExampleCharacterData.md using Guid(c7b9e1d43c9c845f6bbecc769b9beee3) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '441f17de8a2df1ca1cf9ae23bec62575') in 0.012392 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000094 seconds.
  path: Assets/Scripts/UI/DamageNumber.cs
  artifactKey: Guid(29aa22bb629334a2b895e5b972e483cd) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/UI/DamageNumber.cs using Guid(29aa22bb629334a2b895e5b972e483cd) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '46a7334964b33fe7b9f83deb8e13afd9') in 0.005483 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000113 seconds.
  path: Assets/Data/Examples/ExampleAbilityData.md
  artifactKey: Guid(459bd9da8ed6b4feda8617a4247ea9cf) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Data/Examples/ExampleAbilityData.md using Guid(459bd9da8ed6b4feda8617a4247ea9cf) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '42e965a3736fc5ada35f63cbe22f4724') in 0.005912 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000182 seconds.
  path: Assets/Scripts/Core/DataManager.cs
  artifactKey: Guid(c39b87a1eae004dbbb0714b75792277b) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Core/DataManager.cs using Guid(c39b87a1eae004dbbb0714b75792277b) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '713bd1e46610a98cd6dc162d6dded49e') in 0.008010 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000801 seconds.
  path: Assets/Scripts/Enemies/EnemyData.cs
  artifactKey: Guid(d5bf2fd4ddbc249a698a99e99bf7558d) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Enemies/EnemyData.cs using Guid(d5bf2fd4ddbc249a698a99e99bf7558d) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'a0affc614e07a7eef9cf60d103b92e02') in 0.008251 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000146 seconds.
  path: Assets/Scripts/Weapons/ProjectileController.cs
  artifactKey: Guid(7d79608aa446d49dfa0acee89126c343) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Weapons/ProjectileController.cs using Guid(7d79608aa446d49dfa0acee89126c343) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'ba21aed18b14d381633cbc3a153b48e7') in 0.006959 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Scripts/UI/EnemyHealthBar.cs
  artifactKey: Guid(3c1428e789e924e1ea7551af2f26bcb9) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/UI/EnemyHealthBar.cs using Guid(3c1428e789e924e1ea7551af2f26bcb9) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '89f9f161ff54ad14474963e090c5e98f') in 0.007546 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000092 seconds.
  path: Assets/Scripts/Core/SimpleEnemyMover.cs
  artifactKey: Guid(91e73a3f149e84cbcbcac4df643b29e4) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Core/SimpleEnemyMover.cs using Guid(91e73a3f149e84cbcbcac4df643b29e4) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'd911c34fba20db81fcd2cdbde851882f') in 0.012222 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000230 seconds.
  path: Assets/Scripts/Levels/LevelData.cs
  artifactKey: Guid(ede99a90e98a04c4c8d90ff46e65c03d) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Levels/LevelData.cs using Guid(ede99a90e98a04c4c8d90ff46e65c03d) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '337670c1a2aa2fcab354d0ed35637ee3') in 0.010288 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000211 seconds.
  path: Assets/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/UniversalRenderPipelineGlobalSettings.asset using Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '9ec614fcf3103b475456b1ec7d576558') in 0.015146 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.001815 seconds.
  path: Assets/Scripts/Utils/SceneSetup.cs
  artifactKey: Guid(12345678901234567890123456789012) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Utils/SceneSetup.cs using Guid(12345678901234567890123456789012) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '573831b023f05750b2bea1fe9db1452f') in 0.010785 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.004578 seconds.
  path: Assets/Scripts/Weapons/WeaponController.cs
  artifactKey: Guid(d5426a0799a4246e1b5f87cb0d4158a5) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Weapons/WeaponController.cs using Guid(d5426a0799a4246e1b5f87cb0d4158a5) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '2df51059ae0e31cf80f8d78b89553bb0') in 0.017289 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000165 seconds.
  path: Assets/Scripts/UI/CharacterCard.cs
  artifactKey: Guid(ed46d635e9ea64f2ca1283ea39130a95) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/UI/CharacterCard.cs using Guid(ed46d635e9ea64f2ca1283ea39130a95) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '9ab75e37bcb768f0b6922a5d62899c57') in 0.009818 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Scripts/Core/AudioManager.cs
  artifactKey: Guid(9db1f8f38e8594591acc05ced0bf5912) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Core/AudioManager.cs using Guid(9db1f8f38e8594591acc05ced0bf5912) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '6eb3dffe407baba8673dd7f65e7ec657') in 0.008412 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000115 seconds.
  path: Assets/Data/Examples/ColorSystemImplementation.md
  artifactKey: Guid(3edf3f1ca7ae0434abd74362db7690e4) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Data/Examples/ColorSystemImplementation.md using Guid(3edf3f1ca7ae0434abd74362db7690e4) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '0aa3b7562486864443dde6bcdcaa6827') in 0.013349 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Scripts/Weapons/WeaponData.cs
  artifactKey: Guid(de7d0db4680cf4e84bcdfa7b232aaae8) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Weapons/WeaponData.cs using Guid(de7d0db4680cf4e84bcdfa7b232aaae8) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'd3f4a7fca48aa74a9e335f92c48379d0') in 0.011032 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000159 seconds.
  path: Assets/Scripts/Cards/CardPool.cs
  artifactKey: Guid(fddcc5940474a417eb17c483676da597) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Cards/CardPool.cs using Guid(fddcc5940474a417eb17c483676da597) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '1bfffdc3ad86d35728c78c0a64e237df') in 0.008811 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000496 seconds.
  path: Assets/Scripts/Core/InputManager.cs
  artifactKey: Guid(4e194e1fcdd1a483f9334b3e3fe482dd) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Core/InputManager.cs using Guid(4e194e1fcdd1a483f9334b3e3fe482dd) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'e24be5112e4f5293c231dc3757d1929c') in 0.008324 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000113 seconds.
  path: Assets/Scripts/Enemies/EnemyHealth.cs
  artifactKey: Guid(c40bde37cc5894266928b8d3219f3263) Importer(**********,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Enemies/EnemyHealth.cs using Guid(c40bde37cc5894266928b8d3219f3263) Importer(**********,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'aa793d89c8611604a6a2f0c44e46bd4c') in 0.013862 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.557 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.048 seconds
Domain Reload Profiling: 4605ms
	BeginReloadAssembly (2473ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (1790ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (280ms)
	RebuildCommonClasses (89ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (901ms)
		LoadAssemblies (1167ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (61ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1049ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (709ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (481ms)
			ProcessInitializeOnLoadMethodAttributes (104ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 5.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6244.
Memory consumption went from 216.6 MB to 215.8 MB.
Total: 9.892326 ms (FindLiveObjects: 1.988234 ms CreateObjectMapping: 0.672618 ms MarkObjects: 6.460455 ms  DeleteObjects: 0.769751 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.835 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.610 seconds
Domain Reload Profiling: 6449ms
	BeginReloadAssembly (744ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (66ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (227ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (1966ms)
		LoadAssemblies (1415ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (696ms)
			TypeCache.Refresh (415ms)
				TypeCache.ScanAssembly (9ms)
			ScanForSourceGeneratedMonoScriptInfo (220ms)
			ResolveRequiredComponents (56ms)
	FinalizeReload (3611ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1266ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (10ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (137ms)
			ProcessInitializeOnLoadAttributes (941ms)
			ProcessInitializeOnLoadMethodAttributes (157ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 7.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6249.
Memory consumption went from 219.9 MB to 219.1 MB.
Total: 54.470964 ms (FindLiveObjects: 2.156558 ms CreateObjectMapping: 0.450386 ms MarkObjects: 50.203939 ms  DeleteObjects: 1.658346 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.359 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.635 seconds
Domain Reload Profiling: 5003ms
	BeginReloadAssembly (409ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (152ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (93ms)
	LoadAllAssembliesAndSetupDomain (795ms)
		LoadAssemblies (857ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (78ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (3637ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1465ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (13ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (188ms)
			ProcessInitializeOnLoadAttributes (982ms)
			ProcessInitializeOnLoadMethodAttributes (263ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 9.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6254.
Memory consumption went from 225.0 MB to 224.2 MB.
Total: 15.266146 ms (FindLiveObjects: 0.965983 ms CreateObjectMapping: 1.070103 ms MarkObjects: 11.678264 ms  DeleteObjects: 1.549659 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.708 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.974 seconds
Domain Reload Profiling: 5690ms
	BeginReloadAssembly (549ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (140ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (1038ms)
		LoadAssemblies (1237ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (91ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (38ms)
	FinalizeReload (3975ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1342ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (16ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (251ms)
			ProcessInitializeOnLoadAttributes (849ms)
			ProcessInitializeOnLoadMethodAttributes (204ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 12.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6259.
Memory consumption went from 229.6 MB to 228.8 MB.
Total: 9.419150 ms (FindLiveObjects: 1.194298 ms CreateObjectMapping: 0.617661 ms MarkObjects: 6.887325 ms  DeleteObjects: 0.718520 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.491 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.965 seconds
Domain Reload Profiling: 6463ms
	BeginReloadAssembly (836ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (75ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (311ms)
	RebuildCommonClasses (122ms)
	RebuildNativeTypeToScriptingClass (40ms)
	initialDomainReloadingComplete (106ms)
	LoadAllAssembliesAndSetupDomain (1393ms)
		LoadAssemblies (1325ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (417ms)
			TypeCache.Refresh (127ms)
				TypeCache.ScanAssembly (6ms)
			ScanForSourceGeneratedMonoScriptInfo (172ms)
			ResolveRequiredComponents (113ms)
	FinalizeReload (3965ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1610ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (34ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (270ms)
			ProcessInitializeOnLoadAttributes (1014ms)
			ProcessInitializeOnLoadMethodAttributes (271ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 50.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6264.
Memory consumption went from 234.6 MB to 233.8 MB.
Total: 59.963179 ms (FindLiveObjects: 4.520308 ms CreateObjectMapping: 1.115728 ms MarkObjects: 53.352231 ms  DeleteObjects: 0.972829 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.014 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.707 seconds
Domain Reload Profiling: 5727ms
	BeginReloadAssembly (909ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (48ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (206ms)
	RebuildCommonClasses (83ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (944ms)
		LoadAssemblies (1421ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (101ms)
			TypeCache.Refresh (62ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (3709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1304ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (12ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (194ms)
			ProcessInitializeOnLoadAttributes (832ms)
			ProcessInitializeOnLoadMethodAttributes (241ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 8.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6269.
Memory consumption went from 239.6 MB to 238.8 MB.
Total: 9.589948 ms (FindLiveObjects: 0.811516 ms CreateObjectMapping: 0.658516 ms MarkObjects: 7.063800 ms  DeleteObjects: 1.054141 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.602 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.622 seconds
Domain Reload Profiling: 8236ms
	BeginReloadAssembly (391ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (41ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (173ms)
	RebuildNativeTypeToScriptingClass (49ms)
	initialDomainReloadingComplete (167ms)
	LoadAllAssembliesAndSetupDomain (826ms)
		LoadAssemblies (891ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (113ms)
			TypeCache.Refresh (62ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (2ms)
			ResolveRequiredComponents (45ms)
	FinalizeReload (6631ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3116ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (19ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (239ms)
			ProcessInitializeOnLoadAttributes (1983ms)
			ProcessInitializeOnLoadMethodAttributes (841ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (124ms)
Refreshing native plugins compatible for Editor in 55.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6274.
Memory consumption went from 244.6 MB to 243.8 MB.
Total: 63.721935 ms (FindLiveObjects: 3.474362 ms CreateObjectMapping: 1.429851 ms MarkObjects: 48.912750 ms  DeleteObjects: 9.901725 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
