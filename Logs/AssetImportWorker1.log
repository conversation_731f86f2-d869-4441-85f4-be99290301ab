Tuanjie Editor version:  2022.3.55t3 (60da82e5d6ab)
Branch:                  tuanjie/1.5/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4.1 (Build 24E263)
Darwin version:          24.4.0
Architecture:            x86_64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MacOS/Tuanjie
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
-logFile
Logs/AssetImportWorker1.log
-srvPort
61196
Successfully changed project path to: /Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
/Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [140704323959744]  Target information:

Player connection [140704323959744]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2126289767 [EditorId] 2126289767 [Version] 1048832 [Id] OSXEditor(0,huangcongqiangdeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [140704323959744] Host joined multi-casting on [***********:54997]...
Player connection [140704323959744] Host joined alternative multi-casting on [***********:34997]...
AS: AutoStreaming module initializing.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Refreshing native plugins compatible for Editor in 53.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.55t3 (60da82e5d6ab)
[Subsystems] Discovering subsystems at path /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Color LCD preferred device: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Metal devices available: 1
0: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Using device Intel(R) Iris(TM) Plus Graphics 655 (low power)
Initializing Metal device caps: Intel(R) Iris(TM) Plus Graphics 655
Initialize mono
Mono path[0] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed'
Mono path[1] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56119
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Registered in 0.014967 seconds.
- Loaded All Assemblies, in  1.011 seconds
