Tuanjie Editor version:  2022.3.55t3 (60da82e5d6ab)
Branch:                  tuanjie/1.5/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4.1 (Build 24E263)
Darwin version:          24.4.0
Architecture:            x86_64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MacOS/Tuanjie
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
-logFile
Logs/AssetImportWorker0.log
-srvPort
61196
Successfully changed project path to: /Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
/Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [140704323959744]  Target information:

Player connection [140704323959744]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3211658365 [EditorId] 3211658365 [Version] 1048832 [Id] OSXEditor(0,huangcongqiangdeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [140704323959744] Host joined multi-casting on [***********:54997]...
Player connection [140704323959744] Host joined alternative multi-casting on [***********:34997]...
AS: AutoStreaming module initializing.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Refreshing native plugins compatible for Editor in 53.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.55t3 (60da82e5d6ab)
[Subsystems] Discovering subsystems at path /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Desktop/product/games/RoguelikeGame/RoguelikeGame/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Color LCD preferred device: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Metal devices available: 1
0: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Using device Intel(R) Iris(TM) Plus Graphics 655 (low power)
Initializing Metal device caps: Intel(R) Iris(TM) Plus Graphics 655
Initialize mono
Mono path[0] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Managed'
Mono path[1] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56117
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Registered in 0.015064 seconds.
- Loaded All Assemblies, in  1.010 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.864 seconds
Domain Reload Profiling: 1873ms
	BeginReloadAssembly (402ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (83ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (114ms)
	LoadAllAssembliesAndSetupDomain (388ms)
		LoadAssemblies (408ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (378ms)
			TypeCache.Refresh (373ms)
				TypeCache.ScanAssembly (304ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (2ms)
	FinalizeReload (864ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (679ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (20ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (3ms)
			ProcessInitializeOnLoadAttributes (598ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.875 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.787 seconds
Domain Reload Profiling: 3665ms
	BeginReloadAssembly (395ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (99ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (100ms)
	LoadAllAssembliesAndSetupDomain (1249ms)
		LoadAssemblies (895ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (546ms)
			TypeCache.Refresh (450ms)
				TypeCache.ScanAssembly (404ms)
			ScanForSourceGeneratedMonoScriptInfo (65ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1788ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1357ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (13ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (184ms)
			ProcessInitializeOnLoadAttributes (900ms)
			ProcessInitializeOnLoadMethodAttributes (244ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Launching external process: /Applications/Tuanjie/Hub/Editor/2022.3.55t3/Tuanjie.app/Contents/Tools/TuanjieShaderCompiler
Launched and connected shader compiler TuanjieShaderCompiler after 0.87 seconds
Refreshing native plugins compatible for Editor in 7.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5772 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (0.8 MB). Loaded Objects now: 6238.
Memory consumption went from 233.6 MB to 232.8 MB.
Total: 18.844556 ms (FindLiveObjects: 1.634482 ms CreateObjectMapping: 1.337587 ms MarkObjects: 11.658617 ms  DeleteObjects: 4.211461 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 536343.453902 seconds.
  path: Assets/Scripts/Cards/CardSystem.cs
  artifactKey: Guid(a6316de2c725e40acb0486fefc14ab27) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Cards/CardSystem.cs using Guid(a6316de2c725e40acb0486fefc14ab27) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'd7b565f2f5e70a0324a8b84fd61fcde7') in 0.488954 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.001419 seconds.
  path: Assets/Scripts/Utils/GameTester.cs
  artifactKey: Guid(ea2d01cbf76924a90990206bd22fbd35) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Utils/GameTester.cs using Guid(ea2d01cbf76924a90990206bd22fbd35) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '027d5f550387db63976a3a3ef7a09d1a') in 0.006844 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000588 seconds.
  path: Assets/Scripts/UI/MainMenuUI.cs
  artifactKey: Guid(fecd3c76b924b41cb9d32dbc51709d64) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/UI/MainMenuUI.cs using Guid(fecd3c76b924b41cb9d32dbc51709d64) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '6a1227e423e6ce50564e6a6d07dbfa6f') in 0.011384 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Scripts/Utils/ObjectPool.cs
  artifactKey: Guid(2d426e761212940319fa8c50dbfe6c87) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Utils/ObjectPool.cs using Guid(2d426e761212940319fa8c50dbfe6c87) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '83ce82d61ae27eaf8ae6f8636a76c3a2') in 0.006934 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Settings/Lit2DSceneTemplate.scenetemplate
  artifactKey: Guid(d03ed43fc9d8a4f2e9fa70c1c7916eb9) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Settings/Lit2DSceneTemplate.scenetemplate using Guid(d03ed43fc9d8a4f2e9fa70c1c7916eb9) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '25ad0f9e8cbbe6996ff90fd144f62b74') in 0.138774 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 4
========================================================================
Received Import Request.
  Time since last request: 0.000088 seconds.
  path: Assets/Scripts/Characters/CharacterData.cs
  artifactKey: Guid(17ca46245291e4853a38db2d10a090cf) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Characters/CharacterData.cs using Guid(17ca46245291e4853a38db2d10a090cf) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'b38c9f2f91266a8075452e19248ebd12') in 0.008116 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000191 seconds.
  path: Assets/Scripts/UI/CardDisplay.cs
  artifactKey: Guid(7cf3b2cfc6db3466f84acde9058b72ff) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/UI/CardDisplay.cs using Guid(7cf3b2cfc6db3466f84acde9058b72ff) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '9aacf3dc172ef5b9477afe9da5dedf48') in 0.007631 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Scripts/Characters/PlayerController.cs
  artifactKey: Guid(66c8635f6437941c1a32f1425eb66500) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Characters/PlayerController.cs using Guid(66c8635f6437941c1a32f1425eb66500) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '75f75cd28d23cf1ddd887cdb070ff07b') in 0.010765 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000152 seconds.
  path: Assets/Scripts/Core/GameLauncher.cs
  artifactKey: Guid(f77f28ecb6f9f4e698b0387c112a4eda) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Core/GameLauncher.cs using Guid(f77f28ecb6f9f4e698b0387c112a4eda) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '42fe01853cddf7623d672bbb5ab8c0cb') in 0.009259 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000135 seconds.
  path: Assets/Scenes/SampleScene.scene
  artifactKey: Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scenes/SampleScene.scene using Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'ef6898c1781650eb701be4561c68df42') in 0.008521 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000091 seconds.
  path: Assets/Scripts/Characters/ExperienceSystem.cs
  artifactKey: Guid(e5d7ff9e093a345feb559eed3c6400e3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Characters/ExperienceSystem.cs using Guid(e5d7ff9e093a345feb559eed3c6400e3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'cc18d642fe510bd41a55797f79ab515a') in 0.011312 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000096 seconds.
  path: Assets/Scripts/Core/UIManager.cs
  artifactKey: Guid(8596e59025a1d42eb914782b2e7f9d08) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Core/UIManager.cs using Guid(8596e59025a1d42eb914782b2e7f9d08) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '2613ebc277c917eb58b0cb8cefcbf716') in 0.010913 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000773 seconds.
  path: Assets/Settings/Scenes/URP2DSceneTemplate.scene
  artifactKey: Guid(2cda990e2423bbf4892e6590ba056729) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Settings/Scenes/URP2DSceneTemplate.scene using Guid(2cda990e2423bbf4892e6590ba056729) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'b3069f9e48c7d978355d9bfe33eb4618') in 0.012862 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Scripts/Enemies/EnemyController.cs
  artifactKey: Guid(50d9fb14db9594bfb957860b406a5dc5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Enemies/EnemyController.cs using Guid(50d9fb14db9594bfb957860b406a5dc5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'd771008e5b9873f11df4839968674163') in 0.015921 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.001167 seconds.
  path: Assets/Data/Examples/ExampleWeaponData.md
  artifactKey: Guid(e68b6be3643264ec4bdba48ebad39586) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Data/Examples/ExampleWeaponData.md using Guid(e68b6be3643264ec4bdba48ebad39586) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'a3ab2947b08ad56a807404fdf58670ad') in 0.014113 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.001403 seconds.
  path: Assets/Scripts/UI/SkillDisplay.cs
  artifactKey: Guid(70160e77545e14250a62f676be6fcd63) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/UI/SkillDisplay.cs using Guid(70160e77545e14250a62f676be6fcd63) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '2d423d5cea25b0ae8e26a79af8854047') in 0.009759 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000105 seconds.
  path: Assets/Scripts/Abilities/AbilitySystem.cs
  artifactKey: Guid(a38bce54037504220b6d4b6f4f874064) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Abilities/AbilitySystem.cs using Guid(a38bce54037504220b6d4b6f4f874064) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'f8f53ef12843c918fcbe3b99c3efbbfa') in 0.011003 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000113 seconds.
  path: Assets/Scripts/Core/SceneManager.cs
  artifactKey: Guid(d827b490c9de142d8b91306d2a86f303) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Core/SceneManager.cs using Guid(d827b490c9de142d8b91306d2a86f303) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '8af34649ac69a02b7e6c78ac815af475') in 0.009246 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Scripts/Levels/LevelManager.cs
  artifactKey: Guid(5c0c574a6eaa1455881b5bcfbacc459f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Levels/LevelManager.cs using Guid(5c0c574a6eaa1455881b5bcfbacc459f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '78140fcec10371f7455cb24c1a722adb') in 0.009052 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000105 seconds.
  path: Assets/Scripts/UI/GameplayUI.cs
  artifactKey: Guid(bd112aab339b746d896df1346d5d5d8f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/UI/GameplayUI.cs using Guid(bd112aab339b746d896df1346d5d5d8f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '78a1aa1dc73daa6aece43685164b03da') in 0.008604 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Scripts/Core/GameManager.cs
  artifactKey: Guid(5035202cd074141f2826b911051fdd33) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Core/GameManager.cs using Guid(5035202cd074141f2826b911051fdd33) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'e12076241b689769f9ec37483c4c060c') in 0.011377 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000089 seconds.
  path: Assets/Scripts/Abilities/AbilityData.cs
  artifactKey: Guid(7c0a7e4b2a0b6449e9774b82d9dbaf22) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Scripts/Abilities/AbilityData.cs using Guid(7c0a7e4b2a0b6449e9774b82d9dbaf22) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '7beafd2ea7a2727374cafee0a7937fe5') in 0.013740 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.568 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.047 seconds
Domain Reload Profiling: 4615ms
	BeginReloadAssembly (2481ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (1816ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (272ms)
	RebuildCommonClasses (89ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (67ms)
	LoadAllAssembliesAndSetupDomain (903ms)
		LoadAssemblies (1171ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (56ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1048ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (712ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (106ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 5.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6244.
Memory consumption went from 216.4 MB to 215.7 MB.
Total: 10.444035 ms (FindLiveObjects: 2.196664 ms CreateObjectMapping: 0.735396 ms MarkObjects: 6.904949 ms  DeleteObjects: 0.605777 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.827 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.620 seconds
Domain Reload Profiling: 6450ms
	BeginReloadAssembly (734ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (71ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (212ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (1963ms)
		LoadAssemblies (1667ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (528ms)
			TypeCache.Refresh (263ms)
				TypeCache.ScanAssembly (11ms)
			ScanForSourceGeneratedMonoScriptInfo (207ms)
			ResolveRequiredComponents (51ms)
	FinalizeReload (3620ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (26ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (123ms)
			ProcessInitializeOnLoadAttributes (938ms)
			ProcessInitializeOnLoadMethodAttributes (165ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 7.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6249.
Memory consumption went from 219.8 MB to 219.0 MB.
Total: 55.383245 ms (FindLiveObjects: 2.233125 ms CreateObjectMapping: 0.444531 ms MarkObjects: 51.570258 ms  DeleteObjects: 1.133205 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.363 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.632 seconds
Domain Reload Profiling: 4999ms
	BeginReloadAssembly (417ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (51ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (151ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (84ms)
	LoadAllAssembliesAndSetupDomain (796ms)
		LoadAssemblies (871ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (70ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (3634ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1462ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (12ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (191ms)
			ProcessInitializeOnLoadAttributes (980ms)
			ProcessInitializeOnLoadMethodAttributes (256ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 9.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6254.
Memory consumption went from 224.9 MB to 224.1 MB.
Total: 15.061658 ms (FindLiveObjects: 0.926846 ms CreateObjectMapping: 1.197258 ms MarkObjects: 12.012727 ms  DeleteObjects: 0.922483 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.661 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.036 seconds
Domain Reload Profiling: 5702ms
	BeginReloadAssembly (541ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (35ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (120ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (995ms)
		LoadAssemblies (1194ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (75ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (4037ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1377ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (18ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (249ms)
			ProcessInitializeOnLoadAttributes (871ms)
			ProcessInitializeOnLoadMethodAttributes (215ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 12.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6259.
Memory consumption went from 229.4 MB to 228.7 MB.
Total: 7.873466 ms (FindLiveObjects: 0.661033 ms CreateObjectMapping: 0.505519 ms MarkObjects: 6.134776 ms  DeleteObjects: 0.570824 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.484 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.972 seconds
Domain Reload Profiling: 6458ms
	BeginReloadAssembly (795ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (72ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (310ms)
	RebuildCommonClasses (81ms)
	RebuildNativeTypeToScriptingClass (37ms)
	initialDomainReloadingComplete (124ms)
	LoadAllAssembliesAndSetupDomain (1449ms)
		LoadAssemblies (1375ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (392ms)
			TypeCache.Refresh (98ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (165ms)
			ResolveRequiredComponents (122ms)
	FinalizeReload (3972ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1605ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (31ms)
			SetLoadedEditorAssemblies (49ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (247ms)
			ProcessInitializeOnLoadAttributes (1002ms)
			ProcessInitializeOnLoadMethodAttributes (275ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 41.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6264.
Memory consumption went from 234.5 MB to 233.7 MB.
Total: 54.165376 ms (FindLiveObjects: 1.770639 ms CreateObjectMapping: 0.844039 ms MarkObjects: 50.604139 ms  DeleteObjects: 0.944547 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.987 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.777 seconds
Domain Reload Profiling: 5769ms
	BeginReloadAssembly (915ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (52ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (207ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (920ms)
		LoadAssemblies (1405ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (56ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (45ms)
	FinalizeReload (3779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1332ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (15ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (191ms)
			ProcessInitializeOnLoadAttributes (828ms)
			ProcessInitializeOnLoadMethodAttributes (269ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 9.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6269.
Memory consumption went from 239.5 MB to 238.7 MB.
Total: 9.359390 ms (FindLiveObjects: 0.779137 ms CreateObjectMapping: 0.632949 ms MarkObjects: 7.235579 ms  DeleteObjects: 0.710259 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.629 seconds
Native extension for WeixinMiniGame target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.795 seconds
Domain Reload Profiling: 8432ms
	BeginReloadAssembly (411ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (123ms)
	RebuildCommonClasses (178ms)
	RebuildNativeTypeToScriptingClass (121ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (831ms)
		LoadAssemblies (888ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (128ms)
			TypeCache.Refresh (76ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (48ms)
	FinalizeReload (6802ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3290ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (20ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (241ms)
			ProcessInitializeOnLoadAttributes (2127ms)
			ProcessInitializeOnLoadMethodAttributes (831ms)
			AfterProcessingInitializeOnLoad (36ms)
			EditorAssembliesLoaded (13ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (168ms)
Refreshing native plugins compatible for Editor in 55.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5634 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (0.8 MB). Loaded Objects now: 6274.
Memory consumption went from 244.5 MB to 243.7 MB.
Total: 67.202207 ms (FindLiveObjects: 1.997107 ms CreateObjectMapping: 1.367671 ms MarkObjects: 61.290016 ms  DeleteObjects: 2.542670 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
